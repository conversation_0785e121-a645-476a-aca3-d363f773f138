package com.bytecat.algui.AlguiHacker;
import android.os.RemoteException;
import com.bytecat.algui.AlguiManager.AlguiLog;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/25 23:01
 * @Describe AlguiJava内存工具
 */
public class AlguiMemTool {

    public static final String TAG = "AlguiMemTool";

    // 内存区域
    public static final int RANGE_ALL = 0;            // 所有内存区域  -  全部内存
    public static final int RANGE_JAVA_HEAP = 2;      // Java 虚拟机堆内存  -  jh内存
    public static final int RANGE_C_HEAP = 1;         // C++ 堆内存  -  ch内存
    public static final int RANGE_C_ALLOC = 4;        // C++ 分配的内存  -  ca内存 [已适配 android11+ scudo内存分配器]
    public static final int RANGE_C_DATA = 8;         // C++ 的数据段  -  cd内存
    public static final int RANGE_C_BSS = 16;         // C++ 未初始化的数据  -  cb内存
    public static final int RANGE_ANONYMOUS = 32;     // 匿名内存区域  -  a内存
    public static final int RANGE_JAVA = 65536;       // Java 虚拟机内存 - j内存
    public static final int RANGE_STACK = 64;         // 栈内存区域  -  s内存
    public static final int RANGE_ASHMEM = 524288;    // Android 共享内存  -  as内存
    public static final int RANGE_VIDEO = 1048576;    // 视频内存区域  -  v内存
    public static final int RANGE_OTHER = -2080896;   // 其他内存区域  -  o内存
    public static final int RANGE_B_BAD = 131072;     // 错误的内存区域  -  b内存
    public static final int RANGE_CODE_APP = 16384;   // 应用程序代码区域  -  xa内存
    public static final int RANGE_CODE_SYSTEM = 32768;// 系统代码区域  -  xs内存
    // 基址头
    public static final int HEAD_XA = 0;          //xa基址头
    public static final int HEAD_CD = 1;          //cd基址头
    public static final int HEAD_CB = 2;          //cb基址头 [bss]
    // 数据类型
    public static final int TYPE_DWORD = 4;       // DWORD 类型  -  D类型
    public static final int TYPE_FLOAT = 16;      // FLOAT 类型  -  F类型
    public static final int TYPE_DOUBLE = 64;     // DOUBLE 类型  -  E类型
    public static final int TYPE_WORD = 2;        // WORD 类型  -  W类型
    public static final int TYPE_BYTE = 1;        // BYTE 类型  -  B类型
    public static final int TYPE_QWORD = 32;
    public static final int TYPE_STRING = 666;

    public static String getMemoryAddrValue(long i, int tYPE_BYTE) {
        return null;
    }

    public static void MemorySearch(String searchHex, int tYPE_BYTE, int p2, int p3, int p4) {
    }      // QWORD 类型  -  Q类型

    //初始化



    //设置目标包名 【注意：这是初始化函数，不调用此函数的话其它内存操作均失效】 
    public static int setPackageName(String packageName) {
        int i = -1;
        if (packageName != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    i = AlguiRootService.getRootList().setPackageName(packageName);
                } catch (RemoteException e) {
                    i = AlguiNativeMemTool.setPackageName(packageName);
                }
            } else {
                i = AlguiNativeMemTool.setPackageName(packageName);
            }
        }
        return i;
    }
    //获取进程ID
    public static int getPID(String packageName) {
        int pid = -1;
        if (packageName != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    pid = AlguiRootService.getRootList().getPID(packageName);
                } catch (RemoteException e) {
                    pid = AlguiNativeMemTool.getPID(packageName);
                }
            } else {
                pid = AlguiNativeMemTool.getPID(packageName);
            }
        }
        return pid;
    }
    //设置安全写入启用状态
    public static void setIsSecureWrites(boolean sw) {
        if (AlguiRootService.isConnect()) {
            try {
                AlguiRootService.getRootList().setIsSecureWrites(sw);
            } catch (RemoteException e) {
                AlguiNativeMemTool.setIsSecureWrites(sw);
            }
        } else {
            AlguiNativeMemTool.setIsSecureWrites(sw);
        }
    }







    //模块[动/静]态基址偏移内存修改


    //获取模块起始地址(基址) 
    public static long getModuleBaseAddr(String moduleName, int headType) {
        long addr = -1;
        if (moduleName != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    addr = AlguiRootService.getRootList().getModuleBaseAddr(moduleName, headType);
                } catch (RemoteException e) {
                    addr = AlguiNativeMemTool.getModuleBaseAddr(moduleName, headType);
                }
            } else {
                addr = AlguiNativeMemTool.getModuleBaseAddr(moduleName, headType);
            }
        }
        return addr;
    }
    //跳转指针
    public static long jump(long addr, int count) {
        long result = -1;

        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().jump(addr, count);
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.jump(addr, count);
            }
        } else {
            result = AlguiNativeMemTool.jump(addr, count);
        }

        return result;
    }

    //跳转指针32位
    public static long jump32(long addr) {
        long result = -1;

        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().jump32(addr);
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.jump32(addr);
            }
        } else {
            result = AlguiNativeMemTool.jump32(addr);
        }

        return result;
    }
    //跳转指针64位
    public static long jump64(long addr) {
        long result = -1;

        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().jump64(addr);
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.jump64(addr);
            }
        } else {
            result = AlguiNativeMemTool.jump64(addr);
        }

        return result;
    }
    //设置指定内存地址指向的值
    public static int setMemoryAddrValue(String value, long addr, int type, boolean isFree) {
        int result = -1;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().setMemoryAddrValue(value, addr, type, isFree);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.setMemoryAddrValue(value, addr, type, isFree);
                }
            } else {
                result = AlguiNativeMemTool.setMemoryAddrValue(value, addr, type, isFree);
            }
        }
        return result;
    }
    //获取指定内存地址的数据
    public static String getMemoryAddrData(long addr, int type) {
        String result = null;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().getMemoryAddrData(addr, type);
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.getMemoryAddrData(addr, type);
            }
        } else {
            result = AlguiNativeMemTool.getMemoryAddrData(addr, type);
        }

        return result;
    }




    //内存搜索


    // 设置要搜索的内存区域
    public static void setMemoryArea(int memoryArea) {

        if (AlguiRootService.isConnect()) {
            try {
                AlguiRootService.getRootList().setMemoryArea(memoryArea);
            } catch (RemoteException e) {
                AlguiNativeMemTool.setMemoryArea(memoryArea);
            }
        } else {
            AlguiNativeMemTool.setMemoryArea(memoryArea);
        }
    }

    // 内存搜索 【支持范围搜索 格式：最小值~最大值】
    public static long[] MemorySearch(String value, int type) {
        long[] result = null;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().MemorySearch(value, type);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.MemorySearch(value, type);
                }
            } else {
                result = AlguiNativeMemTool.MemorySearch(value, type);
            }
        }
        return result;
    }

    // 内存搜索范围值 【格式：最小值~最大值】
    public static long[] MemorySearchRange(String value, int type) {
        long[] result = null;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().MemorySearchRange(value, type);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.MemorySearchRange(value, type);
                }
            } else {
                result = AlguiNativeMemTool.MemorySearchRange(value, type);
            }
        }

        return result;
    }

    // 联合内存搜索 【格式：值1;值2;值3;n个值:附近范围 示例：2D;3F;4E:50 或 2D;3F;4E没有范围则使用默认范围,两个范围符::代表按顺序搜索】
    public static long[] MemorySearchUnited(String value, int type) {
        long[] result = null;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().MemorySearchUnited(value, type);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.MemorySearchUnited(value, type);
                }
            } else {
                result = AlguiNativeMemTool.MemorySearchUnited(value, type);
            }
        }
        return result;
    }

    // 偏移改善 [筛选偏移处特征值 支持联合改善，范围改善]
    public static long[] ImproveOffset(String value, int type, long offset) {
        long[] result = null;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().ImproveOffset(value, type, offset);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.ImproveOffset(value, type, offset);
                }
            } else {
                result = AlguiNativeMemTool.ImproveOffset(value, type, offset);
            }
        }
        return result;
    }

    // 偏移范围改善 [筛选偏移处只会在一个范围内变化的特征值] 【格式：最小值~最大值】
    public static long[] ImproveOffsetRange(String value, int type, long offset) {
        long[] result = null;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().ImproveOffsetRange(value, type, offset);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.ImproveOffsetRange(value, type, offset);
                }
            } else {
                result = AlguiNativeMemTool.ImproveOffsetRange(value, type, offset);
            }
        }
        return result;
    }

    // 偏移联合改善 [筛选偏移处永远只会为某些值的特征值] 【格式：值1;值2;n个值】
    public static long[] ImproveOffsetUnited(String value, int type, long offset) {
        long[] result = null;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().ImproveOffsetUnited(value, type, offset);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.ImproveOffsetUnited(value, type, offset);
                }
            } else {
                result = AlguiNativeMemTool.ImproveOffsetUnited(value, type, offset);
            }
        }
        return result;
    }

    // 改善 [支持联合改善，范围改善]
    public static long[] ImproveValue(String value, int type) {
        long[] result = null;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().ImproveValue(value, type);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.ImproveValue(value, type);
                }
            } else {
                result = AlguiNativeMemTool.ImproveValue(value, type);
            }
        }
        return result;
    }

    // 结果偏移写入数据 【已对当前进程进行保护】
    public static int MemoryOffsetWrite(String value, int type, long offset, boolean isFree) {
        int result = -1;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().MemoryOffsetWrite(value, type, offset, isFree);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.MemoryOffsetWrite(value, type, offset, isFree);
                }
            } else {
                result = AlguiNativeMemTool.MemoryOffsetWrite(value, type, offset, isFree);
            }
        }
        return result;
    }

    // 获取最终搜索结果数量
    public static int getResultCount() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().getResultCount();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.getResultCount();
            }
        } else {
            result = AlguiNativeMemTool.getResultCount();
        }
        return result;
    }

    // 获取最终搜索结果列表
    public static long[] getResultList() {
        long[] result = null;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().getResultList();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.getResultList();
            }
        } else {
            result = AlguiNativeMemTool.getResultList();
        }
        return result;
    }

    // 打印最终搜索结果列表到指定文件
    public static int printResultListToFile(String filePath) {
        int result = -1;
        if (filePath != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().printResultListToFile(filePath);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.printResultListToFile(filePath);
                }
            } else {
                result = AlguiNativeMemTool.printResultListToFile(filePath);
            }
        }

        return result;
    }

    // 清空最终搜索结果列表
    public static int clearResultList() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().clearResultList();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.clearResultList();
            }
        } else {
            result = AlguiNativeMemTool.clearResultList();
        }
        return result;
    }






    //冻结内存修改

    // 设置冻结延迟时间（毫秒）
    public static void setFreezeDelayMs(int delay) {
        if (AlguiRootService.isConnect()) {
            try {
                AlguiRootService.getRootList().setFreezeDelayMs(delay);
            } catch (RemoteException e) {
                AlguiNativeMemTool.setFreezeDelayMs(delay);
            }
        } else {
            AlguiNativeMemTool.setFreezeDelayMs(delay);
        }
    }

    // 获取冻结项目数量
    public static int getFreezeNum() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().getFreezeNum();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.getFreezeNum();
            }
        } else {
            result = AlguiNativeMemTool.getFreezeNum();
        }
        return result;
    }

    // 添加冻结项目
    public static int addFreezeItem(String value, long addr, int type) {
        int result = -1;
        if (value != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().addFreezeItem(value, addr, type);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.addFreezeItem(value, addr, type);
                }
            } else {
                result = AlguiNativeMemTool.addFreezeItem(value, addr, type);
            }
        }
        return result;
    }

    // 移除指定冻结项目
    public static int removeFreezeItem(long addr) {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().removeFreezeItem(addr);
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.removeFreezeItem(addr);
            }
        } else {
            result = AlguiNativeMemTool.removeFreezeItem(addr);
        }
        return result;
    }

    // 移除所有冻结项目
    public static int removeAllFreezeItem() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().removeAllFreezeItem();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.removeAllFreezeItem();
            }
        } else {
            result = AlguiNativeMemTool.removeAllFreezeItem();
        }
        return result;
    }

    // 启动所有冻结项目
    public static int startAllFreeze() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().startAllFreeze();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.startAllFreeze();
            }
        } else {
            result = AlguiNativeMemTool.startAllFreeze();
        }
        return result;
    }

    // 停止所有冻结项目
    public static int stopAllFreeze() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().stopAllFreeze();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.stopAllFreeze();
            }
        } else {
            result = AlguiNativeMemTool.stopAllFreeze();
        }
        return result;
    }

    // 将冻结项目列表打印到文件
    public static int printFreezeListToFile(String filePath) {
        int result = -1;
        if (filePath != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().printFreezeListToFile(filePath);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.printFreezeListToFile(filePath);
                }
            } else {
                result = AlguiNativeMemTool.printFreezeListToFile(filePath);
            }
        }
        return result;
    }






    //获取内存信息工具


    // 获取指定内存地址的Maps映射行
    public static String getMemoryAddrMapLine(long address) {
        String result = null;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().getMemoryAddrMapLine(address);
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.getMemoryAddrMapLine(address);
            }
        } else {
            result = AlguiNativeMemTool.getMemoryAddrMapLine(address); 
        }

        return result;
    }

    // 获取Maps映射行所在内存区域名称
    public static String getMapLineMemoryAreaName(String mapLine) {
        String result = null;
        if (mapLine != null) { 
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().getMapLineMemoryAreaName(mapLine);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.getMapLineMemoryAreaName(mapLine); 
                }
            } else {
                result = AlguiNativeMemTool.getMapLineMemoryAreaName(mapLine);
            }
        }
        return result;
    }

    // 获取指定内存id的内存名称
    public static String getMemoryAreaIdName(int memid) {
        String result = null;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().getMemoryAreaIdName(memid);
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.getMemoryAreaIdName(memid); 
            }
        } else {
            result = AlguiNativeMemTool.getMemoryAreaIdName(memid); 
        }

        return result;
    }

    // 获取当前内存名称
    public static String getMemoryAreaName() {
        String result = null;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().getMemoryAreaName();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.getMemoryAreaName(); 
            }
        } else {
            result = AlguiNativeMemTool.getMemoryAreaName(); 
        }
        return result;
    }

    // 获取指定数据类型id的数据类型名称
    public static String getDataTypeName(int typeId) {
        String result = null;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().getDataTypeName(typeId);
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.getDataTypeName(typeId); 
            }
        } else {
            result = AlguiNativeMemTool.getDataTypeName(typeId);
        }

        return result;
    }




    //超级用户工具


    // 杀掉指定包名的进程
    public static int killProcess_Root(String packageName) {
        int result = -1;
        if (packageName != null) { 
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().killProcess_Root(packageName);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.killProcess_Root(packageName); 
                }
            } else {
                result = AlguiNativeMemTool.killProcess_Root(packageName); 
            }
        }
        return result;
    }

    // 暂停指定包名的进程
    public static int stopProcess_Root(String packageName) {
        int result = -1;
        if (packageName != null) { 
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().stopProcess_Root(packageName);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.stopProcess_Root(packageName); 
                }
            } else {
                result = AlguiNativeMemTool.stopProcess_Root(packageName);
            }
        }
        return result;
    }

    // 恢复被暂停的指定包名的进程
    public static int resumeProcess_Root(String packageName) {
        int result = -1;
        if (packageName != null) { 
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().resumeProcess_Root(packageName);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.resumeProcess_Root(packageName);
                }
            } else {
                result = AlguiNativeMemTool.resumeProcess_Root(packageName); 
            }
        }
        return result;
    }

    // 杀掉所有inotify监视器
    public static void killAllInotify_Root() {
        if (AlguiRootService.isConnect()) {
            try {
                AlguiRootService.getRootList().killAllInotify_Root();
            } catch (RemoteException e) {
                AlguiNativeMemTool.killAllInotify_Root(); 
            }
        } else {
            AlguiNativeMemTool.killAllInotify_Root(); 
        }
    }

    // 杀掉GG修改器
    public static int killGG_Root() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().killGG_Root();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.killGG_Root(); 
            }
        } else {
            result = AlguiNativeMemTool.killGG_Root(); 
        }
        return result;
    }

    // 杀掉XS脚本
    public static int killXscript_Root() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().killXscript_Root();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.killXscript_Root(); 
            }
        } else {
            result = AlguiNativeMemTool.killXscript_Root(); 
        }
        return result;
    }

    // 重启手机
    public static int rebootsystem_Root() {
        int result = -1;
        if (AlguiRootService.isConnect()) {
            try {
                result = AlguiRootService.getRootList().rebootsystem_Root();
            } catch (RemoteException e) {
                result = AlguiNativeMemTool.rebootsystem_Root(); 
            }
        } else {
            result = AlguiNativeMemTool.rebootsystem_Root(); 
        }
        return result;
    }

    // 静默安装指定路径的APK安装包
    public static int installapk_Root(String apkPackagePath) {
        int result = -1;
        if (apkPackagePath != null) {
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().installapk_Root(apkPackagePath);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.installapk_Root(apkPackagePath); 
                }
            } else {
                result = AlguiNativeMemTool.installapk_Root(apkPackagePath); 
            }
        }
        return result;
    }

    // 静默卸载指定包名的APK软件
    public static int uninstallapk_Root(String packageName) {
        int result = -1;
        if (packageName != null) { 
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().uninstallapk_Root(packageName);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.uninstallapk_Root(packageName); 
                }
            } else {
                result = AlguiNativeMemTool.uninstallapk_Root(packageName); 
            }
        }
        return result;
    }

    // 执行命令
    public static int Cmd(String command) {
        int result = -1;
        if (command != null) { 
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().Cmd(command);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.Cmd(command); 
                }
            } else {
                result = AlguiNativeMemTool.Cmd(command); 
            }
        }
        return result;
    }

    // 执行超级命令
    public static int Cmd_Root(String command) {
        int result = -1;
        if (command != null) { 
            if (AlguiRootService.isConnect()) {
                try {
                    result = AlguiRootService.getRootList().Cmd_Root(command);
                } catch (RemoteException e) {
                    result = AlguiNativeMemTool.Cmd_Root(command); 
                }
            } else {
                result = AlguiNativeMemTool.Cmd_Root(command); 
            }
        }
        return result;
    }

    //执行JNI层的功能函数 参数：功能ID，该功能开关状态，修改值(可选 对于在拖动条或输入框自定义修改值)
    public static void JniSwitch(int id, boolean isSwitch, String value) {
        if(value==null)value="0";
        if (AlguiRootService.isConnect()) {
            try {
                AlguiRootService.getRootList().JniSwitch(id, isSwitch, value);
            } catch (RemoteException e) {
                AlguiNativeMemTool.JniSwitch(id, isSwitch, value);
            }
        } else {
            AlguiNativeMemTool.JniSwitch(id, isSwitch, value);
        }
    }

    //HOOK
    ///Java传递调用JNI对应HOOK功能 (不支持root)
    public static void JniHook(int id, boolean isSwitch, double value) {
        AlguiNativeMemTool.JniHook(id, isSwitch, value);
    }


}
