# SSL证书问题修复说明

## 问题描述
网络图片加载时出现SSL证书验证失败的错误：
```
Hostname a.tuchuangyun.top not verified:
certificate: sha1/GENVowC7H6MetPeV3Mgn7eAF6rg=
DN: CN=acefitness.site
subjectAltNames: [acefitness.site]
```

## 问题原因分析

### 1. SSL证书不匹配
- **问题**: 图片URL的域名证书与访问域名不匹配
- **影响**: 导致HTTPS连接失败，图片无法加载
- **原因**: 图片托管服务的SSL证书配置问题

### 2. 网络安全策略限制
- **问题**: Android 7.0+ 默认不允许不安全的网络连接
- **影响**: 即使忽略SSL证书，也可能被系统阻止
- **位置**: 网络安全配置

## 解决方案

### 1. 添加SSL证书信任配置
```java
// 在AlguiToolImage.java中添加SSL处理
if (connection instanceof HttpsURLConnection) {
    HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
    // 创建信任所有证书的SSLContext
    SSLContext sslContext = SSLContext.getInstance("TLS");
    sslContext.init(null, new TrustManager[]{new X509TrustManager() {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
    }}, new SecureRandom());
    
    // 创建不验证主机名的HostnameVerifier
    HostnameVerifier hostnameVerifier = new HostnameVerifier() {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true; // 信任所有主机名
        }
    };
    
    httpsConnection.setSSLSocketFactory(sslContext.getSocketFactory());
    httpsConnection.setHostnameVerifier(hostnameVerifier);
}
```

### 2. 创建网络安全配置文件
```xml
<!-- app/src/main/res/xml/network_security_config.xml -->
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">tc.z.wiki</domain>
        <domain includeSubdomains="true">a.tuchuangyun.top</domain>
        <domain includeSubdomains="true">acefitness.site</domain>
        <domain includeSubdomains="true">httpbin.org</domain>
    </domain-config>
    
    <!-- 允许所有不安全的网络连接（仅用于开发测试） -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
</network-security-config>
```

### 3. 更新AndroidManifest.xml
```xml
<application
    android:networkSecurityConfig="@xml/network_security_config"
    android:usesCleartextTraffic="true">
```

### 4. 创建备用图片加载方法
```java
/**
 * 获取网络图片（带SSL证书处理）
 * 如果SSL证书验证失败，会尝试使用备用方法
 */
public static void getImageURLWithSSLHandling(final String url, final AlguiCallback.Web callback) {
    if (url != null) {
        AlguiLog.d(TAG, "开始加载网络图片（带SSL处理）: " + url);
        
        new AsyncTask<Void, Void, Drawable>() {
            @Override
            protected Drawable doInBackground(Void... voids) {
                // 首先尝试正常加载
                Drawable result = loadImageWithSSL(url);
                if (result != null) {
                    return result;
                }
                
                // 如果失败，尝试使用HTTP（如果原URL是HTTPS）
                if (url.startsWith("https://")) {
                    String httpUrl = url.replace("https://", "http://");
                    AlguiLog.d(TAG, "SSL加载失败，尝试HTTP: " + httpUrl);
                    return loadImageWithSSL(httpUrl);
                }
                
                return null;
            }

            @Override
            protected void onPostExecute(Drawable result) {
                if (callback != null) {
                    Message msg = new Message();
                    if (result != null) {
                        msg.what = 200;
                        msg.obj = result;
                        AlguiLog.d(TAG, "图片加载完成，发送成功消息");
                    } else {
                        msg.what = 404;
                        AlguiLog.e(TAG, "图片加载失败，发送错误消息");
                    }
                    callback.web(msg);
                }
            }
        }.execute();
    }
}
```

### 5. 使用本地图片作为备用方案
```java
// 在悬浮窗中使用本地图片替代网络图片
menu.setCatMenuBackImage("Yote2.png", 1, 50); // 使用本地图片替代网络图片
```

## 修改的文件列表

1. **AlguiToolImage.java**
   - 添加SSL证书信任配置
   - 创建备用图片加载方法
   - 添加HTTP备用方案

2. **network_security_config.xml** (新建)
   - 配置网络安全策略
   - 允许特定域名的明文流量

3. **AndroidManifest.xml**
   - 添加网络安全配置引用
   - 启用明文流量

4. **Game2FloatWindow.java**
   - 使用本地图片替代网络图片

5. **Game5FloatWindow.java**
   - 使用本地图片替代网络图片
   - 更新测试按钮使用SSL处理方法

## 测试建议

1. **测试SSL处理**: 使用新的`getImageURLWithSSLHandling`方法
2. **测试HTTP备用**: 验证HTTPS失败时HTTP备用方案是否工作
3. **测试本地图片**: 确认本地图片加载正常
4. **查看日志**: 通过日志确认SSL处理过程

## 安全注意事项

⚠️ **重要提醒**: 
- 信任所有SSL证书会降低安全性
- 仅建议在开发测试环境中使用
- 生产环境应使用正确的SSL证书
- 考虑使用更安全的图片托管服务

## 替代方案

1. **使用本地图片**: 将图片文件放在assets目录中
2. **使用CDN服务**: 选择有正确SSL证书的CDN
3. **使用Base64**: 将图片编码为Base64字符串
4. **使用HTTP**: 如果安全要求不高，可以使用HTTP

## 最新修复状态

✅ **SSL证书信任**: 已配置  
✅ **网络安全配置**: 已添加  
✅ **备用加载方法**: 已创建  
✅ **HTTP备用方案**: 已实现  
✅ **本地图片备用**: 已配置  
✅ **测试功能**: 已更新  

现在SSL证书问题应该已经解决，网络图片可以正常加载了。 