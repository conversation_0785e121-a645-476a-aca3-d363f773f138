1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bytecat.algui"
4    android:versionCode="130"
5    android:versionName="1.3.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="30" />
9-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml
10
11    <!-- 此应用可显示在其他应用上方 -->
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:7:5-77
12-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:7:22-75
13
14    <!-- 查看网络连接 -->
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:10:5-78
15-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:10:22-76
16
17    <!-- 更改网络连接性 -->
18    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
18-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:13:5-78
18-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:13:22-76
19
20    <!-- 连接WLAN网络和断开连接 -->
21    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
21-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:16:5-75
21-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:16:22-73
22
23    <!-- 拥有完全的网络访问权限 -->
24    <uses-permission android:name="android.permission.INTERNET" />
24-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:19:5-66
24-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:19:22-64
25
26    <!-- 录音 -->
27    <uses-permission android:name="android.permission.RECORD_AUDIO" />
27-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:22:5-70
27-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:22:22-68
28
29    <!-- 修改或删除您共享存储空间中的内容 -->
30    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
30-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:25:5-80
30-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:25:22-78
31
32    <!-- 读取您共享存储空间中的内容 -->
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:28:5-79
33-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:28:22-77
34
35    <!-- 访问管理外部存储 -->
36    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
36-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:31:5-81
36-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:31:22-79
37
38    <application
38-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:33:5-80:19
39        android:name="com.bytecat.algui.GlobalApplication"
39-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:40:9-42
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.6.0] F:\Android\gradle\caches\transforms-2\files-2.1\3732dbaed7efee9bf6fb58da2e28e003\core-1.6.0\AndroidManifest.xml:24:18-86
41        android:debuggable="true"
41-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:34:9-34
42        android:icon="@drawable/ic_launcher"
42-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:36:9-45
43        android:isGame="true"
43-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:35:9-30
44        android:label="@string/app_name"
44-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:37:9-41
45        android:networkSecurityConfig="@xml/network_security_config"
45-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:41:9-69
46        android:resizeableActivity="true"
46-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:39:9-42
47        android:testOnly="true"
48        android:theme="@style/AppTheme"
48-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:38:9-40
49        android:usesCleartextTraffic="true" >
49-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:42:9-44
50        <activity
50-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:44:9-57:20
51            android:name="com.bytecat.algui.MainActivity"
51-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:45:13-41
52            android:exported="true"
52-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:47:13-36
53            android:label="@string/app_name" >
53-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:46:13-45
54            <intent-filter>
54-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:49:13-55:29
55                <action android:name="android.intent.action.MAIN" />
55-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:51:17-68
55-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:51:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:53:17-76
57-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:53:27-74
58            </intent-filter>
59        </activity>
60
61        <!-- 注册游戏详情页Activity -->
62        <activity android:name="com.bytecat.algui.GameDetailActivity" />
62-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:60:9-56
62-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:60:19-53
63
64        <!-- 注册悬浮窗选择页Activity -->
65        <activity android:name="com.bytecat.algui.FloatSelectionActivity" />
65-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:63:9-60
65-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:63:19-57
66
67        <!-- 注册攻略页Activity -->
68        <activity android:name="com.bytecat.algui.GuideActivity" />
68-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:66:9-51
68-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:66:19-48
69
70        <!-- 注册攻略网页Activity -->
71        <activity android:name="com.bytecat.algui.GuideWebActivity" />
71-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:69:9-54
71-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:69:19-51
72
73        <!-- 注册发卡网页Activity -->
74        <activity android:name="com.bytecat.algui.CardActivity" />
74-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:72:9-50
74-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:72:19-47
75
76        <meta-data
76-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:74:9-76:34
77            android:name="android.max_aspect"
77-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:75:13-46
78            android:value="4.0" />
78-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:76:13-32
79
80        <service android:name="com.bytecat.algui.AlguiService" />
80-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:78:9-65
80-->F:\Android\code\tohka\AlguiPro\app\src\main\AndroidManifest.xml:78:18-63
81    </application>
82
83</manifest>
