<?xml version='1.0' encoding='utf-8'?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.bytecat.algui">

    <!-- 此应用可显示在其他应用上方 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>

    <!-- 查看网络连接 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>

    <!-- 更改网络连接性 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>

    <!-- 连接WLAN网络和断开连接 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>

    <!-- 拥有完全的网络访问权限 -->
    <uses-permission android:name="android.permission.INTERNET"/>

    <!-- 录音 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>

    <!-- 修改或删除您共享存储空间中的内容 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <!-- 读取您共享存储空间中的内容 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>

    <!-- 访问管理外部存储 -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>

    <application
        android:debuggable="true"
        android:isGame="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:resizeableActivity="true"
        android:name=".GlobalApplication"
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true">

        <activity
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:exported="true">

            <intent-filter>

                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>

            </intent-filter>

        </activity>

        <!-- 注册游戏详情页Activity -->
        <activity android:name=".GameDetailActivity" />

        <!-- 注册悬浮窗选择页Activity -->
        <activity android:name=".FloatSelectionActivity" />

        <!-- 注册攻略页Activity -->
        <activity android:name=".GuideActivity" />

        <!-- 注册攻略网页Activity -->
        <activity android:name=".GuideWebActivity" />

        <!-- 注册发卡网页Activity -->
        <activity android:name=".CardActivity" />

        <meta-data
            android:name="android.max_aspect"
            android:value="4.0"/>

        <service android:name="com.bytecat.algui.AlguiService"/>

    </application>

</manifest>
