package com.bytecat.algui.AlguiHacker;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/22 21:48
 * @Describe Algui ipc通道 声明支持root环境执行的方法
 */
interface AlguiRootIPC {

    //=======AlguiROOT内存修改工具========
    // 初始化
    int setPackageName(in String packageName); // 设置目标包名 【注意：这是初始化函数，不调用此函数的话其它内存操作均失效】
    int getPID(in String packageName); // 获取进程ID 
    void setIsSecureWrites(boolean sw);// 设置安全写入启用状态
    // 模块[动/静]态基址偏移内存修改
    long getModuleBaseAddr(in String moduleName, int headType); // 获取模块起始地址(基址)
    long jump(long addr,int count); // 跳转指针
    long jump32(long addr); // 跳转指针 [32位]
    long jump64(long addr); // 跳转指针 [64位]
    int setMemoryAddrValue(in String value, long addr, int type, boolean isFree); // 设置指定内存地址指向的值 🛡️该方法已对当前进程进行保护 防止GG模糊🛡️
    String getMemoryAddrData(long addr, int type); // 获取指定内存地址的数据
    int getDword(long addr);
    float getFloat(long addr);
    double getDouble(long addr);
    long getQword(long addr);
    int getWord(long addr);
    byte getByte(long addr);
    String getString(long addr);

    int getDword_t(long addr);
    float getFloat_t(long addr);
    double getDouble_t(long addr);
    long getQword_t(long addr);
    int getWord_t(long addr);
    byte getByte_t(long addr);
    String getString_t(long startAddr);
    
    
    // 内存搜索
    void setMemoryArea(int memoryArea); // 设置要搜索的内存区域
    long[] MemorySearch(in String value, int type); // 内存搜索
    long[] MemorySearchRange(in String value, int type); // 内存搜索范围值
    long[] MemorySearchUnited(in String value, int type); // 联合内存搜索
    long[] ImproveOffset(in String value, int type, long offset); // 偏移改善
    long[] ImproveOffsetRange(in String value, int type, long offset); // 偏移范围改善
    long[] ImproveOffsetUnited(in String value, int type, long offset); // 偏移联合改善
    long[] ImproveValue(in String value, int type); // 改善
    int MemoryOffsetWrite(in String value, int type, long offset, boolean isFree); // 结果偏移写入数据
    int getResultCount(); // 获取最终搜索结果数量
    long[] getResultList(); // 获取最终搜索结果列表
    int printResultListToFile(in String filePath); // 打印最终搜索结果列表到指定文件
    int clearResultList(); // 清空最终搜索结果列表
    // 冻结内存修改
    //FreezeItem[] getFreezeList(); // 获取冻结项目列表 !不支持!
    void setFreezeDelayMs(int delay); // 设置冻结延迟时间（毫秒）
    int getFreezeNum(); // 获取冻结项目数量
    int addFreezeItem(in String value, long addr, int type); // 添加冻结项目
    int removeFreezeItem(long addr); // 移除指定冻结项目
    int removeAllFreezeItem(); // 移除所有冻结项目
    int startAllFreeze(); // 启动所有冻结项目
    int stopAllFreeze(); // 停止所有冻结项目
    int printFreezeListToFile(in String filePath); // 将冻结项目列表打印到文件
    // 获取内存信息相关工具
    String getMemoryAddrMapLine(long address); // 获取指定内存地址的Maps映射行
    String getMapLineMemoryAreaName(in String mapLine); // 获取Maps映射行所在内存区域名称
    String getMemoryAreaIdName(int memid); // 获取指定内存id的内存名称
    String getMemoryAreaName(); // 获取当前内存名称
    String getDataTypeName(int typeId); // 获取指定数据类型id的数据类型名称
    // 完全需要ROOT权限的操作
    // PS：告诉一下菜鸟，这些操作涉及到跨进程和系统操作，所以必须完全ROOT，直装也没用
    //--kill--
    int killProcess_Root(in String packageName); // 杀掉指定包名的进程
    void killAllInotify_Root(); // 杀掉所有inotify监视器，防止游戏监视文件变化
    int stopProcess_Root(String packageName); // 暂停指定包名的进程 (此方法对于执行者自身进程无需Root)
    int resumeProcess_Root(String packageName); // 恢复被暂停的指定包名的进程 (此方法对于执行者自身进程无需Root)
    int killGG_Root(); // 杀掉GG修改器
    int killXscript_Root(); // 杀掉XS脚本
    //--Other--
    int rebootsystem_Root(); // 重启手机
    int installapk_Root(in String apkPackagePath); // 静默安装指定路径的APK安装包
    int uninstallapk_Root(in String packageName); // 静默卸载指定包名的APK软件
    int Cmd(in String command);//执行命令
    int Cmd_Root(in String command);//执行超级命令

    void JniSwitch(int id,boolean isSwitch,String value);//执行JNI层的功能
   
    
}
