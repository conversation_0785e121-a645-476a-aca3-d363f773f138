package com.bytecat.algui.AlguiDemo;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/01/19 12:04
 * @Describe Algui示例
 */
import android.content.Context;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewFoldMenu;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;

public class AlguiDemo {
    
    public static final String TAG = "AlguiDemo";
    
    public static AlguiWinMenu show(final Context aContext) {
        //获取Gui简单快速构建器
        final AlguiV a = AlguiV.Get(aContext);

        //创建一个菜单窗口
        final AlguiWinMenu menu = a.WinMenu("AlguiDemo").showMenu();
        menu.setCatMenuBufferLineMargins(3, 3, 3, 0);//设置菜单每行的外边距
        menu.setCatMenuSize(300, 480);//设置菜单大小
        menu.setCatMenuBufferLineMaxView(1);//设置菜单一行最大视图容量为1这样一行一个折叠菜单，无需手动换行

        a.TextInfo(menu, "作者很懒，示例写到一半不想写了太多了，使用上都差不多自己摸索，望原谅！");

        a.TickItem(menu, "显示日志窗口")
            .setCatCallback(new AlguiCallback.Item(){
                public void item(int id) {
                    if (id == 1) {
                        AlguiLog.getLogConsole(aContext).show();
                    } else {
                        AlguiLog.getLogConsole(aContext).exit();
                    }
                }
            });
        AlguiViewFoldMenu by= a.FoldMenu(menu, "帮助");
        by.setCatUnfold(true);//默认展开
        a.TextTitle(by, "致谢");
        by.endl();
        a.TextSon(by, "Algui 作者：ByteCat & 艾琳");
        by.endl();
        a.TextSon(by, "作者b站：https://b23.tv/3u2y9YO [搜ByteCat404]").setCatTextIsLink(true);
        by.endl();
        a.TextSon(by, "作者QQ：https://qm.qq.com/q/ot52Gd53Eu [3353484607]").setCatTextIsLink(true);
        by.endl();
        a.TextSon(by, "QQ群：https://qm.qq.com/q/beuSE6DvIk [931212209]").setCatTextIsLink(true);
        by.endl();
        a.TextSon(by, "联系作者QQ购买Algui专业版，在售后群下载最新完整版").setCatTextColor(0xFF9C27B0);
        by.endl();
        a.TextSon(by, "Algui界面设计来源imgui，感谢亲爱的imgui");
        by.endl();
        a.Line(by, 0xFF42424C).setCatMargins(0, 2, 0, 2);
        by.endl();


        a.TextTitle(by, "关于");
        by.endl();
        a.TextSon(by, "Algui是一个基于安卓Java的窗口UI开发工具包，旨在为开发者提供快速创建窗口界面、内存修改和HOOK功能的解决方案。该工具包灵感来源于IMGUI（Immediate Mode GUI）库，结合Java和JNI（Java Native Interface）技术，为开发者提供灵活的内存操作和调试能力，尤其适合游戏内存修改和作弊菜单制作。");
        by.endl();
        a.Line(by, 0xFF42424C).setCatMargins(0, 2, 0, 2);
        by.endl();

        a.TextTitle(by, "核心功能");
        by.endl();
        a.TextSon(by, "窗口UI框架：采用Java和IMGUI风格的UI库，支持快速创建和自定义窗口界面，简化开发者的GUI开发流程。");
        by.endl();
        a.TextSon(by, "内存修改支持：提供GG修改器类似的功能，支持范围搜索、联合搜索、联合改善等高级特性。开发者可以通过Java直接对内存进行操作，进行数据修改和调试。");
        by.endl();
        a.TextSon(by, "JNI内存操作：利用JNI桥接技术，直接在Java层实现底层内存修改，不需要依赖外部工具，提升了修改效率和稳定性。");
        by.endl();
        a.TextSon(by, "HOOK功能：支持对指定函数进行HOOK操作，允许在程序运行时动态拦截和修改目标函数的执行，适用于调试、反向工程以及作弊功能的实现。");
        by.endl();
        a.Line(by, 0xFF42424C).setCatMargins(0, 2, 0, 2);
        by.endl();

        a.TextTitle(by, "适用场景");
        by.endl();
        a.TextSon(by, "游戏辅助：可用于快速制作游戏中的内置作弊菜单直装、外部作弊菜单插件、内存修改器等。");
        by.endl();
        a.TextSon(by, "内存分析与修改：适合开发内存扫描、修改工具，支持对内存中的数据进行精确搜索和修改。");
        by.endl();
        a.TextSon(by, "反向工程：在逆向工程和调试中提供便捷的界面和内存操作功能，辅助开发者进行漏洞分析和功能探索。");
        by.endl();
        a.Line(by, 0xFF42424C).setCatMargins(0, 2, 0, 2);
        by.endl();

        a.TextTitle(by, "优势");
        by.endl();
        a.TextSon(by, "支持直接在Java进行Root跨进程内存操作。");
        by.endl();
        a.TextSon(by, "无需外部插件，Java原生支持内存操作与调试。");
        by.endl();
        a.TextSon(by, "高度可定制化的UI，极大提升开发效率。");
        by.endl();
        a.TextSon(by, "提供多种内存修改算法，支持高级搜索功能。");
        by.endl();
        a.TextSon(by, "通过JNI实现底层内存访问，避免了传统的外挂工具依赖问题。");
        by.endl();
        a.Line(by, 0xFF42424C).setCatMargins(0, 2, 0, 2);
        by.endl();


        a.TextInfo(by, "免责声明：本工具包仅用于合法的开发和调试目的，开发者应遵守当地法律法规，避免用于非法用途。");
        by.endl();

        //往菜单添加一个折叠菜单
        AlguiViewFoldMenu fm1= a.FoldMenu(menu, "文本");
        fm1.setCatUnfold(true);//默认展开
        a.TextInfo(fm1, "以下只对文本的额外特性进行示例，并不包含所有，具体效果还需自己实现");
        fm1.endl();//折叠菜单换行
        a.Text(fm1, "普通文本");
        a.Text(fm1, "添加阴影").setCatTextSize(11). setCatTextGlow(5, 0xFFFFFFFF);
        a.TextColors(fm1, "渐变文本", 0xFFff00cc, 0xFFffcc00, 0xFF00ffcc, 0xFFff0066);
        a.TextColorsMove(fm1, "动态渐变文本", 0xFFff00cc, 0xFFffcc00, 0xFF00ffcc, 0xFFff0066);
        fm1.endl();//折叠菜单换行
        a.TextHelp(fm1, "可点击弹出提示框的文本", "提示：这是可弹出提示框的文本");
        fm1.endl();//折叠菜单换行
        a.TextRoll(fm1, "这是可滚动的文本，可自行调整滚动速度");
        fm1.endl();//折叠菜单换行
        a.TextRoll(fm1, "例如慢速滚动的文本，并且可以同时启用动态跑马灯渐变")
            .setCatTextRollSpeed(1)//设置滚动速度
            .setCatTextColor(0xFFff00cc, 0xFFffcc00, 0xFF00ffcc, 0xFFff0066)//设置文本颜色为多个颜色进行渐变
            .setCatTextMoveGrad(true)//启用动态渐变
            ;
        fm1.endl();//折叠菜单换行
        a.Textlink(fm1, "可自动识别链接 点击链接加入群聊：https://qm.qq.com/q/3RkXRJxUyk");
        fm1.endl();//折叠菜单换行

        String text = "这是HTML文本 例如<b>粗体</b>和<i>斜体 </i>" +
            "<a href='https://qm.qq.com/q/3RkXRJxUyk'>加入Algui交流群</a>";

        a.TextHtml(fm1, text);



        //往菜单添加一个折叠菜单
        AlguiViewFoldMenu fm= a.FoldMenu(menu, "其它组件");
        fm.setCatUnfold(true);//默认展开
        fm.setCatLineMargins(3, 3, 3, 0);//设置折叠菜单每行的外边距
        fm.setCatLineMaxView(1);//设置折叠菜单一行最大视图容量为1就无需手动换行了
        a.FoldMenuSwitch(fm, "发送一个通知", new AlguiCallback.Item(){
                public void item(int id) {
                    //播放一个音效
                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info_gta5);
                    switch (id) {
                        default:
                        case 0:
                            AlguiWinInform.Get(aContext) .showInfo_Black(AlguiAssets.Icon.inform_info, "通知标题", "这是通知信息", 3);
                            break;
                        case 1:
                            AlguiWinInform.Get(aContext) .showInfo_White(AlguiAssets.Icon.inform_info, "通知标题", "这是通知信息", 3);
                            break;
                        case 2:
                            AlguiWinInform.Get(aContext) .showInfo_Blue(AlguiAssets.Icon.inform_info, "通知标题", "这是通知信息", 3);
                            break;
                    }
                }
            }, "通知风格：黑色", "通知风格：白色", "通知风格：蓝色");

        a.FoldMenuSwitch(fm, "显示一个文本对话框", new AlguiCallback.Item(){
                public void item(int id) {
                    //播放一个音效
                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                    switch (id) {
                        default:
                        case 0:
                            a.WinDialogText_Black("对话框标题", "对话框文本");
                            break;
                        case 1:
                            a.WinDialogText_White("对话框标题", "对话框文本");
                            break;
                        case 2:
                            a.WinDialogText_Blue("对话框标题", "对话框文本");
                            break;
                    }
                }
            }, "对话框风格：黑色", "对话框风格：白色", "对话框风格：蓝色");
        //如果使用此方式设置进程后之后进行内存修改不要设置包名
        a.FoldMenuSwitch(fm, "选择游戏进程[示例]", new AlguiCallback.Item(){
                public void item(int id) {

                    switch (id) {
                        default:
                        case 0://4399
                            AlguiMemTool.setPackageName("4399版本包名");
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                            AlguiWinInform.Get(aContext) .showInfo_White("https://pp.myapp.com/ma_icon/0/icon_196230_1734512088/256", "已切换进程", "当前为4399版本", 5);

                            break;
                        case 1://TapTap
                            AlguiMemTool.setPackageName("TapTap版本包名");
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                            AlguiWinInform.Get(aContext) .showInfo_White("https://android-artworks.25pp.com/fs08/2021/09/30/10/106_6277470a4726c83336916a66e77b068d_con_130x130.png", "已切换进程", "当前为TapTap版本", 5);

                            break;

                    }
                }
            }, "4399版本的", "TapTap版本");


        a.InputText(fm, "播放音频(支持：base64，网络音频链接，本地音频路径)", "开始播放")
            .setCatCallback(new AlguiCallback.Input(){
                public void start(String text) {

                }
                public void update(String text) {

                }
                public void end(String text) {

                }
                public void buttonClick(String text) {
                    AlguiToolAudio.playAudio(aContext, text);
                }
            });



        return menu;
    }
    
    
}
