package com.bytecat.algui.AlguiDemo;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/01/19 12:02
 * @Describe Algui游戏ESP绘制透视外挂调试菜单示例
 */
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.SurfaceHolder;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolCache;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.bytecat.algui.AlguiWindows.AlguiWinDraw;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.bytecat.algui.AlguiManager.AlguiAssets;

public class AlguiDemoESPDebugMenu {
    
    public static final String TAG = "AlguiDemoESPDebugMenu";
    
    private static final int ENGINE_UNITY=0;
    private static final int ENGINE_UE=1;
    private static int engine=ENGINE_UNITY;
    private static int pSize=4;//内存指针大小
    private static String packName;//游戏包名
    private static long matrixArray=-1;//矩阵数组头
    private static long objArray=-1;//目标数组头
    private static long xo=-1;//x偏移
    private static long yo=-1;//y偏移
    private static long zo=-1;//z偏移
    private static int num;//遍历数量
    //初始化绘制
    private static AlguiWinDraw initDraw(Context aContext) {
        //画笔
        final Paint paint = new Paint();
        paint.setColor(0xFFFF9800);
        paint.setAntiAlias(true);     //设置抗锯齿

        AlguiLog.setLogRepeatable(false);//不可重复写入日志

        //创建Algui动态帧绘制的窗口
        final AlguiWinDraw draw = new AlguiWinDraw(aContext);
        draw.setCatCallback(new AlguiCallback.Draw(){
                int px,py,pxMidpoint,pyMidpoint;//画布分辨率和画布中心点坐标
                float[] matrix = new float[4 * 4];//矩阵数组 这里4x4矩阵
                String info;
                //第一帧调用初始化
                //返回true代表初始化完成开始下一帧更新 
                //返回false代表初始化失败将锁定在此帧一直初始化并检查直到返回true才开始下一帧更新
                public boolean Start(Canvas canvas) {
                    if (objArray <= 0 || xo <= 0)return false;
                    //设置游戏包名
                    int pid = AlguiMemTool.setPackageName(packName);
                    if (pid <= 0)return false;

                    if (matrixArray <= 0) {
                        //如果没有矩阵数组则使用默认基址(💀这里是我自己为了方便才这样 其它游戏不同)
                        long libunity = AlguiMemTool.getModuleBaseAddr("libunity.so", AlguiMemTool.HEAD_CB);
                        matrixArray = jump(jump(jump(libunity + 0x92900) + 0x0) + 0x40) + 0x2E4;
                        if (libunity <= 0 || matrixArray <= 0)return false;
                    }
                    info =String.format("PID%d | 数量%d | 矩阵头0x%08X | 数组头0x%08X", pid,num,matrixArray,objArray);
                    //初始化成功
                    return true;
                }
                //每一帧调用更新 
                //返回true代表更新完成开始下一帧更新 
                //返回false代表更新失败会跳转到Start函数检查是否初始化完成
                public boolean Update(Canvas canvas) {

                    canvas.drawText(info, 10, 10, paint);  
                    //获取当前矩阵
                    for (int i = 0; i < matrix.length; i++) {
                        matrix[i]  = Float.parseFloat(AlguiMemTool.getMemoryAddrData(matrixArray + i * 4, AlguiMemTool.TYPE_FLOAT));
                    }

                    //遍历数组
                    for (int i=0;i < num;i++) {
                        //获取当前指针结构体头部
                        long obj = jump(objArray + i * pSize);
                        //获取xyz 3D坐标
                        float x = Float.parseFloat(AlguiMemTool.getMemoryAddrData(obj + xo, AlguiMemTool.TYPE_FLOAT));
                        float y = Float.parseFloat(AlguiMemTool.getMemoryAddrData(obj + yo, AlguiMemTool.TYPE_FLOAT));
                        float z = Float.parseFloat(AlguiMemTool.getMemoryAddrData(obj + zo, AlguiMemTool.TYPE_FLOAT));

                        //将3D坐标利用摄像机矩阵转换到摄像机坐标
                        float cz = matrix[3] * x + matrix[7] * z + matrix[11] * y + matrix[15];
                        if (cz == 0) cz = 1;  //防止后面除零异常
                        float cx = pxMidpoint + (matrix[0] * x + matrix[4] * z + matrix[8] * y + matrix[12]) / cz * pxMidpoint;
                        float cy = pyMidpoint - (matrix[1] * x + matrix[5] * z + matrix[9] * y + matrix[13]) / cz * pyMidpoint;
                        if (cz <= 2) continue; //在摄像机后面则跳过

                        //绘制坐标点
                        paint.setColor(0xFFFF9800);
                        paint.setStyle(Paint.Style.FILL); //填充
                        canvas.drawCircle(cx, cy, 8, paint);
                        paint.setColor(0xFF000000);
                        paint.setStyle(Paint.Style.STROKE); //描边
                        paint.setStrokeWidth(3);
                        canvas.drawCircle(cx, cy, 8, paint);
                    }

                    return true;//更新完成开始下一帧更新
                }

                //渲染线程结束时调用
                public void End(SurfaceHolder holder) {

                }

                //跳转指针简化
                public long jump(long addr) {
                    return AlguiMemTool.jump(addr, pSize);
                }

                //更新画布大小时调用
                public void UpdateCanvasSize(SurfaceHolder holder, int format, int width, int height) {
                    px = width;
                    py = height;
                    pxMidpoint = px / 2;
                    pyMidpoint = py / 2;
                }
            }
        );
        return draw;
    }



    //显示菜单
    public static AlguiWinMenu show(final Context aContext) {

        AlguiV a=AlguiV.Get(aContext);//获取UI构建器

        AlguiWinMenu menu = a.WinMenu("ESP-Debug");//创建菜单
        menu.setCatMenuBufferLineMaxView(1);//一行最大1个视图 自动换行
        menu.setCatMenuBufferLineMargins(3, 3, 3, 0);//设置菜单每行的外边距
        packName = (String)AlguiToolCache.getData(aContext, "ESPDEBUG_PACKNAME", "");//使用上次保存发包名
        a.InputText(menu, "设置游戏包名")
            .setCatIcon(AlguiAssets.Icon.game_handle)
            .setCatIconColor(0xFF1976D2)
            .setCatInputText(packName)
            .setCatCallback(new AlguiCallback.Input(){
                //开始输入时调用
                public void start(String text) {

                }
                //内容更新时调用
                public void update(String text) {

                }
                //输入结束时调用
                public void end(String text) {
                    packName = text;
                    AlguiToolCache.saveData(aContext, "ESPDEBUG_PACKNAME", packName);
                    AlguiLog.d(TAG,"包名：%s",packName);
                }
            }
        ).getByteIcon().setCatSize(11, 11);



        a.InputText(menu, "设置矩阵头地址")
            .setCatIcon(AlguiAssets.Icon.matrix_tabler)
            .setCatIconColor(0xFF1976D2)
            .setCatCallback(new AlguiCallback.Input(){
                //开始输入时调用
                public void start(String text) {

                }
                //内容更新时调用
                public void update(String text) {

                }
                //输入结束时调用
                public void end(String text) {
                    matrixArray = Long.parseLong(text, 16);
                    AlguiLog.d(TAG,"矩阵头：0x%08X",matrixArray);
                }
            }
        ).getByteIcon().setCatSize(11, 11);


        a.InputText(menu, "设置数组头地址")
            .setCatIcon(AlguiAssets.Icon.matrix_data)
            .setCatIconColor(0xFF1976D2)
            .setCatCallback(new AlguiCallback.Input(){
                //开始输入时调用
                public void start(String text) {

                }
                //内容更新时调用
                public void update(String text) {

                }
                //输入结束时调用
                public void end(String text) {
                    objArray = Long.parseLong(text, 16);
                    AlguiLog.d(TAG,"数组头：0x%08X",objArray);
                }
            }
        ).getByteIcon().setCatSize(11, 11);


        a.InputText(menu, "设置从数组指针结构体到X坐标的偏移量")
            .setCatIcon(AlguiAssets.Icon.v3d)
            .setCatIconColor(0xFF1976D2)
            .setCatCallback(new AlguiCallback.Input(){
                //开始输入时调用
                public void start(String text) {

                }
                //内容更新时调用
                public void update(String text) {

                }
                //输入结束时调用
                public void end(String text) {
                    xo = Long.parseLong(text, 16);
                    AlguiLog.d(TAG,"x坐标偏移：0x%08X",xo);
                }
            }
        ).getByteIcon().setCatSize(11, 11);

        a.InputInt(menu, "绘制数量")
            .setCatIcon(AlguiAssets.Icon.number)
            .setCatIconColor(0xFF1976D2)
            .setCatCallback(new AlguiCallback.Input(){
                //开始输入时调用
                public void start(String text) {

                }
                //内容更新时调用
                public void update(String text) {

                }
                //输入结束时调用
                public void end(String text) {
                    num = Integer.parseInt(text);
                    AlguiLog.d(TAG,"数量：%d",num);
                }
            }
        ).getByteIcon().setCatSize(11, 11);

        a.FoldMenuSwitch(menu, "设置游戏位数", new AlguiCallback.Item(){
                public void item(int id) {
                    switch (id) {
                        default:
                        case 0:
                            pSize = 4;
                            AlguiLog.d(TAG,"选择了32位 指针字节大小4");
                            break;
                        case 1:
                            pSize = 8;
                            AlguiLog.d(TAG,"选择了64位 指针字节大小8");
                            break;
                    }
                }
            }, "32位", "64位");

        a.FoldMenuSwitch(menu, "设置游戏引擎", new AlguiCallback.Item(){
                public void item(int id) {
                    switch (id) {
                        default:
                        case 0:
                            engine = ENGINE_UNITY;
                            AlguiLog.d(TAG,"选择了Unity引擎");
                            break;
                        case 1:
                            engine = ENGINE_UE;
                            AlguiLog.d(TAG,"选择了UE引擎");
                            break;
                    }
                }
            }, "Unity", "UE");

        final AlguiViewButton b = a.ButtonLong(menu, "开始绘制");
        b.setCatTextColor(0xFFFFFFFF);
        b.setCatBackColor(0xFF43A047);
        b.setCatCallback(new AlguiCallback.Click(){
                AlguiWinDraw draw = initDraw(aContext);
                public void click(boolean isSwitch) {
                    switch (engine) {
                        default:
                        case ENGINE_UNITY:
                            zo = xo + 0x4;
                            yo = zo + 0x4;
                            break;
                        case ENGINE_UE:
                            yo = xo + 0x4;
                            zo = yo + 0x4;
                            break;
                    }
                    AlguiLog.d(TAG,"开始绘制 坐标偏移：X=0x%08X Y=0x%08X Z=0x%08X",xo,yo,zo);
                    if (isSwitch) {
                        b.setCatBackColor(0xFFF44336);
                        b.setCatText("停止绘制");

                        draw.startDraw();
                    } else {
                        b.setCatBackColor(0xFF43A047);
                        b.setCatText("开始绘制");
                        draw.endDraw();
                    }
                }
            }
        );




        return menu;
    }
    
    
    
}
