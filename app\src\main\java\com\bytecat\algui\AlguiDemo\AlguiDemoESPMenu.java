package com.bytecat.algui.AlguiDemo;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/01/19 11:59
 * @Describe Algui游戏ESP绘制透视外挂菜单示例【简单】
 */
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.view.SurfaceHolder;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiWindows.AlguiWinDraw;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;

public class AlguiDemoESPMenu {
    
    public static final String TAG = "AlguiDemoESPMenu";
    
    //初始化绘制
    private static AlguiWinDraw initDraw(Context aContext) {
        //画笔
        final Paint paint = new Paint();
        paint.setColor(0xFF00FF00);  //初始颜色
        paint.setAntiAlias(true);     //设置抗锯齿
        paint.setTextAlign(Paint.Align.CENTER);//文本中心作为文本位置
        paint.setStrokeWidth(3);

        AlguiLog.setLogRepeatable(false);//不可重复写入日志

        //创建Algui动态帧绘制的窗口
        final AlguiWinDraw draw = new AlguiWinDraw(aContext);
        draw.setCatCallback(new AlguiCallback.Draw(){
                int px,py,pxMidpoint,pyMidpoint;//画布分辨率和画布中心点坐标
                int pSize;//内存指针大小(根据ARM位数调整)
                long matrixsAddr;//矩阵数组头
                long playersEnterAddr;
                long playersAddr;//玩家数组头
                float[] matrix = new float[4 * 4];//矩阵数组 这里4x4矩阵

                float playerWidth = 50;  //玩家宽度
                float playerHeight = 100; //玩家高度

                //🤖 其它游戏进行ESP ️只需要修改Start和Update函数中的一些参数
                //第一帧调用初始化
                //返回true代表初始化完成开始下一帧更新 
                //返回false代表初始化失败将锁定在此帧一直初始化并检查直到返回true才开始下一帧更新
                public boolean Start(Canvas canvas) {
                    //设置游戏arm位数 32and64
                    setArmBit(64);

                    //设置游戏包名
                    int pid = AlguiMemTool.setPackageName("com.sofunny.Sausage");
                    if (pid <= 0)return false;

                    //获取矩阵数组头
                    long libunity = AlguiMemTool.getModuleBaseAddr("libunity.so", AlguiMemTool.HEAD_CB);
                    matrixsAddr = jump(jump(jump(libunity + 0x92900) + 0x0) + 0x40) + 0x2E4;
                    if (libunity <= 0 || matrixsAddr <= 0)return false;

                    //获取玩家数组入口
                    long libil2cpp = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CD);
                    playersEnterAddr = jump(jump(jump(jump(jump(jump(libil2cpp + 0xA53198) + 0x28) + 0x1C0) + 0x18) + 0xE0) + 0x48) + 0x10;
                    if (libil2cpp <= 0 || playersEnterAddr <= 0)return false;

                    //获取玩家数组头 这里跳进玩家数组入口后偏移20到达玩家数组头
                    playersAddr = jump(playersEnterAddr) + 0x20;
                    if (playersAddr <= 0)return false;
                    AlguiLog.d(TAG,
                               "\n绘制初始化完成：" +
                               "\n进程PID：%d" + 
                               "\nlibil2cpp.so起始地址：0x%08X" +
                               "\nlibunity.so起始地址：0x%08X" +
                               "\n玩家数组入口地址：0x%08X" +
                               "\n玩家数组头地址：0x%08X" + 
                               "\n矩阵头地址：0x%08X"
                               , pid, libil2cpp, libunity, playersEnterAddr, playersAddr, matrixsAddr
                               );
                    //初始化成功
                    return true;
                }
                //每一帧调用更新 
                //返回true代表更新完成开始下一帧更新 
                //返回false代表更新失败会跳转到Start函数检查是否初始化完成
                public boolean Update(Canvas canvas) {
                    //绘制画布边界(调试)
                    paint.setColor(Color.RED);
                    paint.setStyle(Paint.Style.STROKE); 
                    paint.setStrokeWidth(2); 
                    canvas.drawRect(0, 0, px, py, paint);

                    //获取玩家数量 
                    //这里香肠派对玩家数量在玩家数组入口0x8的位置
                    //其它游戏不同自行配置
                    int playersNum = Integer.parseInt(AlguiMemTool.getMemoryAddrData(playersEnterAddr + 0x8, AlguiMemTool.TYPE_DWORD));AlguiLog.d(TAG, "玩家数量：%d", playersNum);
                    if (playersNum <= 0 || playersNum > 100)return false;//玩家数量异常重新初始化

                    //获取当前矩阵
                    for (int i = 0; i < matrix.length; i++) {
                        matrix[i]  = Float.parseFloat(AlguiMemTool.getMemoryAddrData(matrixsAddr + i * 4, AlguiMemTool.TYPE_FLOAT));
                    }

                    //遍历玩家数组 绘制每个玩家
                    for (int i=0;i < playersNum;i++) {
                        //获取当前玩家结构体头部
                        long player = jump(playersAddr + i * pSize);
                        //获取当前玩家血量
                        float hp = Float.parseFloat(AlguiMemTool.getMemoryAddrData(player + 0x700, AlguiMemTool.TYPE_FLOAT));
                        if (hp <= 0)continue;//跳过已死亡的玩家
                        //获取当前玩家xzy 3D坐标
                        float x = Float.parseFloat(AlguiMemTool.getMemoryAddrData(player + 0x548, AlguiMemTool.TYPE_FLOAT));
                        float z = Float.parseFloat(AlguiMemTool.getMemoryAddrData(player + 0x54C, AlguiMemTool.TYPE_FLOAT));
                        float y = Float.parseFloat(AlguiMemTool.getMemoryAddrData(player + 0x550, AlguiMemTool.TYPE_FLOAT));
                        //将当前玩家3D坐标利用摄像机矩阵转换到摄像机坐标
                        float cz = matrix[3] * x + matrix[7] * z + matrix[11] * y + matrix[15];
                        if (cz == 0) cz = 1;  //防止后面除零异常
                        float cx = pxMidpoint + (matrix[0] * x + matrix[4] * z + matrix[8] * y + matrix[12]) / cz * pxMidpoint;
                        float cy = pyMidpoint - (matrix[1] * x + matrix[5] * z + matrix[9] * y + matrix[13]) / cz * pyMidpoint;
                        if (cz <= 2) continue; //在摄像机后面则跳过

                        //绘制坐标点
                        paint.setColor(Color.GREEN);
                        canvas.drawCircle(cx, cy, 5, paint);

                        //绘制方框
                        float left = cx - playerWidth / 2;
                        float top = cy - playerHeight / 2;
                        float right = cx + playerWidth / 2;
                        float bottom = cy + playerHeight / 2;

                        paint.setColor(Color.GREEN);
                        paint.setStyle(Paint.Style.STROKE);
                        paint.setStrokeWidth(2);
                        canvas.drawRect(left, top, right, bottom, paint);

                        //绘制血条
                        //根据血量改变颜色
                        if (hp > 75) {
                            paint.setColor(Color.GREEN);
                        } else if (hp > 50) {
                            paint.setColor(Color.YELLOW);
                        } else {
                            paint.setColor(Color.RED);
                        }
                        paint.setStyle(Paint.Style.FILL);
                        canvas.drawRect(left, top - 10, right, top, paint);               

                    }

                    return true;//更新完成开始下一帧更新
                }

                //渲染线程结束时调用
                public void End(SurfaceHolder holder) {

                }
                //设置游戏位数
                public void setArmBit(int b) {
                    pSize = b / 8;
                }
                //跳转指针简化
                public long jump(long addr) {
                    return AlguiMemTool.jump(addr, pSize);
                }

                //更新画布大小时调用
                public void UpdateCanvasSize(SurfaceHolder holder, int format, int width, int height) {
                    px = width;
                    py = height;
                    pxMidpoint = px / 2;
                    pyMidpoint = py / 2;
                }
            }
        );
        return draw;
    }


    //显示菜单
    public static AlguiWinMenu show(final Context aContext) {

        AlguiV a=AlguiV.Get(aContext);//获取UI构建器

        AlguiWinMenu menu = a.WinMenu("香肠派对64位ESP示例");//创建菜单
        //向菜单中添加控件
        a.CheckBox(menu, "ESP")
            .setCatCallback(new AlguiCallback.Click(){
                AlguiWinDraw draw = initDraw(aContext);//初始化绘制
                public void click(boolean isSwitch) {
                    if (isSwitch) {
                        draw.startDraw();
                    } else {
                        draw.endDraw();
                    }
                }
            }
        );

        return menu;
    }
    
}
