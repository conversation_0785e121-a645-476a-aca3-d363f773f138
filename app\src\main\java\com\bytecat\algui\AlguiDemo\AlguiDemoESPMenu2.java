package com.bytecat.algui.AlguiDemo;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/01/19 12:00
 * @Describe Algui游戏ESP绘制透视外挂菜单示例【复杂】
 */
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.view.SurfaceHolder;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiHacker.Vector3;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolMaths;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewFoldMenu;
import com.bytecat.algui.AlguiWindows.AlguiWinDraw;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import java.util.ArrayList;
import com.bytecat.algui.AlguiManager.AlguiObjectManager;

public class AlguiDemoESPMenu2 {
    
    public static final String TAG = "AlguiDemoESPMenu2";
    
    //游戏属性
    private static class Game {
        static final String packName="com.JUG.lastfire";//包名
        static final int arm=32;//arm位数 32and64
        static final int Byte=arm / 8;//内存字节长度
    }

    //摄像机矩阵
    private static class CamerasMatrix {
        private long addresss;//矩阵数组内存指针
        private float[] matrix = new float[4 * 4];//矩阵数组
        public CamerasMatrix(long addr) {
            addresss = addr;
        }
        //获取内存地址
        public long getAddr() {
            return addresss;
        }
        //更新矩阵
        public boolean update() {
            for (int i = 0; i < matrix.length; i++) {
                matrix[i]  = Float.parseFloat(AlguiMemTool.getMemoryAddrData(addresss + i * 4, AlguiMemTool.TYPE_FLOAT));
            }
            return true;
        }
        //获取矩阵
        public float[] getMatrix() {
            return matrix;
        }
    }

    //玩家数组
    private static class PlayerArray {
        private long addresss;//内存指针
        private int length=-4;//长度偏移
        private ArrayList<Player> array;

        public PlayerArray(long addr) {
            addresss = addr;
            array = new ArrayList<>();
        }
        //获取内存地址
        public long getAddr() {
            return addresss;
        }
        //更新玩家列表
        public boolean update() {
            int size=size();
            if (size <= 0 || size >= 101) {
                //AlguiLog.d(TAG, "玩家列表更新失败 玩家数量异常：" + size);
                return false;
            }
            clearAllPlayer();
            for (int i = 0; i < size; i++) {
                long p =addresss + i * Game.Byte;
                Player player=new Player(AlguiMemTool.jump(p, Game.Byte));
                addPlayer(player);
            }
            return true;
        }
        //获取数组长度
        public int size() {
            return Integer.parseInt(AlguiMemTool.getMemoryAddrData(addresss + length, AlguiMemTool.TYPE_DWORD));
        }
        //添加一个玩家
        public void addPlayer(Player player) {
            array.add(player);
        }
        //删除一个玩家
        public void remPlayer(Player player) {
            array.remove(player);
        }
        //清除所有玩家
        public void clearAllPlayer() {
            array.clear();
        }
        //获取玩家列表
        public ArrayList<Player> getPlayerList() {
            return array;
        }
    }

    //玩家结构体
    private static class Player {
        private long addresss;//内存指针
        private float maxHP=100;//最大血量
        private static final long HP = 0xC;//当前血量
        private static final long x = 0xEC;//x坐标
        private static final long z = 0xF0;//z坐标
        private static final long y = 0xF4;//y坐标
        public Player(long addr) {addresss = addr;}
        //获取内存地址
        public long getAddr() {
            return addresss;
        }
        //获取血量地址
        public long getHPaddr() {
            return getAddr() + HP;
        }
        //获取最大血量
        public float getMaxHP() {
            return maxHP;
        }
        //获取当前血量
        public int getHP() {
            return Integer.parseInt(AlguiMemTool.getMemoryAddrData(addresss + HP, AlguiMemTool.TYPE_DWORD));
        }
        //获取当前坐标
        public Vector3 getVector3() {
            return new Vector3(
                Float.parseFloat(AlguiMemTool.getMemoryAddrData(addresss + x, AlguiMemTool.TYPE_FLOAT))
                , Float.parseFloat(AlguiMemTool.getMemoryAddrData(addresss + y, AlguiMemTool.TYPE_FLOAT))
                , Float.parseFloat(AlguiMemTool.getMemoryAddrData(addresss + z, AlguiMemTool.TYPE_FLOAT))

            );
        }
    }
    private static int px, py; //屏幕分辨率
    private  static int pxMidpoint, pyMidpoint; //屏幕中心点
    private static  PlayerArray playerArray;//玩家数组
    private static  CamerasMatrix cameras;//摄像机矩阵
    //包围盒配置
    private static float width = 1f;  //包围盒宽度
    private static float height = 1f; //包围盒高度
    private static float depth = 2.2f;  //包围盒深度
    //方框配置
    private static float width3D = 1.0f; //玩家3D宽度(不同游戏自己测量)
    private static float height3D = 4.0f; //玩家3D高度(不同游戏自己测量)
    //文本配置
    private static float textScaling = 5; //文本动态缩放比例

    private static float xOffset;//x偏移
    private static float yOffset;//y偏移
    //绘制样式开关
    private static class DrawStyleBool {
        static boolean drawPos;//是否绘制坐标点
        static boolean drawRect;//是否绘制方框
        static boolean drawBox;//是否绘制包围盒
        static boolean drawBack;//是否绘制背敌
        static boolean drawRay;//是否绘制射线
    }
    //绘制属性开关
    private static class DrawAttBool {
        static boolean drawHP;//是否绘制血量
        static boolean drawName;//是否绘制名称
        static boolean drawInfo;//是否绘制信息
    }


    public static AlguiWinMenu show(final Context aContext) {
        //画笔
        final Paint paint = new Paint();
        paint.setColor(0xFF00FF00);  //初始颜色
        paint.setAntiAlias(true);     //设置抗锯齿
        paint.setTextAlign(Paint.Align.CENTER);//文本中心作为文本位置
        paint.setStrokeWidth(3);



        //创建Algui动态帧绘制的窗口 支持动态更新绘制 即使没有运行游戏
        final AlguiWinDraw draw = new AlguiWinDraw(aContext);
        draw.setCatCallback(new AlguiCallback.Draw(){
                //更新画布大小时调用
                public void UpdateCanvasSize(SurfaceHolder holder, int format, int width, int height) {
                    px = width;
                    py = height;
                    pxMidpoint = px / 2;
                    pyMidpoint = py / 2;
                }
                //第一帧调用初始化
                //返回true代表初始化完成开始下一帧更新 
                //返回false代表初始化失败将锁定在此帧一直初始化并检查直到返回true才开始下一帧更新
                public boolean Start(Canvas canvas) {
                    //读取进程
                    int pid = AlguiMemTool.setPackageName(Game.packName);
                    if (pid <= 0) {
                        // AlguiLog.d(TAG, "游戏进程获取失败 PID" + pid);
                        return false;
                    }


                    //获取模块起始地址
                    long libil2cpp = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_CB);
                    long libunity = AlguiMemTool.getModuleBaseAddr("libunity.so", AlguiMemTool.HEAD_CB);
                    if (libil2cpp <= 0 || libunity <= 0) {
                        // AlguiLog.d(TAG, "游戏模块获取失败 libil2cpp.so：0x%08X libunity.so：0x%08X", libil2cpp, libunity);
                        return false;
                    }


                    //获取对象数组起始地址
                    long a =AlguiMemTool.jump(libil2cpp + 0xA2BC, Game.Byte);
                    long b =AlguiMemTool.jump(a + 0x9D0, Game.Byte);
                    long c =AlguiMemTool.jump(b + 0x1CC, Game.Byte);
                    long objArrayStart = c + 0x10;
                    if (c <= 0) {
                        // AlguiLog.d(TAG, "玩家数组起始地址获取失败 地址：0x%08X", c);
                        return false;
                    }


                    //获取矩阵数组起始地址
                    long a2 =AlguiMemTool.jump(libunity + 0xC0D0, Game.Byte);
                    long b2 =AlguiMemTool.jump(a2 + 0x8, Game.Byte);
                    long c2 =AlguiMemTool.jump(b2 + 0x10, Game.Byte);
                    long matrixStart = c2 + 0x2C0;
                    if (c2 <= 0) {
                        // AlguiLog.d(TAG, "摄像机矩阵起始地址获取失败 地址：0x%08X" , c2);
                        return false;
                    }

                    //初始化玩家数组
                    playerArray = new PlayerArray(objArrayStart);
                    if (!playerArray.update()) {
                        return false;
                    }


                    //初始化摄像机矩阵
                    cameras = new CamerasMatrix(matrixStart);

                    /*AlguiLog.d(TAG,
                     "\n绘制初始化完成：" +
                     "\n进程PID：%d" + 
                     "\nlibil2cpp.so起始地址：0x%08X" +
                     "\nlibunity.so起始地址：0x%08X" +
                     "\n数组起始地址：0x%08X" + 
                     "\n矩阵起始地址：0x%08X"
                     , pid, libil2cpp, libunity, objArrayStart, matrixStart
                     );*/
                    /* AlguiLog.d(TAG, "\n下面检查一次是否获取到当前对局全部玩家\n\n");
                     for (int i=0;i < playerArray.size();i++) {
                     Player player= playerArray.getPlayerList().get(i);
                     long addr = player.getAddr();
                     int hp=player.getHP();
                     Vector3 v3=player.getVector3();
                     Vector3 v3Camera = AlguiToolMaths.worldV3To4x4V3(v3, cameras.getMatrix(), px, py);

                     AlguiLog.d(TAG, "\n玩家0x%08X的属性：\n血量：%d\n世界坐标：%s\n摄像机坐标：%s\n", addr, hp, v3, v3Camera);
                     }*/
                    return true;//初始化完成
                }
                //每一帧调用更新 
                //返回true代表更新完成开始下一帧更新 
                //返回false代表更新失败会跳转到Start函数检查是否初始化完成
                public boolean Update(Canvas canvas) {
                    //检查是否有玩家如果没有则重新初始化
                    int size=playerArray.size();
                    if (size <= 0 || size > 101) {
                        // AlguiLog.d(TAG, "玩家列表数量异常 重新初始化 数量：" + size);
                    }
                    cameras.update();//更新矩阵

                    for (Player player : playerArray.getPlayerList()) {
                        int hp = player.getHP();
                        float maxHp=player.getMaxHP();
                        if (hp <= 0 || hp > maxHp) continue;//跳过已死亡的玩家
                        Vector3 v3=player.getVector3();
                        Vector3 v3Camera = AlguiToolMaths.worldV3To4x4V3(v3, cameras.getMatrix(), px, py);
                        v3Camera.x += xOffset;
                        v3Camera.y += yOffset;
                        //在摄像机后面则跳过
                        if (v3Camera.z <= 2) continue;
                        //超出屏幕则只绘制在边界上 背敌
                        if (v3Camera.x > px || v3Camera.x < 0 || v3Camera.y > py || v3Camera.y < 0) {
                            float drawX = Math.max(0, Math.min(v3Camera.x, px));
                            float drawY = Math.max(0, Math.min(v3Camera.y, py)); 
                            if (DrawStyleBool.drawBack) {
                                paint.setColor(Color.RED);
                                canvas. drawCircle(drawX, drawY, 20, paint);
                            }
                            if (DrawStyleBool.drawRay) {
                                //射线
                                paint.setColor(0xFFFF6E40);
                                canvas.drawLine(pxMidpoint, 0, drawX, drawY, paint);
                            }

                            continue;
                        }
                        //人物3d尺寸转2d尺寸 近大远小
                        float width2D = width3D * pxMidpoint / v3Camera.z;
                        float height2D = height3D * pyMidpoint / v3Camera.z;
                        //计算方框每个点
                        float left = v3Camera.x - width2D / 2;
                        float top = v3Camera.y - height2D / 2;
                        float right = v3Camera.x + width2D / 2;
                        float bottom = v3Camera.y + height2D / 2;
                        float centreX =(right + left) / 2;
                        //计算血量条顶点
                        float hpTop = top - 10;//血量条高

                        if (DrawStyleBool.drawBox) {
                            drawBox(canvas, v3);//绘制当前对象3D包围盒
                        } 
                        if (DrawStyleBool.drawRect) {
                            //方框
                            paint.setColor(0xAE424242);
                            canvas.drawRect(left, top, right, bottom, paint);//填充
                        }
                        if (DrawAttBool.drawHP) {
                            //绘制血量条背景
                            paint.setColor(0x50FF0000);
                            canvas.drawRect(left, hpTop, right, top, paint);
                            paint.setColor(0xFF000000);
                            paint.setStyle(Paint.Style.STROKE);
                            canvas.drawRect(left, hpTop, right, bottom, paint);//描边
                            paint.setStyle(Paint.Style.FILL);
                            //绘制血量条前景
                            float hpBarWidth = width2D * (hp / maxHp);//血量条宽（根据血量和最大血量的比例）
                            int hpColor = hp >= maxHp * 0.6 ? 0xFF00FF00 : (hp >= maxHp * 0.3 ? 0xFFFF9800 : 0xFFFF0000);
                            paint.setColor(hpColor);
                            canvas.drawRect(left, hpTop, left + hpBarWidth, top, paint);

                        }
                        if (DrawStyleBool.drawRay) {
                            //射线
                            paint.setColor(Color.GREEN);
                            canvas.drawLine(pxMidpoint, 0, v3Camera.x, hpTop, paint);
                        }
                        //文本大小根据方框宽高来决定缩放
                        float textSize = Math.min(width2D, height2D) / textScaling;
                        paint.setTextSize(textSize);
                        paint.setColor(0xFFFFFFFF);
                        long addr=player.getAddr();
                        if (DrawAttBool.drawName) {
                            String str =String.format("玩家<0x%08X>", addr);
                            canvas.drawText(str, centreX, hpTop, paint);  
                        }
                        if (DrawAttBool.drawInfo) {
                            String str =  String.format("血量：%d\n世界坐标：%s\n摄像机坐标：%s",  hp, v3, v3Camera);
                            canvas.drawText(str, left, bottom, paint);  

                        }

                        //绘制当前对象坐标点
                        if (DrawStyleBool.drawPos) {
                            paint.setColor(Color.GREEN);
                            canvas.drawCircle(v3Camera.x, v3Camera.y, textSize, paint);
                        }

                    }



                    return true;//更新完成开始下一帧更新
                }
                //渲染线程结束时调用
                public void End(SurfaceHolder holder) {

                }

                //绘制包围盒
                private void drawBox(Canvas canvas, Vector3 v3) {
                    //从对象世界坐标点开始计算包围盒8个顶点的世界坐标
                    Vector3[] boxDotV3s = new Vector3[8];
                    boxDotV3s[0] = new Vector3(v3.x - width / 2, v3.y - height / 2, v3.z - depth / 2);  // 左下前
                    boxDotV3s[1] = new Vector3(v3.x + width / 2, v3.y - height / 2, v3.z - depth / 2);  // 右下前
                    boxDotV3s[2] = new Vector3(v3.x - width / 2, v3.y + height / 2, v3.z - depth / 2);  // 左上前
                    boxDotV3s[3] = new Vector3(v3.x + width / 2, v3.y + height / 2, v3.z - depth / 2);  // 右上前
                    boxDotV3s[4] = new Vector3(v3.x - width / 2, v3.y - height / 2, v3.z + depth / 2);  // 左下后
                    boxDotV3s[5] = new Vector3(v3.x + width / 2, v3.y - height / 2, v3.z + depth / 2);  // 右下后
                    boxDotV3s[6] = new Vector3(v3.x - width / 2, v3.y + height / 2, v3.z + depth / 2);  // 左上后
                    boxDotV3s[7] = new Vector3(v3.x + width / 2, v3.y + height / 2, v3.z + depth / 2);  // 右上后

                    //将包围盒8个顶点的世界坐标转换到摄像机坐标
                    Vector3[] boxDotV2s = new Vector3[8];
                    for (int i = 0; i < boxDotV3s.length; i++) {
                        boxDotV2s[i] = AlguiToolMaths.worldV3To4x4V3(boxDotV3s[i], cameras.getMatrix(), px, py);
                    }

                    paint.setColor(0x5000FF00);
                    //绘制前面面（前四个顶点构成一个矩形）
                    drawFilledFace(canvas, boxDotV2s[0], boxDotV2s[1], boxDotV2s[3], boxDotV2s[2]);
                    //绘制后面面（后四个顶点构成一个矩形）
                    drawFilledFace(canvas, boxDotV2s[4], boxDotV2s[5], boxDotV2s[7], boxDotV2s[6]);
                    //绘制连接前后面的面
                    drawFilledFace(canvas, boxDotV2s[0], boxDotV2s[1], boxDotV2s[5], boxDotV2s[4]); 
                    drawFilledFace(canvas, boxDotV2s[2], boxDotV2s[3], boxDotV2s[7], boxDotV2s[6]); 
                    drawFilledFace(canvas, boxDotV2s[1], boxDotV2s[3], boxDotV2s[7], boxDotV2s[5]); 
                    drawFilledFace(canvas, boxDotV2s[0], boxDotV2s[2], boxDotV2s[6], boxDotV2s[4]); 
                    paint.setColor(Color.BLACK); 
                    paint.setStrokeWidth(3);
                    //绘制前面四条边
                    canvas.drawLine(boxDotV2s[0].x, boxDotV2s[0].y, boxDotV2s[1].x, boxDotV2s[1].y, paint);
                    canvas.drawLine(boxDotV2s[1].x, boxDotV2s[1].y, boxDotV2s[3].x, boxDotV2s[3].y, paint);
                    canvas.drawLine(boxDotV2s[3].x, boxDotV2s[3].y, boxDotV2s[2].x, boxDotV2s[2].y, paint);
                    canvas.drawLine(boxDotV2s[2].x, boxDotV2s[2].y, boxDotV2s[0].x, boxDotV2s[0].y, paint);
                    //绘制后面四条边
                    canvas.drawLine(boxDotV2s[4].x, boxDotV2s[4].y, boxDotV2s[5].x, boxDotV2s[5].y, paint);
                    canvas.drawLine(boxDotV2s[5].x, boxDotV2s[5].y, boxDotV2s[7].x, boxDotV2s[7].y, paint);
                    canvas.drawLine(boxDotV2s[7].x, boxDotV2s[7].y, boxDotV2s[6].x, boxDotV2s[6].y, paint);
                    canvas.drawLine(boxDotV2s[6].x, boxDotV2s[6].y, boxDotV2s[4].x, boxDotV2s[4].y, paint);
                    //绘制连接前后面的四条边
                    canvas.drawLine(boxDotV2s[0].x, boxDotV2s[0].y, boxDotV2s[4].x, boxDotV2s[4].y, paint);
                    canvas.drawLine(boxDotV2s[1].x, boxDotV2s[1].y, boxDotV2s[5].x, boxDotV2s[5].y, paint);
                    canvas.drawLine(boxDotV2s[2].x, boxDotV2s[2].y, boxDotV2s[6].x, boxDotV2s[6].y, paint);
                    canvas.drawLine(boxDotV2s[3].x, boxDotV2s[3].y, boxDotV2s[7].x, boxDotV2s[7].y, paint);
                }
                private void drawFilledFace(Canvas canvas, Vector3 p1, Vector3 p2, Vector3 p3, Vector3 p4) {
                    Path path = new Path();
                    path.moveTo(p1.x, p1.y);
                    path.lineTo(p2.x, p2.y);
                    path.lineTo(p3.x, p3.y);
                    path.lineTo(p4.x, p4.y);
                    path.close(); 
                    canvas.drawPath(path, paint);
                }
            }
        );



        //创建UI
        //AlguiLog.setLogRepeatable(false);//不重复日志
        AlguiV a=AlguiV.Get(aContext);//获取Gui简单快速构建器


        //创建普通菜单窗口
        AlguiWinMenu menu = a.WinMenu("游戏逆向透视绘制Demo");
        menu.setCatMenuBufferLineMargins(3, 3, 3, 0);//设置菜单每行的外边距
        menu.setCatMenuSize(300, 200);//设置菜单大小
        //主菜单添加复选框
        a.CheckBox(menu, "调试控制台")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    if (isSwitch) {
                        AlguiLog.getLogConsole(aContext).show();
                    } else {
                        AlguiLog.getLogConsole(aContext).exit();
                    }
                }
            }
        );
        //主菜单添加复选框
        a.CheckBox(menu, "开始")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    if (isSwitch) {
                        draw.startDraw();
                    } else {
                        draw.endDraw();
                    }
                }
            }
        );
        menu.endl();//主菜单换行


        //主菜单添加一个折叠菜单
        AlguiViewFoldMenu drawFold= a.FoldMenu(menu, "视觉");
        drawFold.setCatLineMargins(0, 0, 0, 3);//设置折叠菜单每行的外边距
        //折叠菜单添加文本
        // a.Text(drawFold,"绘制类型");
        // a.TextHelp(drawFold,"?","");
        a.FoldMenuSwitch(drawFold, "绘制类型", new AlguiCallback.Item(){
                public void item(int id) {
                    switch (id) {
                        default:
                        case 0:
                            DrawStyleBool.drawRect = true;//启用方框
                            DrawStyleBool.drawBox = false;//禁用包围盒
                            break;
                        case 1:
                            DrawStyleBool.drawRect = false;//禁用方框
                            DrawStyleBool.drawBox = true;//启用包围盒
                            break;
                    }
                }
            }, "方框", "包围盒");
        drawFold.endl();//折叠菜单换行

        //折叠菜单添加复选框
        a.CheckBox(drawFold, "坐标点")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    DrawStyleBool.drawPos = isSwitch;
                }
            }
        );

        //折叠菜单添加复选框
        a.CheckBox(drawFold, "背敌")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    DrawStyleBool.drawBack = isSwitch;
                }
            }
        );
        //折叠菜单添加复选框
        a.CheckBox(drawFold, "射线")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    DrawStyleBool.drawRay = isSwitch;
                }
            }
        );
        //折叠菜单添加复选框
        a.CheckBox(drawFold, "血量")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    DrawAttBool.drawHP = isSwitch;
                }
            }
        );
        //折叠菜单添加复选框
        a.CheckBox(drawFold, "名称")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    DrawAttBool.drawName = isSwitch;
                }
            }
        );
        //折叠菜单添加复选框
        a.CheckBox(drawFold, "信息")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    DrawAttBool.drawInfo = isSwitch;
                }
            }
        );
        drawFold.endl();//折叠菜单换行
        //折叠菜单添加复选框
        a.CheckBox(drawFold, "阴影")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    if (isSwitch) {
                        paint.setShadowLayer(8, 8, 8, Color.BLACK);
                    } else {
                        paint.clearShadowLayer();
                    }

                }
            }
        );
        a.TextHelp(drawFold, "?", "会给所有图形都添加阴影，这会消耗大量性能，掉帧严重，谨慎使用！");
        drawFold.endl();//折叠菜单换行
        //折叠菜单添加小数拖动条
        a.DragFloat1(drawFold, "X偏移{v}", -2000 , 0, 2000)
            .setCatCallback(new AlguiCallback.DragBar(){
                public void start(double p) {
                    xOffset = (float)p;
                    AlguiLog.d(TAG,"drag start："+p);
                }
                public void update(double p) {
                    xOffset = (float)p;
                    AlguiLog.d(TAG,"drag update："+p);
                }
                public void end(double p) {
                    xOffset = (float)p;
                    AlguiLog.d(TAG,"drag end："+p);
                }
            }
        ).setCatMargins(0, 0, 3, 0);//设置外边距

        //折叠菜单添加小数拖动条
        a.DragFloat1(drawFold, "Y偏移{v}", -2000 , 0, 2000)
            .setCatCallback(new AlguiCallback.DragBar(){
                public void start(double p) {
                    yOffset = (float)p;
                }
                public void update(double p) {
                    yOffset = (float)p;
                }
                public void end(double p) {
                    yOffset = (float)p;
                }
            }
        );
        drawFold.endl();
        
    
        return menu;
    }
    
    
}
