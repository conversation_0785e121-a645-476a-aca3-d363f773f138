package com.bytecat.algui.AlguiDemo;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/01/19 11:57
 * @Describe 游戏Mod作弊菜单示例 小白请调用此代码来显示成品：AlguiDemoModMenu.ShowGameModMenuDemo(aContext);
 */
import android.content.Context;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.ViewGroup;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewItem;
import com.bytecat.algui.AlguiViews.AlguiViewText;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;

public class AlguiDemoModMenu {
    
    public static final String TAG = "AlguiDemoModMenu";
    
    
    
    public static AlguiWinMenu show(final Context aContext) {
        //获取Gui简单快速构建器
        final AlguiV a = AlguiV.Get(aContext);

        //创建一个普通菜单窗口 下面进一步美化
        final AlguiWinMenu menu = a.WinMenu("游戏作弊菜单");
        menu.setCatMenuTitleTFAssets(null, Typeface.BOLD);//设置菜单标题文本字体粗体
        //menu.setCatBallImage("https://982.gov06.cn/upload/c3/990aca649b4c9f13044da85ea642e3.gif");//设置悬浮图标
        menu.setCatBallRadiu(50);//悬浮图标圆角
        menu.setCatEnableMenuSize(false);//禁用菜单自由设置大小
        menu.setCatMenuSize(200, 335);//设置菜单大小
        menu.setCatMenuBackColor(0xBE000000);//设置菜单背景颜色

        //美化菜单顶部布局
        menu.setCatMenuTitleSize(15);//设置标题大小
        menu.setCatMenuTitleColor(0xFFFFFFFF);//设置标题颜色
        menu.setCatMenuTopPadding(10, 11, 10, 11);//设置菜单顶部布局内边距
        menu.setCatMenuTopBackImage("MenuTopBack1.png", -1, -1);//设置菜单顶部背景纹理 图片，毛玻璃模糊，透明度

        //美化菜单导航栏并显示我们的信息
        menu.setCatMenuNavPadding(4, 3, 4, 3);//导航栏内边距
        menu.setCatMenuNavBackColor(0xFF090908);//导航栏背景颜色
        //这里我们创建一个滚动文本显示信息，父布局是菜单的导航栏 通过getByteMenuNavLayout方法获取导航栏布局
        AlguiViewText viceTitle = a.TextRoll(menu.getByteMenuNavLayout(), "作者：ByteCat 作者QQ3353484607 Algui交流群730967224");
        viceTitle.setCatTextRollSpeed(1);//设置滚动速度
        viceTitle.setCatTextTFAssets(null, Typeface.BOLD);//设置文本字体粗体
        viceTitle.setCatTextColor(0xFFFFFFFF);//设置文本颜色
        viceTitle.setCatTextSize(10);//设置文本大小

        //删除菜单关闭图标 下面我们自定义关闭按钮到菜单底部布局
        menu.setCatMenuEndIconImage(null);//删除菜单关闭图标 null代表没有图标
        //通过getByteMenuBottomLayout方法获取到菜单底部布局
        AlguiLinearLayout menuBottomlayout = menu.getByteMenuBottomLayout();//获取菜单底部布局
        //设置让菜单底部布局的内容居中对齐
        menuBottomlayout.setGravity(Gravity.CENTER);
        //设置菜单底部布局背景颜色
        menuBottomlayout.setCatBackColor(0xBE000000);
        //设置菜单底部布局内边距
        menuBottomlayout.setCatPadding(5, 5, 5, 5);
        //我们创建一个文本 将它添加进底部布局作为我们的关闭按钮
        AlguiViewText endButton=a.Text(menuBottomlayout, "关闭");
        //这里我们将文本的宽度设置为根据文本内容大小自动调整 防止影响到菜单长按移动 高度撑满底部布局扩大触摸范围
        endButton.setCatSize(AlguiLinearLayout.LayoutParams.WRAP_CONTENT, AlguiLinearLayout.LayoutParams.MATCH_PARENT);
        endButton.setCatTextSize(13);//设置文本大小
        endButton.setCatTextColor(0xFFFFFFFF);//设置文本颜色
        endButton.setCatTextTFAssets(null, Typeface.BOLD);//设置字体粗体
        //设置文本的触摸点击事件，让它点击后显示悬浮球，以实现关闭菜单
        endButton.setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    menu.showBall();//显示悬浮球
                }
            }
        );

        //我们让菜单显示和关闭时播放不同提示音，营造真实感
        menu.setCatMenuOpenCallback(new AlguiCallback.Click(){
                public void click(boolean isShowMenu) {
                    if (isShowMenu) {
                        //菜单显示时
                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.click);
                        AlguiViewItem.setCatSelectBackColor(0xFFFFFFFF);
                        AlguiViewItem.setCatSelectTextColor(0xFF000000);
                    } else {
                        //菜单关闭时
                        AlguiViewItem.setCatSelectBackColor(0xFF397CCD);
                        AlguiViewItem.setCatSelectTextColor(0xFFFFFFFF);
                    }
                }
            }
        );
        //至此美化结束，我们将得到一个很好看的作弊菜单

        //下面我们往菜单添加和菜单风格保持一致的按钮 进一步提升视觉效果
        //由于我们打造的菜单需要一行一个按钮，才整齐好看
        //所以为了避免麻烦的手动换行，我们将菜单每一行最大视图容量改为1这样就会自动换行
        menu.setCatMenuBufferLineMaxView(1);//设置菜单缓冲区一行最大视图容量
        //创建一个开关切换项
        //AlguiViewItem item=a.Switch(menu,"人物无敌");
        //上面普通开关添加进菜单会发现很丑，所以我们进一步美化
        //由于开关会有多个，我们没必要写重复的代码都美化一遍
        //所以我们封装成一个和菜单风格保持一致的开关方法
        //之后我们只需要传父布局和开关标题即可创建一个开关
        //(具体开关封装请看下面Switch方法内部)

        //我们设置全局所有项选中时的外观
        AlguiViewItem.setCatSelectBackColor(0xFFFFFFFF);//设置全局项选中时的背景颜色
        AlguiViewItem.setCatSelectTextColor(0xFF000000);//设置全局项选中时的文本颜色


        //我们往菜单添加一个我们封装的开关方法
        Switch(a, menu, "人物无敌")
            .setCatCallback(new AlguiCallback.Item(){
                public void item(int id) {
                    //我们在切换时播放一个音频营造真实感
                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.switch_metal);
                    //这里开关切换时根据ID判断是开还是关
                    //1是开，0是关
                    if (id == 1) {
                        //开关开启
                        //发送一个通知
                        a.WinSendInfo_White(AlguiAssets.Icon.inform_info2, "人物无敌", "开启成功", 5);

                    } else {
                        //开关关闭
                        //发送一个通知
                        a.WinSendInfo_White(AlguiAssets.Icon.inform_info2, "人物无敌", "关闭成功", 5);

                    }
                }
            }
        );
        //使用我们封装的开关方法往菜单添加一个开关
        Switch(a, menu, "人物飞天")
            .setCatCallback(new AlguiCallback.Item(){
                public void item(int id) {
                    //我们在切换时播放一个音频营造真实感
                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.switch_metal);
                    //这里开关切换时根据ID判断是开还是关
                    //1是开，0是关
                    if (id == 1) {
                        //开关开启
                        //发送一个通知
                        a.WinSendInfo_White(AlguiAssets.Icon.inform_info2, "人物飞天", "开启成功", 5);

                    } else {
                        //开关关闭
                        //发送一个通知
                        a.WinSendInfo_White(AlguiAssets.Icon.inform_info2, "人物飞天", "关闭成功", 5);

                    }
                }
            }
        );
        //下面我们继续封装一个可以切换多个选项而不是开启和关闭的按钮
        //(具体切换选项按钮封装请看下面SwitchItem方法内部)
        //使用我们封装的方法往菜单添加一个切换多选项按钮
        SwitchItem(a, menu, "人物加速", "x1", "x2", "x3")
            .setCatCallback(new AlguiCallback.Item(){
                public void item(int id) {
                    //我们在切换时播放一个音频营造真实感
                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.switch_metal);
                    //这里切换时根据ID判断是哪个选项
                    //由于我们封装时选项ID自动从0开始递增的
                    //所以"x1","x2","x3"的ID分别是应该是0,1,2
                    //然后我们显示通知看看是否切换了对应选项
                    switch (id) {
                        case 0:
                            //发送一个通知
                            a.WinSendInfo_White(AlguiAssets.Icon.inform_info2, "切换成功", "你选择了x1倍速", 5);
                            break;
                        case 1:
                            //发送一个通知
                            a.WinSendInfo_White(AlguiAssets.Icon.inform_info2, "切换成功", "你选择了x2倍速", 5);
                            break;
                        case 2:
                            //发送一个通知
                            a.WinSendInfo_White(AlguiAssets.Icon.inform_info2, "切换成功", "你选择了x3倍速", 5);
                            break;
                    }
                }
            }
        );
        menu.showMenu();//显示最终菜单
        
        //至此教程结束 有问题进Algui交流群642274897
        return menu;
    }
    //下面我们封装和菜单风格一致的开关创建方法
    private static AlguiViewItem Switch(AlguiV a, ViewGroup fatherLayout, CharSequence title) {
        //创建一个开关项
        AlguiViewItem item=a.Switch(fatherLayout, title);
        item.setCatPadding(7, 5, 7, 5);//设置开关内边距
        item.setCatTextColor(0xFFFFFFFF);//设置开关文本颜色白色
        item.setCatBackColor(0);//设置开关背景颜色透明
        item.setCatTextSize(11);//设置开关文本大小
        return item;//然后我们返回美化好的开关
    }
    //下面我们封装和菜单风格一致的切换选项按钮
    //这里由于选项我们只需要文本作为选项
    //所以我们简化传入的参数只需要传入字符串文本即可自动添加对应文本选项
    private static AlguiViewItem SwitchItem(AlguiV a, ViewGroup fatherLayout, CharSequence title, String... str) {
        //老样子，还是和之前的开关封装一样，很简单，美化和开关相同
        //创建一个切换选项按钮
        AlguiViewItem item=a.SwitchItem(fatherLayout, title);
        item.setCatPadding(7, 5, 7, 5);//设置内边距
        item.setCatTextColor(0xFFFFFFFF);//设置文本颜色
        item.setCatBackColor(0);//设置背景颜色
        item.setCatTextSize(11);//设置文本大小
        //我们遍历传入的所有文本 将每个文本作为一个选项添加进选项按钮
        int id=0;//文本选项的ID 这里我们自动从0开始依次给每个文本选项设置ID
        for (String string:str) {
            if (string != null) {
                //文本不为空我们就往选项按钮添加一个选项
                //创建一个文本视图作为选项 并美化文本选项外观 和菜单保持一致
                //父布局传null不设置父布局，之后我们添加进选项按钮
                AlguiViewText text = a.Text(null, string);
                text.setCatTextColor(0xFFFFFFFF);//设置文本颜色
                text.setCatTextSize(11);//设置文本大小
                item.addItem(id, text);//将文本添加进选项并设置ID
                //文本添加完成将ID递增 让下一个选项的ID为1，之后2以此类推
                id++;
            }
        }
        return item;//然后返回我们美化好的切换选项方法
    }
    
    
}
