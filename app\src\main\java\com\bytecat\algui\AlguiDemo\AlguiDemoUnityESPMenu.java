package com.bytecat.algui.AlguiDemo;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/01/23 20:07
 * @Describe Unity3D游戏通用绘制模板示例 游戏：香肠派对
 */
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.SurfaceHolder;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewFoldMenu;
import com.bytecat.algui.AlguiWindows.AlguiWinDraw;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import java.util.ArrayList;

public class AlguiDemoUnityESPMenu {

    public static final String TAG = "AlguiDemoUnityESPMenu";


    //Unity碰撞体结构
    private static class Unity_Collision {
        long address=-1;//内存地址
        int flag=-1;//Unity碰撞体通用标识符
        String name="碰撞体";//绘制名称
        int color=0xFF000000;//绘制颜色
        int textColor=0xFFFFFFFF;//绘制文本颜色
        int ID1=-1;//ID1
        int ID2=-1;//ID2
    }


    //调试菜单
    private static long INTERVAL = 30000; //更新延迟时间(ms)
    private static boolean isDrawALLColl;//是否绘制所有碰撞体
    private static boolean isSieveFlag;//是否过滤flag
    private static int collflag;//要过滤的flag值


    //初始化绘制
    private static AlguiWinDraw initDraw(Context aContext) {
        //画笔
        final Paint paint = new Paint();
        paint.setAntiAlias(true);  
        paint.setStrokeWidth(3);
        paint.setTextAlign(Paint.Align.CENTER);//文本中心作为文本位置


        AlguiLog.setLogRepeatable(false);//不可重复写入日志

        //创建Algui动态帧绘制的窗口
        final AlguiWinDraw draw = new AlguiWinDraw(aContext);
        draw.setCatCallback(new AlguiCallback.Draw(){
                int px,py,pxMidpoint,pyMidpoint;//画布分辨率和画布中心点坐标
                final int ARM=64;//游戏位数(👀自己设置)
                //下面不用管
                
                final int P_SIZE=ARM / 8;//指针大小 x/8
                final long O_CASIZE = ARM == 64 ?0x2C: 0x20;//碰撞体数量
                final long O_X=ARM == 64 ?0xA0: 0x40;//x
                final long O_Z=O_X + 0x4;//z
                final long O_Y=O_Z + 0x4;//y
                long lastExecutionTime;//上一次更新时间戳
                long matrixsAddr;//矩阵数组
                long collAddr;//碰撞体数组
                long collAddrStart;//碰撞体数组起始地址
                float[] matrix = new float[4 * 4];//矩阵数组 这里4x4矩阵
                ArrayList<Unity_Collision> collList = new ArrayList<>();//碰撞体列表
                public long jump(long addr) {return AlguiMemTool.jump(addr, P_SIZE);}//跳转指针

                //第一帧调用初始化
                //返回true代表初始化完成开始下一帧更新 
                //返回false代表初始化失败将锁定在此帧一直初始化并检查直到返回true才开始下一帧更新
                public boolean Start(Canvas canvas) {

                    //设置游戏包名(👀自己设置)
                    int pid = AlguiMemTool.setPackageName("com.sofunny.Sausage");
                    if (pid <= 0)return false;

                    //获取矩阵数组头(👀自己设置)
                    long module = AlguiMemTool.getModuleBaseAddr("libunity.so", AlguiMemTool.HEAD_CB);
                    matrixsAddr = jump(jump(jump(module + 0x92900) + 0x0) + 0x40) + 0x2E4;
                    if (module <= 0 || matrixsAddr <= 0)return false;

                    //获取碰撞体数组(👀自己设置)
                    collAddr = jump(module + 0xA5508) + 0x3F8;//碰撞体数组基址
                    if (module <= 0 || collAddr <= 0)return false;
                    collAddrStart = jump(collAddr);//碰撞体数组头基址

                    lastExecutionTime = 0;

                    /*AlguiLog.d(TAG,
                     "\n绘制初始化完成：" +
                     "\n进程PID：%d" + 
                     "\n模块起始地址：0x%08X" +
                     "\n玩家数组头地址：0x%08X" + 
                     "\n矩阵头地址：0x%08X"
                     , pid, module,  playersAddr, matrixsAddr
                     );*/
                    //初始化成功
                    return true;
                }

                //每一帧调用更新 
                //返回true代表更新完成开始下一帧更新 
                //返回false代表更新失败会跳转到Start函数检查是否初始化完成
                public boolean Update(Canvas canvas) {
                    long currentTime = System.currentTimeMillis();//获取系统时间
                    //每过一段时间进行更新对象列表
                    if (currentTime - lastExecutionTime >= INTERVAL) {
                        collList.clear();//清除碰撞体列表
                        //获取碰撞体数量
                        int size = Integer.parseInt(AlguiMemTool.getMemoryAddrData(collAddr + O_CASIZE, AlguiMemTool.TYPE_DWORD));
                        //遍历碰撞体数组 进行静态存储
                        for (int i=0;i < size;i++) {
                            Unity_Collision p=new Unity_Collision();
                            p.address = jump(collAddrStart + i * P_SIZE);
                            float x = Float.parseFloat(AlguiMemTool.getMemoryAddrData(p.address + O_X, AlguiMemTool.TYPE_FLOAT));
                            if (x == 0)continue;//跳过无用
                            p.flag = Integer.parseInt(AlguiMemTool.getMemoryAddrData(p.address + P_SIZE, AlguiMemTool.TYPE_DWORD));
                            //获取用于过滤的特征ID地址(可选 自己找过滤特征)
                            //p.ID1 = Integer.parseInt(AlguiMemTool.getMemoryAddrData(jump(jump(p.address + 0x28) + 0x10) + 0x90, AlguiMemTool.TYPE_DWORD));
                            //p.ID2 = Integer.parseInt(AlguiMemTool.getMemoryAddrData(jump(jump(p.address + 0x28) + 0x10) + 0xC, AlguiMemTool.TYPE_DWORD));
                            /*if (p.flag == 196614 && p.ID1 == 457972) {
                             p.name = "玩家";
                             p.color = 0xFF43A047;
                             collList.add(p);
                             continue;
                             }*/
                            if (isDrawALLColl) {
                                if (isSieveFlag) {
                                    if (p.flag == collflag) {
                                        //对于过滤调试
                                        //p.name = String.format("地址：0x%08X FLAG：%d ID1：%d ID2：%d", p.address, p.flag, p.ID1, p.ID2);
                                        p.name = String.format("0x%08X", p.address);
                                        collList.add(p);
                                    }
                                } else {
                                    //对于过滤调试
                                    //p.name = String.format("地址：0x%08X FLAG：%d ID1：%d ID2：%d", p.address, p.flag, p.ID1, p.ID2);
                                   p.name = String.format("0x%08X", p.address);
                                    collList.add(p);
                                }
                            }
                        }
                        //更新上次执行时间
                        lastExecutionTime = currentTime;
                    }

                    if (collList.size() <= 0)return false;//碰撞体空列表则不继续更新，减少CPU占用

                    //绘制画布边界(调试)
                    /* paint.setColor(Color.RED);
                     paint.setStyle(Paint.Style.STROKE); 
                     paint.setStrokeWidth(2); 
                     canvas.drawRect(0, 0, px, py, paint);

                     paint.setColor(Color.RED);
                     paint.setStyle(Paint.Style.FILL); 
                     paint.setTextAlign(Paint.Align.LEFT);
                     canvas.drawText("time：" + time + " | 数量：" + collList.size(), 50, 50, paint);
                     */
                    //获取当前矩阵
                    for (int x = 0; x < matrix.length; x++) {
                        matrix[x]  = Float.parseFloat(AlguiMemTool.getMemoryAddrData(matrixsAddr + x * 4, AlguiMemTool.TYPE_FLOAT));
                    }
                    //绘制碰撞体
                    for (Unity_Collision coll:collList) {
                        //获取当前玩家xzy 3D坐标
                        float x = Float.parseFloat(AlguiMemTool.getMemoryAddrData(coll.address + O_X, AlguiMemTool.TYPE_FLOAT));
                        float z = Float.parseFloat(AlguiMemTool.getMemoryAddrData(coll.address + O_Z, AlguiMemTool.TYPE_FLOAT));
                        float y = Float.parseFloat(AlguiMemTool.getMemoryAddrData(coll.address + O_Y, AlguiMemTool.TYPE_FLOAT));
                        //将当前3D坐标利用摄像机矩阵转换到摄像机坐标
                        float cz = matrix[3] * x + matrix[7] * z + matrix[11] * y + matrix[15];
                        if (cz <= 0) continue; //在摄像机后面则跳过
                        if (cz == 0) cz = 1;  //防止后面除零异常
                        float cx = pxMidpoint + (matrix[0] * x + matrix[4] * z + matrix[8] * y + matrix[12]) / cz * pxMidpoint;
                        float cy = pyMidpoint - (matrix[1] * x + matrix[5] * z + matrix[9] * y + matrix[13]) / cz * pyMidpoint;


                        //超出屏幕则只绘制在边界上
                        if (cx > px || cx < 0 || cy > py || cy < 0) {
                            //背敌显示(可选)
                            /*float drawX = Math.max(0, Math.min(cx, px));
                             float drawY = Math.max(0, Math.min(cy, py)); 
                             paint.setColor(0xFF424242);
                             canvas.drawLine(pxMidpoint, 0, drawX, drawY, paint);
                             paint.setColor(coll.color);
                             canvas. drawCircle(drawX, drawY, 15, paint);*/
                            continue;
                        }
                        //射线(可选)
                        //paint.setColor(coll.color);
                        //canvas.drawLine(pxMidpoint, 0, cx, cy, paint);
                        DrawRectText(canvas, coll.name , coll.color, coll.textColor, 18, cx, cy);
                    }

                    DrawRectText(canvas, "数量：" + collList.size() + " | 下次更新倒计时：" + (INTERVAL - (currentTime - lastExecutionTime)) / 1000 + "秒", 0xFF424242, 0xFFFFFFFF, 15, pxMidpoint, 10);

                    return true;//更新完成开始下一帧更新
                }

                //渲染线程结束时调用
                public void End(SurfaceHolder holder) {}

                //更新画布大小时调用
                public void UpdateCanvasSize(SurfaceHolder holder, int format, int width, int height) {
                    px = width;
                    py = height;
                    pxMidpoint = px / 2;
                    pyMidpoint = py / 2;
                }

                //绘制矩形文本 画布，文本，背景颜色，文本颜色，文本大小，x，y
                public void DrawRectText(Canvas canvas, String text, int backColor, int textColor, int size, float cx, float cy) {
                    paint.setTextSize(size);
                    float textWidth = paint.measureText(text);
                    float textHeight = paint.getTextSize();
                    float padding=3;//内边距
                    float rectLeft = cx - textWidth / 2 - padding; 
                    float rectTop = cy - textHeight / 2 - padding; 
                    float rectRight = cx + textWidth / 2 + padding; 
                    float rectBottom = cy + textHeight / 2 + padding; 
                    paint.setColor(backColor);
                    canvas.drawRect(rectLeft, rectTop, rectRight, rectBottom, paint);
                    paint.setColor(textColor); 
                    canvas.drawText(text, cx, cy + textHeight / 3, paint); 
                }
            }
        );
        return draw;
    }


    //显示菜单
    public static AlguiWinMenu show(final Context aContext) {

        AlguiV a=AlguiV.Get(aContext);//获取UI构建器
        AlguiWinMenu menu = a.WinMenu("Algui-UnityESP-Demo");//创建菜单
        menu.setCatMenuBufferLineMargins(3, 3, 3, 0);//设置菜单每行的外边距

        final AlguiWinDraw draw  = initDraw(aContext);

        a.ButtonLong(menu, "停止渲染")
            .setCatBackColor(0xCEEF5350)
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    draw.endDraw();
                }
            }
        );

        menu.endl();

        //主菜单添加一个折叠菜单
        AlguiViewFoldMenu debugFold= a.FoldMenu(menu, "调试");
        debugFold.setCatLineMargins(0, 3, 0, 0);//设置折叠菜单每行的外边距

        a.CheckBox(debugFold, "绘制所有对象")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    isDrawALLColl = isSwitch;
                    draw.startDraw();
                }
            }
        );
        debugFold.endl();
        a.CheckBox(debugFold, null)
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    isSieveFlag = isSwitch;
                    draw.startDraw();
                }
            }
        );
        a.DragInt(debugFold, "过滤 FLAG={v}", 196610 , 196610, 196620)
            .setCatCallback(new AlguiCallback.DragBar(){
                public void start(double p) {

                }
                public void update(double p) {

                }
                public void end(double p) {
                    collflag = (int)p;
                    draw.startDraw();
                }
            }
        );
        debugFold.endl();
        a.TextHelp(debugFold, "帮助", "对于绘制数量过多时，帧更新可能发生卡顿，请使用此设置来配置更新延迟时间，但是这可能会导致未及时绘制游戏新创建的对象，因为我们要等待到达这段延迟后才更新绘制列表！");
        a.DragInt(debugFold, "更新延迟{v}秒", 0 , (int)INTERVAL/1000, 60)
            .setCatCallback(new AlguiCallback.DragBar(){
                public void start(double p) {
                    INTERVAL=(int)p*1000;
                }
                public void update(double p) {
                    INTERVAL=(int)p*1000;
                }
                public void end(double p) {
                    INTERVAL=(int)p*1000;
                    draw.startDraw();
                }
            }
        );
        


        return menu;
    }

}
