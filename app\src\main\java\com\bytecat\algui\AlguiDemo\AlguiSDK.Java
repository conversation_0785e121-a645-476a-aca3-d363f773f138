package com.bytecat.algui.AlguiDemo;

import android.content.Context;
import android.text.InputType;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiActivity;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiViews.AlguiFlowLayout;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiViewImage;
import com.bytecat.algui.AlguiViews.AlguiViewInputBox;
import com.bytecat.algui.AlguiViews.AlguiViewLine;
import com.bytecat.algui.AlguiViews.AlguiViewSoundWave;
import com.bytecat.algui.AlguiViews.AlguiViewText;
import com.bytecat.algui.AlguiViews.AlguiViewTriangle;
import com.bytecat.algui.AlguiViews.AlguiViewWeb;
import com.bytecat.algui.AlguiWindows.AlguiWinDialog;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.bytecat.algui.AlguiWindows.AlguiWindow;


/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/29 03:39
 * @Describe Algui-SDK文档 
 * 只展示一部分组件的教程，因为前缀Algui的组件都差不多只要使用setCat和getByte来设置和获取
 * 说白了作者写到一半不想写了，太多内容了注释都要半天 =͟͟͞͞(꒪⚇꒪*) 非常抱歉！
 */
//每个Algui组件前缀setCat...方法来修改该组件属性
//1.内部进行了dp转px自适应外部无需转换
//2.内部均处理了空指针，无需二次判空
//3.对于没有Cat前缀的set方法不会自动处理一些转换和异常，或者是安卓提供的方法

//对于拓展：每个Algui组件前缀getByte...方法来获取与该组件绑定的一些子对象然后设置对应子对象的属性

public class AlguiSDK {
    public static final String TAG = "AlguiSDK";
    private AlguiSDK() {throw new UnsupportedOperationException("AlguiSDK文档仅供查阅 ByteCat"); }
    Context aContext=null;

    //====================================================窗口====================================================
    private class Window {
        //菜单窗口 AlguiWinMenu
        public void AlguiWinMenu() {
            AlguiWinMenu menu = new AlguiWinMenu(aContext)
                //窗口
                .setCatWinActivity(aContext)//设置该窗口显示在哪个活动中，支持随时更改(可以HOOK传其它程序的活动即可将此窗口注入进该程序指定活动)
                .setCatWinPos(0, 0)//设置窗口相对屏幕左上角的坐标
                .setCatEnableWinMove(true)//是否启用窗口长按动态移动
                .setCatWinTransparent(0)//设置窗口透明度0-1
                .setCatWinBrightness(0)//设置窗口显示时屏幕的亮度0-1
                .setCatWinAnimations(AlguiWindow.Animations.Animation_Translucent)//设置窗口启动退出动画
                .setCatWinFlags(WindowManager.LayoutParams.FLAG_SECURE | WindowManager.LayoutParams.FLAG_DITHER)//设置一些窗口特性 (比如这里设置防截屏防录屏)
                .addWinFlag(WindowManager.LayoutParams.FLAG_DITHER)//添加一个窗口特性(比如这里添加防录屏)
                .remWinFlag(WindowManager.LayoutParams.FLAG_DITHER)//移除一个窗口特性(比如这里移除防录屏)
                .showWin()//显示窗口
                .updateWin()//更新窗口
                .hideWin()//隐藏或删除窗口
                //悬浮球
                .setCatBallImage("src")//设置悬浮球图片支持：png,jpg,gif... && 网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
                .setCatBallSize(0, 0)//设置悬浮球大小
                .setCatBallRadiu(0)//设置悬浮球圆角半径
                .setCatBallTransparency(0)//设置悬浮球透明度0-255
                .setCatBallColor(0)//设置悬浮球颜色 (仅支持纯色图片)
                .setCatBallBlur(0)//设置悬浮球毛玻璃模糊半径
                .showBall()//显示悬浮球
                //菜单
                .setCatEnableMenuOutsideEnd(true)//是否启用点击外部自动关闭菜单(如果关闭，那么菜单显示时其它程序没有输入法)
                .setCatMenuTransparent(0)//设置菜单透明度0-1
                .setCatMenuBackColor(0)//设置菜单背景颜色(传多个颜色时自动渐变)
                .setCatMenuBackImage("src", 0, 0)//设置菜单背景图片 参数：图片资源(支持：网络链接，base64，本地文件路径，assets文件夹的图片文件名)，图片毛玻璃模糊半径，图片透明度0-255
                .setCatMenuBorder(0, 0)//设置菜单描边大小和颜色
                .setCatMenuRadiu(0)//设置菜单圆角半径
                .setCatMenuMinSize(100, 100)//设置菜单最小大小
                .setCatMenuSize(200, 200)//设置菜单大小
                .setCatEnableMenuSize(true)//是否启用菜单大小动态调节器
                .showMenu()//显示菜单
                //菜单&顶部布局
                .setCatEnableMenuTitle(true)//是否启用菜单顶部标题栏
                .setCatMenuTopBackColor(0)//设置菜单顶部背景颜色(传多个颜色则自动渐变)
                .setCatMenuTopBackImage("src", 0, 0)//设置菜单顶部背景图片(注意：设置后圆角描边背景颜色失效，null恢复) 参数：图片资源(支持：网络链接，base64，本地文件路径，assets文件夹的图片文件名)，图片毛玻璃模糊半径，图片透明度0-255
                .setCatMenuTopPadding(0, 0, 0, 0)//设置菜单顶部内边距 (左上右下)
                .setCatMenuTopBorder(0, 0)//设置菜单顶部描边大小和颜色
                .setCatMenuTopRadiu(0)//设置菜单顶部圆角半径
                //菜单&顶部布局&图标
                .setCatMenuIconImage("src")//设置菜单图标 (null无图标) 支持：png,jpg,gif... && 网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
                .setCatMenuIconColor(0)//设置菜单图标颜色(仅支持纯色图片)
                .setCatMenuIconBlur(0)//设置菜单图标毛玻璃模糊半径
                .setCatMenuIconRadiu(0)//设置菜单图标圆角半径
                //菜单&顶部布局&标题
                .setCatMenuTitle("标题")//设置菜单标题文本 (null无标题) (支持自动识别HTML文本，支持格式化变量到文本)
                .setCatMenuTitleTFAssets("assets文件夹字体文件名")//设置菜单标题字体(传assets文件夹的字体文件名)
                .setCatMenuTitleSize(0)//设置菜单标题大小 (顶部内容都跟随标题大小动态改变大小)
                .setCatMenuTitleColor(0)//设置菜单标题颜色(传多个颜色则自动渐变)
                .setCatMenuTitleMoveGrad(false)//设置菜单标题动态渐变(启用后将使标题的渐变颜色跑马灯动起来)
                .setCatMenuTitleGlow(0, 0)//设置菜单标题阴影半径和颜色
                //菜单&顶部布局&关闭菜单图标
                .setCatMenuEndIconImage("src")//设置菜单关闭图标 (null无关闭图标)支持：png,jpg,gif... && 网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
                .setCatMenuEndIconColor(0)//设置菜单关闭图标颜色(仅支持纯色图片)
                .setCatMenuEndIconBlur(0)//设置菜单关闭图标毛玻璃模糊半径
                .setCatMenuEndIconRadiu(0)//设置菜单关闭图标圆角半径
                //菜单&顶部导航区(流式布局)
                .setCatMenuNavOrientation(AlguiFlowLayout.HORIZONTAL) //设置导航区方向(垂直=父容器横向添加每行，每行垂直添加视图 & 横向=父容器垂直添加每行，每行横向添加视图)
                .setCatMenuNavPadding(0, 0, 0, 0)  //设置导航区内边距 (左上右下)
                .setCatMenuNavBackColor(0)  //设置导航区背景颜色(传多个颜色则自动渐变)
                .setCatMenuNavBorder(0, 0)  //设置导航区描边大小和颜色
                .setCatMenuNavRadiu(0)  //设置导航区圆角半径
                .setCatMenuNavLineMaxView(0)  //设置导航区每行最大视图数量(超出则自动换行，0则不换行需手动换行)
                //.endl_Nav()//代表导航区这一行结束开始换行(手动换行)
                .addView_Nav()//向导航区当前行添加一些视图(中途null代表换行)
                .remView_Nav()  //从导航区删除一些视图
                .addViewToLine_Nav(0, null)  //在导航区指定行添加一些视图(比如在第0行添加视图)
                .remViewToLine_Nav(0, null)//从导航区指定行删除一些视图(比如删除第0行的视图)
                .remLine_Nav(0)//删除导航区指定行(按索引)
                .remLine_Nav(new AlguiLinearLayout(aContext))//删除导航区指定行(按对象)
                .remAllView_Nav()//删除导航区中的所有视图
                //菜单&滚动列表
                .setCatMenuListBackColor(0)//设置菜单滚动列表背景颜色
                //菜单&滚动列表&缓冲区(流式布局)
                .setCatMenuBufferOrientation(AlguiFlowLayout.HORIZONTAL) //设置缓冲区方向(垂直=父容器横向添加每行，每行垂直添加视图 & 横向=父容器垂直添加每行，每行横向添加视图)
                .setCatMenuBufferPadding(0, 0, 0, 0)  //设置缓冲区内边距 (左上右下)
                .setCatMenuBufferBackColor(0)  //设置缓冲区背景颜色(传多个颜色则自动渐变)
                .setCatMenuBufferBorder(0, 0)  //设置缓冲区描边大小和颜色
                .setCatMenuBufferRadiu(0)  //设置缓冲区圆角半径
                .setCatMenuBufferLineMaxView(0)  //设置缓冲区每行最大视图数量(超出则自动换行，0则不换行需手动换行)
                //.endl()//代表缓冲区这一行结束开始换行(手动换行)
                .addView()//向缓冲区当前行添加一些视图(中途null代表换行)
                .remView()  //从缓冲区删除一些视图
                .addViewToLine(0, null)  //在缓冲区指定行添加一些视图(比如在第0行添加视图)
                .remViewToLine(0, null)//从缓冲区指定行删除一些视图(比如删除第0行的视图)
                .remLine(0)//删除缓冲区指定行(按索引也可按对象)
                .remAllView()//删除缓冲区中的所有视图
                //菜单&底部布局
                .setCatMenuBottomBackColor(0)  //设置菜单底部背景颜色(传多个颜色则自动渐变)
                .setCatMenuBottomBorder(0, 0)  // 设置菜单底部描边大小和颜色
                .setCatMenuBottomRadiu(0)  //设置菜单底部圆角半径
                //菜单&底部布局&直角(菜单大小调节器)
                .setCatMenuTriangleColor(0)  //设置菜单直角颜色
                .setCatMenuTriangleSize(0)  //设置菜单直角大小
                ;
            //设置菜单打开关闭回调监听器
            menu.setCatMenuOpenCallback(new AlguiCallback.Click(){
                    //菜单显示或隐藏时调用
                    public void click(boolean isShowMenu) {
                        if (isShowMenu) {
                            AlguiLog.i(TAG,"菜单显示");
                        } else {
                            AlguiLog.i(TAG,"菜单隐藏");
                        }
                    }
                }
            );
        }


        //对话框窗口️ AlguiWinDialog
        public void AlguiWinDialog() {
            final AlguiWinDialog dialog= new AlguiWinDialog(aContext)
                .setCatIsCanEnd(true)//是否可以关闭对话框(禁用后去除关闭按钮并无法点击外部关闭)
                .setCatColor(0, 0)//设置对话框颜色(背景色和前景色)
                .setCatStyle(AlguiWinDialog.Style.BLACK)//主题样式
                .setCatIcon(AlguiAssets.Icon.inform_info)//图标(null没有图标) 支持：png,jpg,gif... && 网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
                .setCatTitle("标题")//标题文本(支持自动识别HTML文本，支持格式化变量到文本)
                //内容布局(流式布局)
                .setCatOrientation(AlguiFlowLayout.HORIZONTAL) //设置内容布局方向(垂直=父容器横向添加每行，每行垂直添加视图 & 横向=父容器垂直添加每行，每行横向添加视图)
                .setCatLineMaxView(0)//设置内容布局每行最大视图数量(超出则自动换行，0则不换行需手动换行)
                //.endl()//代表内容布局这一行结束开始换行(手动换行)
                .addView()//向内容布局当前行添加一些视图(中途null代表换行)
                .remView()  //从内容布局删除一些视图
                .addViewToLine(0, null)  //在内容布局指定行添加一些视图(比如在第0行添加视图)
                .remViewToLine(0, null)//从内容布局指定行删除一些视图(比如删除第0行的视图)
                .remLine(0)//删除内容布局指定行(按索引也可按对象)
                .remAllView()//删除内容布局中的所有视图
                .setCatNoButtonText("取消")//消极按钮文本 (null没有消极按钮)
                .setCatYesButtonText("确定")//积极按钮文本(null没有积极按钮)
                .show()//显示对话框
                .end();//结束对话框
            //设置消极按钮点击回调监听器
            dialog.setCatNoButtonClick(new AlguiCallback.Click(){
                    //点击时调用
                    public void click(boolean b) {
                        dialog.end();//结束对话框
                    }
                }
            );
            //设置积极按钮点击回调监听器
            dialog.setCatYesButtonClick(new AlguiCallback.Click(){
                    //点击时调用
                    public void click(boolean b) {
                        dialog.end();//结束对话框
                    }
                }
            );
        }

        //通知 AlguiWinInform 单例🅾
        public void AlguiWinInform() {
            //设置通知窗口显示在哪个活动中，支持随时更改(可以HOOK传其它程序的活动即可将此窗口注入进该程序指定活动)
            AlguiWinInform.Get(aContext).setCatActivity(aContext);
            //设置通知窗口显示在哪个位置(比如这里显示在屏幕右下角)
            AlguiWinInform.Get(aContext).setCatPos(Gravity.BOTTOM | Gravity.END);
            //清除通知窗口所有通知
            AlguiWinInform.Get(aContext).clear();

            //向通知窗口发送一个预设的黑色主题的通知 参数：图片，标题，信息，显示时间(秒) null代表不显示这个视图 比如图片传null则不显示图片
            AlguiWinInform.Get(aContext).showInfo_Black("网络链接 或 base64 或 本地路径 或 文件名(assets文件夹的)", "标题", "信息", 3);
            //向通知窗口发送一个预设的白色主题的通知 参数：图片，标题，信息，显示时间(秒) null代表不显示这个视图 比如图片传null则不显示图片
            AlguiWinInform.Get(aContext).showInfo_White("网络链接 或 base64 或 本地路径 或 文件名(assets文件夹的)", "标题", "信息", 3);
            //向通知窗口发送一个预设的蓝色主题的通知 参数：图片，标题，信息，显示时间(秒) null代表不显示这个视图 比如图片传null则不显示图片
            AlguiWinInform.Get(aContext).showInfo_Blue("网络链接 或 base64 或 本地路径 或 文件名(assets文件夹的)", "标题", "信息", 3);

            //向通知窗口发送一个自定义样式的通知
            AlguiWinInform.Get(aContext).showInfo(
                //通知主布局外观
                0xFF303030, //背景颜色
                7, //圆角半径
                0.25f, 0xD0FFFFFF,//描边大小颜色

                //图标外观
                "网络链接 或 base64 或 本地路径 或 文件名(assets文件夹的)",//图标
                0xD0FFFFFF, //颜色(只支持单色图标)
                0,//圆角半径

                //标题外观
                "标题", //文本
                0xD0FFFFFF, //文本颜色
                null,//文本字体

                //信息外观
                "信息", //文本
                0x60FFFFFF, //文本颜色
                null,//文本字体

                //显示时间(秒)
                3
            );

            //向通知窗口发送一个完全自定义视图的通知 参数：显示时间，一些视图
            AlguiWinInform.Get(aContext).showInfo(3, null);
        }


    }







    //====================================================视图====================================================
    private class View {

        
        //文本 AlguiViewText
        public void AlguiViewText() {
            AlguiViewText textView = new AlguiViewText(aContext)
                .setCatSize(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT)//视图大小
                .setCatWeight(0)//权重 用于此视图和与同父布局的其它视图的占用大小比例
                .setCatPadding(0, 0, 0, 0)//内边距(左上右下)
                .setCatMargins(0, 0, 0, 0)//外边距(左上右下)
                .setCatBackColor(0)//背景颜色(传多个颜色则自动渐变)
                .setCatRadiu(0)//圆角半径
                .setCatBorder(0, 0)//描边大小和颜色
                .setCatText("文本")//文本(支持自动识别HTML文本，支持格式化变量到文本)
                .setCatTextSize(7)//文本大小
                .setCatTextTFAssets("assets文件夹字体文件名称")//文本字体(传assets文件夹字体文件名)
                .setCatTextColor(0xFFADB1B7)//文本颜色(传多个颜色则自动渐变)
                .setCatTextGlow(0, 0)//文本阴影半径和颜色
                .setCatTextMoveGrad(false)//是否启用文本动态渐变(启用后将使文本的渐变颜色跑马灯动起来)
                .setCatTextGradSpeed(1)//动态渐变跑马灯速度
                .setCatTextGravity(Gravity.LEFT)//文本对齐位置(需确保视图宽高合适)
                .setCatTextIsLink(false)//是否自动识别文本中的网络链接(启用则自动转换链接可点击跳转)
                .setCatTextRoll(false)//是否启用文本弹幕滚动(只支持横向，确保视图宽度合适)
                .setCatTextRollSpeed(2)//文本弹幕滚动速度
                .callClick(true)//手动执行点击监听器
                .setCatParentLayout(null)//父布局
                ;
            //设置文本点击回调监听器
            textView.setCatCallback(new AlguiCallback.Click(){
                    //点击时调用
                    public void click(boolean b) {

                    }
                }
            );

        }
        
        //图像 AlguiViewImage
        public void AlguiViewImage() {
            AlguiViewImage img=new AlguiViewImage(aContext)
                .setCatImage("img")//通用设置图片方法 支持：png,jpg,gif... && 网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
                .setCatImageURL("url")//设置网络图片
                .setCatImageBase64("base64")//设置base64编码图片
                .setCatImageFile("path")//设置本地图片 (纯文件名则识别为assets文件夹下的图片文件或assets/img.png)
                .setCatDrawable(null)//设置Drawable图片
                .setCatSize(40, 40)//图片大小(宽高)
                .setCatSize(1)//图片百分比大小 例如：0.5则缩放到50% (按原始尺寸进行百分比缩放)
                .setCatWeight(0)//权重 用于此视图和与同父布局的其它视图的占用大小比例
                .setCatMargins(0, 0, 0, 0)//外边距(左上右下)
                .setCatRadiu(0)//圆角半径
                .setCatTransparent(0)//透明度
                .setCatColor(0)//设置图片颜色(仅支持纯色图片)
                .setCatBlur(0)//设置图片毛玻璃模糊半径(不支持gif动态图模糊)
                .setCatCallback(null)//设置图片点击回调监听器
                .callClick(true)//手动调用点击回调监听器
                .setCatParentLayout(null)//设置父布局
                ;
            img.downloadImage("路径");//下载当前图像到本地
        }
        
        //输入框 AlguiViewInputBox
        public void AlguiViewInputBox() {
            //[只提供关键设置方法，对于自定义拓展 通过.getByte...获取和此组件绑定的各种对象然后设置对应对象的样式]
            AlguiViewInputBox input=new AlguiViewInputBox(aContext)
                .setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT)//视图大小
                .setCatWeight(1)//权重 用于此视图和与同父布局的其它视图的占用大小比例
                .setCatPadding(0, 0, 0, 0)//内边距(左上右下)
                .setCatMargins(0, 0, 0, 0)//外边距(左上右下)
                .setCatBackColor(0)//背景颜色(传多个颜色则自动渐变)
                .setCatRadiu(0)//圆角半径
                .setCatBorder(0, 0)//描边大小和颜色
                .setCatIcon("imgsrc")//设置输入框图标(null代表没有图标) 支持：png,jpg,gif... && 网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
                .setCatIconTransparent(0)//图标透明度
                .setCatIconRadiu(0)//图标圆角半径
                .setCatIconColor(0)//图标颜色(仅支持纯色图标)
                .setCatIconBlur(0)//图标毛玻璃模糊半径
                .setCatInputHint("请输入文本")//设置输入框提示文本
                .setCatInputHintColor(0)//提示文本颜色
                .setCatInputText("")//设置输入框输入内容
                .setCatInputTextColor(0)//输入框输入内容颜色
                .setCatTextSize(0)//设置输入框文本大小(图标和按钮大小都跟随文本大小)
                .setCatTextTFAssets("assets文件夹的字体文件名称")//设置输入框文本字体(assets文件夹的字体文件名)
                .setCatButtonBackColor(0)//按钮背景颜色(传多个颜色则自动渐变)
                .setCatButtonBorder(0, 0)//按钮描边大小和颜色
                .setCatButtonRadiu(0)//按钮圆角半径
                .setCatButtonText("确定")//设置按钮文本(null代表没有按钮)
                .setCatButtonTextColor(0)//按钮文本颜色(传多个颜色则自动渐变)
                .setCatButtonTextMoveGrad(false)//是否启用按钮文本动态渐变(启用后将使文本的渐变颜色跑马灯动起来)
                .setCatButtonTextGlow(0, 0)//按钮文本阴影和颜色
                .setCatParentLayout(null)//设置父布局
                ;
            //输入类型：
            //InputType.
            // TYPE_CLASS_TEXT - 用于普通文本输入。适用于大多数文本输入框，显示常规的字符输入键盘。
            // TYPE_TEXT_VARIATION_PASSWORD - 用于密码输入。输入内容会以星号（或其他符号）隐藏，适用于密码输入框。
            // TYPE_TEXT_VARIATION_VISIBLE_PASSWORD - 用于可见密码输入。输入的内容以明文显示，适用于用户希望查看密码的场景。
            // TYPE_TEXT_VARIATION_WEB_PASSWORD - 用于网页密码输入。常用于网页表单中的密码字段，通常有特定的密码管理优化。
            // TYPE_TEXT_VARIATION_URI - 用于URI输入。适用于输入网址或URI（如：http://或ftp://）。
            // TYPE_TEXT_VARIATION_EMAIL_ADDRESS - 用于电子邮件地址输入。键盘会优化为适合输入电子邮件地址，如自动补充 `@` 符号。
            // TYPE_TEXT_VARIATION_PERSON_NAME - 用于输入人名。通常显示优化的人名键盘，便于用户输入全名、姓氏等。
            // TYPE_TEXT_VARIATION_LONG_MESSAGE - 用于输入较长的文本消息。通常会启用多行输入框，并显示“换行”键。
            // TYPE_CLASS_NUMBER - 用于数字输入。显示数字键盘，适合输入价格、数量等数字信息。
            // TYPE_NUMBER_VARIATION_NORMAL - 用于常规数字输入。适合输入数字，没有密码或特殊格式要求。
            // TYPE_NUMBER_VARIATION_PASSWORD - 用于数字密码输入。适用于需要输入数字密码的场景，输入内容会隐藏。
            // TYPE_CLASS_PHONE - 用于电话号码输入。显示专用的电话号码键盘，优化了电话号码输入的格式。
            // TYPE_CLASS_DATETIME - 用于日期或时间输入。显示适合选择日期或时间的键盘。
            // TYPE_TEXT_FLAG_NO_SUGGESTIONS - 禁用文本建议。输入框不会显示任何自动补全或预测文本建议。
            // TYPE_TEXT_FLAG_AUTO_COMPLETE - 启用自动完成功能。通常用于输入电子邮件地址、URL、用户名等，系统会提供自动完成的建议。
            // TYPE_TEXT_FLAG_CAP_CHARACTERS - 强制所有输入字符为大写。输入的每个字母都会自动转为大写。
            // TYPE_TEXT_FLAG_CAP_SENTENCES - 自动将每个句子的首字母转换为大写。适用于正常文本输入。
            // TYPE_TEXT_FLAG_CAP_WORDS - 自动将每个单词的首字母转换为大写。常用于标题或专有名词的输入。
            // TYPE_TEXT_FLAG_MULTI_LINE - 启用多行输入。允许输入换行符，适合需要输入长文本（如评论、消息等）的场景。
            input.setCatInputType(InputType.TYPE_CLASS_TEXT);//设置输入框输入类型
            //设置输入框回调监听器
            input.setCatCallback(new AlguiCallback.Input(){
                    //开始输入时调用
                    public void start(String text) {

                    }
                    //内容更新时调用
                    public void update(String text) {

                    }
                    //输入结束时调用
                    public void end(String text) {

                    }
                    //按下按钮时调用 (选择性，对于无按钮可不写)
                    public void buttonClick(String text) {

                    }
                }
            );
        }





        //网络视图 AlguiViewWeb
        public void AlguiViewWeb() {
            //[只提供关键设置方法，对于自定义拓展 通过.getByte...获取和此组件绑定的各种对象然后设置对应对象的样式]
            //初始化第二个参数传有效活动，用于在web中跳转链接失败时自动打开游览器跳转
            AlguiViewWeb web = new AlguiViewWeb(aContext, AlguiActivity.MainActivity)
                .setCatSize(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT)//视图大小
                .setCatWeight(1)//权重 用于此视图和与同父布局的其它视图的占用大小比例
                .setCatMargins(0, 0, 0, 0)//外边距(左上右下)
                .setCatBackColor(0)//背景颜色
                .setCatRadiu(0)//圆角半径
                .setCatZoom(0)//缩放
                .setCatWeb("网站,html代码,html文件路径,html文件名(assets文件夹的)")//设置通用加载内容
                .setCatWebsite("url", 1)//设置加载网站(参数：网站链接，缩放百分比)
                .setCatHtmlFile("file.html")//设置加载html文件 支持文件路径，assets文件夹的文件名
                .setCatHtml("htmlCode")//设置加载html代码(有多个重载不示例了)
                .setCatParentLayout(null)//设置父布局
                ;
        }


        //三角形 AlguiViewTriangle
        public void AlguiViewTriangle() {
            AlguiViewTriangle t=new AlguiViewTriangle(aContext)
                .setCatType(AlguiViewTriangle.Type.EQUILATERAL_BOTTOM)//设置三角形方向类型 [AlguiViewTriangle.Type.三角形方向类型]
                .setCatSize(0)//设置三角形大小
                .setCatColor(0)//设置三角形颜色
                .setCatParentLayout(null)//设置父布局
                ;

        }


        //线条 AlguiViewLine
        public void AlguiViewLine() {
            //线条样式
            /*public static class AlguiViewLine.Style {
             public static final int STYLE_SOLID = 0;    //实线
             public static final int STYLE_DASHED = 1;   //虚线
             public static final int STYLE_DOTTED = 2;   //点线
             public static final int STYLE_WAVE= 3;   //波浪线
             }*/
            AlguiViewLine line=new AlguiViewLine(aContext)
                .setCatSize(0)//线条大小
                .setCatWeight(0)//权重 用于此视图和与同父布局的其它视图的占用大小比例
                .setCatMargins(0, 0, 0, 0)//外边距(左上右下)
                .setCatStyle(AlguiViewLine.Style.STYLE_DASHED)//线条样式
                .setCatColor(0)//线条颜色
                .setCatParentLayout(null)//设置父布局
                ;
        }


        //可视化声音监测视图 AlguiViewSoundWave
        public void AlguiViewSoundWave() {
            //声波样式
            /* public static class AlguiViewSoundWave.Style{
             public static final int STYLE_LINE_DENSE=0;//密集线 (完整的声纹 拥有丰富的视觉效果)
             public static final int STYLE_LINE_INTERVAL=1;//间隔线 (半完整的声纹 会丢失一些声波数据)
             public static final int STYLE_LINE_PARTICE=2;//粒子线  (半完整的声纹 会丢失一些声波数据)
             public static final int STYLE_LINE_WAVE=3;//波浪线  (半完整的声纹 会丢失一些声波数据)
             }*/
            //声波颜色样式
            /* public static class AlguiViewSoundWave.StyleColor{
             public static final int STYLE_COLOR_CUSTOMIZE=0;//自定义
             public static final int STYLE_COLOR_AMP=1;//每个线条根据振幅小绿中黄大红动态变换
             public static final int STYLE_COLOR_AMP_ALL=2;//所有线条根据振幅小绿中黄大红动态变换
             }*/
            //传递的第二个参数为有效活动，用于请求录音权限
            AlguiViewSoundWave sw=new AlguiViewSoundWave(aContext, AlguiActivity.MainActivity)
                .setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, 30)//视图大小
                .setCatWeight(0)//权重 用于此视图和与同父布局的其它视图的占用大小比例
                .setCatPadding(0, 0, 0, 0)//内边距(左上右下)
                .setCatMargins(0, 0, 0, 0)//外边距(左上右下)
                .setCatBackColor(0)//背景颜色(传多个颜色则自动渐变)
                .setCatRadiu(0)//圆角半径
                .setCatBorder(0, 0)//描边大小和颜色
                .setCatStyle(AlguiViewSoundWave.Style.STYLE_LINE_INTERVAL)//声波样式
                .setCatColorStyle(AlguiViewSoundWave.StyleColor.STYLE_COLOR_AMP)//声波颜色样式
                .setCatColor(0)//声波颜色(传多个颜色则自动渐变)
                .setCatGlow(0, 0)//声波发光半径和颜色(掉帧)
                .setCatSensitivity(0)//声波灵敏度
                .startRecording()//开始录音
                .stopRecording()//停止录音
                .setCatParentLayout(null)//设置父布局
                ;
        }

      


    }



}
