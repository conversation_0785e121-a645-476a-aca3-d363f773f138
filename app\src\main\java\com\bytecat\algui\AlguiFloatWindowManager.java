package com.bytecat.algui;

import android.content.Context;
import android.content.Intent;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/24 22:36
 * @Describe 悬浮窗管理器 - 实现单例模式，确保每个游戏的悬浮窗只能存在一个
 */
public class AlguiFloatWindowManager {

    public static final String TAG = "AlguiFloatWindowManager";

    // 单例实例
    private static AlguiFloatWindowManager instance;
    
    // 当前活跃的悬浮窗映射表 <游戏名称, 悬浮窗实例>
    private Map<String, AlguiWinMenu> activeFloatWindows;
    
    // 当前活跃的游戏名称
    private String currentActiveGame;
    
    // 私有构造函数，防止外部实例化
    private AlguiFloatWindowManager() {
        activeFloatWindows = new HashMap<>();
        currentActiveGame = null;
        AlguiLog.d(TAG, "悬浮窗管理器初始化完成");
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized AlguiFloatWindowManager getInstance() {
        if (instance == null) {
            instance = new AlguiFloatWindowManager();
        }
        return instance;
    }
    
    /**
     * 启动指定游戏的悬浮窗
     * @param context 上下文
     * @param gameName 游戏名称
     * @return 是否成功启动
     */
    public boolean startFloatWindow(Context context, String gameName) {
        if (context == null || gameName == null || gameName.isEmpty()) {
            AlguiLog.e(TAG, "启动悬浮窗失败：参数无效");
            return false;
        }
        
        AlguiLog.d(TAG, "请求启动游戏悬浮窗: " + gameName);
        
        // 检查是否已有相同游戏的悬浮窗在运行
        if (activeFloatWindows.containsKey(gameName)) {
            AlguiWinMenu existingWindow = activeFloatWindows.get(gameName);
            if (existingWindow != null && existingWindow.getByteWindow().isWindowShow()) {
                AlguiLog.w(TAG, "游戏 " + gameName + " 的悬浮窗已在运行，将关闭现有窗口");
                closeFloatWindow(gameName);
            }
        }
        
        // 如果当前有其他游戏的悬浮窗在运行，先关闭它
        if (currentActiveGame != null && !currentActiveGame.equals(gameName)) {
            AlguiLog.d(TAG, "关闭当前活跃游戏悬浮窗: " + currentActiveGame);
            closeFloatWindow(currentActiveGame);
        }
        
        try {
            // 根据游戏名称启动对应的悬浮窗
            switch (gameName) {
                case "星陨计划":
                    Main.start(context);
                    currentActiveGame = gameName;
                    AlguiLog.d(TAG, "成功启动星陨计划悬浮窗");
                    return true;
                    
                case "天下布魔":
                    Game2FloatWindow.start(context);
                    currentActiveGame = gameName;
                    AlguiLog.d(TAG, "成功启动天下布魔悬浮窗");
                    return true;
                    
                case "潘吉亚异闻录":
                    Game3FloatWindow.start(context);
                    currentActiveGame = gameName;
                    AlguiLog.d(TAG, "成功启动潘吉亚异闻录悬浮窗");
                    return true;
                    
                case "贤者同盟":
                    Game4FloatWindow.start(context);
                    currentActiveGame = gameName;
                    AlguiLog.d(TAG, "成功启动贤者同盟悬浮窗");
                    return true;
                    
                case "樱井物语":
                    Game5FloatWindow.start(context);
                    currentActiveGame = gameName;
                    AlguiLog.d(TAG, "成功启动樱井物语悬浮窗");
                    return true;
                    
                default:
                    AlguiLog.e(TAG, "未知游戏名称: " + gameName);
                    return false;
            }
        } catch (Exception e) {
            AlguiLog.e(TAG, "启动悬浮窗异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 通过Service启动悬浮窗（用于全局悬浮窗）
     * @param context 上下文
     * @param gameName 游戏名称
     * @return 是否成功启动
     */
    public boolean startFloatWindowViaService(Context context, String gameName) {
        if (context == null || gameName == null || gameName.isEmpty()) {
            AlguiLog.e(TAG, "通过Service启动悬浮窗失败：参数无效");
            return false;
        }
        
        AlguiLog.d(TAG, "通过Service启动游戏悬浮窗: " + gameName);
        
        try {
            // 通过Service启动悬浮窗
            Intent intent = new Intent(context, AlguiService.class);
            intent.putExtra("game", gameName);
            context.startService(intent);
            
            currentActiveGame = gameName;
            AlguiLog.d(TAG, "成功通过Service启动 " + gameName + " 悬浮窗");
            return true;
            
        } catch (Exception e) {
            AlguiLog.e(TAG, "通过Service启动悬浮窗异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 关闭指定游戏的悬浮窗
     * @param gameName 游戏名称
     * @return 是否成功关闭
     */
    public boolean closeFloatWindow(String gameName) {
        if (gameName == null || gameName.isEmpty()) {
            AlguiLog.e(TAG, "关闭悬浮窗失败：游戏名称无效");
            return false;
        }
        
        AlguiLog.d(TAG, "请求关闭游戏悬浮窗: " + gameName);
        
        try {
            // 从活跃窗口映射表中移除
            AlguiWinMenu window = activeFloatWindows.remove(gameName);
            if (window != null) {
                window.hideWin();
                AlguiLog.d(TAG, "成功关闭悬浮窗: " + gameName);
            }
            
            // 如果关闭的是当前活跃游戏，清空当前活跃游戏标记
            if (gameName.equals(currentActiveGame)) {
                currentActiveGame = null;
                AlguiLog.d(TAG, "清空当前活跃游戏标记");
            }
            
            return true;
            
        } catch (Exception e) {
            AlguiLog.e(TAG, "关闭悬浮窗异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 关闭所有悬浮窗
     * @return 是否成功关闭
     */
    public boolean closeAllFloatWindows() {
        AlguiLog.d(TAG, "请求关闭所有悬浮窗");
        
        try {
            // 关闭所有活跃的悬浮窗
            for (AlguiWinMenu window : activeFloatWindows.values()) {
                if (window != null) {
                    window.hideWin();
                }
            }
            
            // 清空活跃窗口映射表
            activeFloatWindows.clear();
            currentActiveGame = null;
            
            AlguiLog.d(TAG, "成功关闭所有悬浮窗");
            return true;
            
        } catch (Exception e) {
            AlguiLog.e(TAG, "关闭所有悬浮窗异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 注册悬浮窗实例
     * @param gameName 游戏名称
     * @param window 悬浮窗实例
     */
    public void registerFloatWindow(String gameName, AlguiWinMenu window) {
        if (gameName != null && window != null) {
            activeFloatWindows.put(gameName, window);
            currentActiveGame = gameName;
            AlguiLog.d(TAG, "注册悬浮窗: " + gameName);
        }
    }
    
    /**
     * 注销悬浮窗实例
     * @param gameName 游戏名称
     */
    public void unregisterFloatWindow(String gameName) {
        if (gameName != null) {
            activeFloatWindows.remove(gameName);
            if (gameName.equals(currentActiveGame)) {
                currentActiveGame = null;
            }
            AlguiLog.d(TAG, "注销悬浮窗: " + gameName);
        }
    }
    
    /**
     * 检查指定游戏的悬浮窗是否正在运行
     * @param gameName 游戏名称
     * @return 是否正在运行
     */
    public boolean isFloatWindowRunning(String gameName) {
        if (gameName == null) {
            return false;
        }
        
        AlguiWinMenu window = activeFloatWindows.get(gameName);
        return window != null && window.getByteWindow().isWindowShow();
    }
    
    /**
     * 检查是否有任何悬浮窗正在运行
     * @return 是否有悬浮窗运行
     */
    public boolean hasAnyFloatWindowRunning() {
        for (AlguiWinMenu window : activeFloatWindows.values()) {
            if (window != null && window.getByteWindow().isWindowShow()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取当前活跃的游戏名称
     * @return 当前活跃游戏名称，如果没有则返回null
     */
    public String getCurrentActiveGame() {
        return currentActiveGame;
    }
    
    /**
     * 获取活跃悬浮窗数量
     * @return 活跃悬浮窗数量
     */
    public int getActiveFloatWindowCount() {
        return activeFloatWindows.size();
    }
    
    /**
     * 获取所有活跃的游戏名称
     * @return 活跃游戏名称数组
     */
    public String[] getActiveGameNames() {
        return activeFloatWindows.keySet().toArray(new String[0]);
    }
    
    /**
     * 重置管理器状态
     */
    public void reset() {
        closeAllFloatWindows();
        AlguiLog.d(TAG, "悬浮窗管理器状态已重置");
    }
} 