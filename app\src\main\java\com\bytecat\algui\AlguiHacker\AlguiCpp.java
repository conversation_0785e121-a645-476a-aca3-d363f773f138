package com.bytecat.algui.AlguiHacker;
import android.content.Context;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/22 21:48
 * @Describe 访问c++
 */
public class AlguiCpp {

    public static final String TAG = "AlguiCpp";

    //执行shell命令
    public static void shell(String shell) {
        try {
            Process process =Runtime.getRuntime().exec(shell, null, null);
            process.waitFor();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //执行外部cpp已编译好的so文件
    /**
     * 执行外部cpp已编译好的so文件 普通执行
     * @param soFilePath so文件绝对路径
     */
    public static void exec(String soFilePath) {
        if (soFilePath == null) {
            return;
        }
        //授予so文件777权限 必须有执行权限否则无法运行
        shell("chmod 777 " +  soFilePath);
        //执行so文件
        shell(soFilePath);
    }

    /**
     * 执行外部cpp已编译好的so文件 普通执行 使用root权限执行
     * @param soFilePath so文件绝对路径
     */
    public static void exec_root(String soFilePath) {
        if (soFilePath == null) {
            return;
        }
        //授予so文件777权限 必须有执行权限否则无法运行
        shell("su -c \"" + "chmod 777 " + soFilePath + "\"");
        //执行so文件
        shell("su -c \"" + soFilePath + "\"");

    }


    /**
     * 执行外部cpp已编译好的so文件 这将执行当前程序lib路径的so文件
     * @param libName 当前程序lib目录下要执行的so文件名
     */
    //注意：请确保当前程序安装包lib文件夹中的每个文件夹都包含该so文件，这样才能自动识别在不同处理器下执行不同so文件
    public static void exec_lib(Context context, String libName) {
        if (libName == null) {
            return;
        }
        exec(context.getApplicationInfo().nativeLibraryDir + "/" + libName);
    }


    /**
     * 执行外部cpp已编译好的so文件 这将以Root权限执行当前程序lib路径的so文件
     * @param libName 当前程序lib目录下要执行的so文件名
     */
    //注意：请确保当前程序安装包lib文件夹中的每个文件夹都包含该so文件，这样才能自动识别在不同处理器下执行不同so文件
    public static void exec_lib_root(Context context, String libName) {
        if (libName == null) {
            return;
        }
        exec_root(context.getApplicationInfo().nativeLibraryDir + "/" + libName);
    }

}
