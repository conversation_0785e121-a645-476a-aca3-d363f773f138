package com.bytecat.algui.AlguiHacker;
import com.bytecat.algui.AlguiTools.AlguiToolNative;
import android.content.Context;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/26 02:35
 * @Describe Algui内存工具JNI映射接口
 */
public class AlguiNativeMemTool {
    
    public static final String TAG = "AlguiNativeMemTool";
    static{AlguiToolNative.loadLibrary("Algui");}
    
    //初始化
    public static native int setPackageName(String packageName); // 设置目标包名 【注意：这是初始化函数，不调用此函数的话其它内存操作均失效】 
    public static native int getPID(String packageName); // 获取进程ID 
    public static native void setIsSecureWrites(boolean sw);// 设置安全写入启用状态

    //模块[动/静]态基址偏移内存修改
    public static native long getModuleBaseAddr(String moduleName, int headType); // 获取模块起始地址(基址) 
    public static native long jump(long addr,int count); // 跳转指针
    public static native long jump32(long addr); // 跳转指针 [32位] 
    public static native long jump64(long addr); // 跳转指针 [64位] 
    public static native int setMemoryAddrValue(String value, long addr, int type, boolean isFree); // 设置指定内存地址指向的值 🛡️该方法已对当前进程进行保护 防止GG模糊🛡️ 
    public static native String getMemoryAddrData(long addr, int type); // 获取指定内存地址的数据
    //读[推荐Root外部辅助用](支持第三方进程，这是在进程的mem文件读取的，有io所以像绘制这种频繁使用可能会影响性能)
    public static native int getDword(long addr);
    public static native float getFloat(long addr);
    public static native double getDouble(long addr);
    public static native long getQword(long addr);
    public static native int getWord(long addr);
    public static native byte getByte(long addr);
    public static native String getString(long addr);
    //读[推荐一体直装用](仅供自身进程使用，直接解引用访问所以很快，第三方进程会引发野指针所以仅供自身进程使用，比如给直装绘制用非常快)
    public static native int getDword_t(long addr);
    public static native float getFloat_t(long addr);
    public static native double getDouble_t(long addr);
    public static native long getQword_t(long addr);
    public static native int getWord_t(long addr);
    public static native byte getByte_t(long addr);
    public static native String getString_t(long startAddr);
    

    //内存搜索
    public static native void setMemoryArea(int memoryArea); // 设置要搜索的内存区域
    public static native long[] MemorySearch(String value, int type); // 内存搜索  【支持范围搜索 格式：最小值~最大值】
    public static native long[] MemorySearchRange(String value, int type); // 内存搜索范围值 【格式：最小值~最大值】
    public static native long[] MemorySearchUnited(String value, int type);//联合内存搜索 【格式：值1;值2;值3;n个值:附近范围 示例：2D;3F;4E:50 或 2D;3F;4E没有范围则使用默认范围,两个范围符::代表按顺序搜索 (同GG) 并且值也支持范围例如1~2;3:64】
    public static native long[] ImproveOffset(String value, int type, long offset);//偏移改善 [筛选偏移处特征值 支持联合改善，范围改善]
    public static native long[] ImproveOffsetRange(String value, int type, long offset);//偏移范围改善 [筛选偏移处只会在一个范围内变化的特征值] 【格式：最小值~最大值 (同GG)】
    public static native long[] ImproveOffsetUnited(String value, int type, long offset);//偏移联合改善 [筛选偏移处永远只会为某些值的特征值] 【格式：值1;值2;n个值; 示例：2D;3F;4E 或 2D;3~6F;9E 注：联合改善不支持附近范围和顺序改善】
    public static native long[] ImproveValue(String value, int type);//改善 [支持联合改善，范围改善]
    public static native int MemoryOffsetWrite(String value, int type, long offset, boolean isFree); // 结果偏移写入数据 🛡️该方法已对当前进程进行保护 防止GG模糊🛡️
    public static native int getResultCount(); // 获取最终搜索结果数量
    public static native long[] getResultList(); // 获取最终搜索结果列表
    public static native int printResultListToFile(String filePath); // 打印最终搜索结果列表到指定文件
    public static native int clearResultList(); // 清空最终搜索结果列表

    //冻结内存修改
    public static native void setFreezeDelayMs(int delay); // 设置冻结延迟时间（毫秒）
    public static native int getFreezeNum(); // 获取冻结项目数量
    public static native int addFreezeItem(String value, long addr, int type); // 添加冻结项目
    public static native int removeFreezeItem(long addr); // 移除指定冻结项目
    public static native int removeAllFreezeItem(); // 移除所有冻结项目
    public static native int startAllFreeze(); // 启动所有冻结项目
    public static native int stopAllFreeze(); // 停止所有冻结项目
    public static native int printFreezeListToFile(String filePath); // 将冻结项目列表打印到文件

    //获取内存信息相关工具
    public static native String getMemoryAddrMapLine(long address); // 获取指定内存地址的Maps映射行
    public static native String getMapLineMemoryAreaName(String mapLine); // 获取Maps映射行所在内存区域名称
    public static native String getMemoryAreaIdName(int memid); // 获取指定内存id的内存名称
    public static native String getMemoryAreaName(); // 获取当前内存名称
    public static native String getDataTypeName(int typeId); // 获取指定数据类型id的数据类型名称

    //完全需要ROOT权限的操作 
    //PS：告诉一下菜鸟，这些操作涉及到跨进程和系统操作，所以必须完全ROOT，直装也没用
    //--kill--
    public static native int killProcess_Root(String packageName); // 杀掉指定包名的进程
    public static native int stopProcess_Root(String packageName); // 暂停指定包名的进程 (此方法对于执行者自身进程无需Root)
    public static native int resumeProcess_Root(String packageName); // 恢复被暂停的指定包名的进程 (此方法对于执行者自身进程无需Root)
    public static native void killAllInotify_Root(); // 杀掉所有inotify监视器，防止游戏监视文件变化
    public static native int killGG_Root(); // 杀掉GG修改器
    public static native int killXscript_Root(); // 杀掉XS脚本
    //--Other--
    public static native int rebootsystem_Root(); // 重启手机
    public static native int installapk_Root(String apkPackagePath); // 静默安装指定路径的APK安装包
    public static native int uninstallapk_Root(String packageName); // 静默卸载指定包名的APK软件
    public static native int Cmd(String command);//执行命令
    public static native int Cmd_Root(String command);//执行超级命令
    
    //执行JNI层的功能函数 参数：功能ID，该功能开关状态，修改值(可选 对于在拖动条或输入框自定义修改值)
    public static native void JniSwitch(int id,boolean isSwitch,String value);
    
    //执行控制JNI层的HOOK(不支持root) 参数：功能ID，该功能开关状态，修改值(可选 对于在拖动条或输入框自定义修改值)
    public static native void JniHook(int id,boolean isSwitch,double value);
    
    
    
 
    
    
    //native层签名校验
    //public static native int loadGame(Context context);
}
