package com.bytecat.algui.AlguiHacker;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteException;
import com.topjohnwu.superuser.ipc.RootService;
import com.bytecat.algui.AlguiTools.AlguiToolNative;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/22 21:48
 * @Describe Algui Root客户端 实现IPC通道方法接口传递给服务端
 */
public class AlguiRootClient extends RootService {

    public static final String TAG = "AlguiRootClient";

    //实现Algui JRoot服务端的IPC接口函数 下面方法将在ROOT环境下调用
    @Override
    public IBinder onBind(@NonNull Intent intent) {
        return new AlguiRootIPC.Stub() {
            //=======AlguiROOT内存修改工具========
            @Override // 设置包名
            public int setPackageName(String packageName) throws RemoteException { return AlguiNativeMemTool.setPackageName(packageName); }

            @Override // 获取进程ID
            public int getPID(String packageName) throws RemoteException { return AlguiNativeMemTool.getPID(packageName); }

            @Override // 设置是否启用安全写入
            public void setIsSecureWrites(boolean sw) throws RemoteException { AlguiNativeMemTool.setIsSecureWrites(sw); }

            @Override // 获取模块基址
            public long getModuleBaseAddr(String moduleName, int headType) throws RemoteException { return AlguiNativeMemTool.getModuleBaseAddr(moduleName, headType); }

            @Override // 跳转指针
            public long jump(long addr, int count) throws RemoteException { return AlguiNativeMemTool.jump(addr, count); }

            @Override // 32位跳转
            public long jump32(long addr) throws RemoteException { return AlguiNativeMemTool.jump32(addr); }

            @Override // 64位跳转
            public long jump64(long addr) throws RemoteException { return AlguiNativeMemTool.jump64(addr); }

            @Override // 设置内存地址值
            public int setMemoryAddrValue(String value, long addr, int type, boolean isFree) throws RemoteException { return AlguiNativeMemTool.setMemoryAddrValue(value, addr, type, isFree); }

            @Override // 获取内存地址数据
            public String getMemoryAddrData(long addr, int type) throws RemoteException { return AlguiNativeMemTool.getMemoryAddrData(addr, type); }

            @Override // 设置内存区域
            public void setMemoryArea(int memoryArea) throws RemoteException { AlguiNativeMemTool.setMemoryArea(memoryArea); }

            @Override // 内存搜索
            public long[] MemorySearch(String value, int type) throws RemoteException { return AlguiNativeMemTool.MemorySearch(value, type); }

            @Override // 内存范围搜索
            public long[] MemorySearchRange(String value, int type) throws RemoteException { return AlguiNativeMemTool.MemorySearchRange(value, type); }

            @Override // 联合内存搜索
            public long[] MemorySearchUnited(String value, int type) throws RemoteException { return AlguiNativeMemTool.MemorySearchUnited(value, type); }

            @Override // 改进偏移
            public long[] ImproveOffset(String value, int type, long offset) throws RemoteException { return AlguiNativeMemTool.ImproveOffset(value, type, offset); }

            @Override // 改进范围偏移
            public long[] ImproveOffsetRange(String value, int type, long offset) throws RemoteException { return AlguiNativeMemTool.ImproveOffsetRange(value, type, offset); }

            @Override // 改进联合偏移
            public long[] ImproveOffsetUnited(String value, int type, long offset) throws RemoteException { return AlguiNativeMemTool.ImproveOffsetUnited(value, type, offset); }

            @Override // 改进值
            public long[] ImproveValue(String value, int type) throws RemoteException { return AlguiNativeMemTool.ImproveValue(value, type); }

            @Override // 内存偏移写入
            public int MemoryOffsetWrite(String value, int type, long offset, boolean isFree) throws RemoteException { return AlguiNativeMemTool.MemoryOffsetWrite(value, type, offset, isFree); }

            @Override // 获取结果数量
            public int getResultCount() throws RemoteException { return AlguiNativeMemTool.getResultCount(); }

            @Override // 获取结果列表
            public long[] getResultList() throws RemoteException { return AlguiNativeMemTool.getResultList(); }

            @Override // 打印结果列表到文件
            public int printResultListToFile(String filePath) throws RemoteException { return AlguiNativeMemTool.printResultListToFile(filePath); }

            @Override // 清除结果列表
            public int clearResultList() throws RemoteException { return AlguiNativeMemTool.clearResultList(); }

            @Override // 设置冻结延迟
            public void setFreezeDelayMs(int delay) throws RemoteException { AlguiNativeMemTool.setFreezeDelayMs(delay); }

            @Override // 获取冻结数量
            public int getFreezeNum() throws RemoteException { return AlguiNativeMemTool.getFreezeNum(); }

            @Override // 添加冻结项
            public int addFreezeItem(String value, long addr, int type) throws RemoteException { return AlguiNativeMemTool.addFreezeItem(value, addr, type); }

            @Override // 移除冻结项
            public int removeFreezeItem(long addr) throws RemoteException { return AlguiNativeMemTool.removeFreezeItem(addr); }

            @Override // 移除所有冻结项
            public int removeAllFreezeItem() throws RemoteException { return AlguiNativeMemTool.removeAllFreezeItem(); }

            @Override // 启动所有冻结
            public int startAllFreeze() throws RemoteException { return AlguiNativeMemTool.startAllFreeze(); }

            @Override // 停止所有冻结
            public int stopAllFreeze() throws RemoteException { return AlguiNativeMemTool.stopAllFreeze(); }

            @Override // 打印冻结列表到文件
            public int printFreezeListToFile(String filePath) throws RemoteException { return AlguiNativeMemTool.printFreezeListToFile(filePath); }

            @Override // 获取内存地址映射行
            public String getMemoryAddrMapLine(long address) throws RemoteException { return AlguiNativeMemTool.getMemoryAddrMapLine(address); }

            @Override // 获取映射行的内存区域名
            public String getMapLineMemoryAreaName(String mapLine) throws RemoteException { return AlguiNativeMemTool.getMapLineMemoryAreaName(mapLine); }

            @Override // 获取内存区域ID名称
            public String getMemoryAreaIdName(int memid) throws RemoteException { return AlguiNativeMemTool.getMemoryAreaIdName(memid); }

            @Override // 获取内存区域名称
            public String getMemoryAreaName() throws RemoteException { return AlguiNativeMemTool.getMemoryAreaName(); }

            @Override // 获取数据类型名称
            public String getDataTypeName(int typeId) throws RemoteException { return AlguiNativeMemTool.getDataTypeName(typeId); }

            @Override // 杀死进程（Root权限）
            public int killProcess_Root(String packageName) throws RemoteException { return AlguiNativeMemTool.killProcess_Root(packageName); }

            @Override // 杀死所有Inotify（Root权限）
            public void killAllInotify_Root() throws RemoteException { AlguiNativeMemTool.killAllInotify_Root(); }

            @Override // 停止进程（Root权限）
            public int stopProcess_Root(String packageName) throws RemoteException { return AlguiNativeMemTool.stopProcess_Root(packageName); }

            @Override // 恢复进程（Root权限）
            public int resumeProcess_Root(String packageName) throws RemoteException { return AlguiNativeMemTool.resumeProcess_Root(packageName); }

            @Override // 杀死GG（Root权限）
            public int killGG_Root() throws RemoteException { return AlguiNativeMemTool.killGG_Root(); }

            @Override // 杀死Xscript（Root权限）
            public int killXscript_Root() throws RemoteException { return AlguiNativeMemTool.killXscript_Root(); }

            @Override // 重启系统（Root权限）
            public int rebootsystem_Root() throws RemoteException { return AlguiNativeMemTool.rebootsystem_Root(); }

            @Override // 安装APK（Root权限）
            public int installapk_Root(String apkPackagePath) throws RemoteException { return AlguiNativeMemTool.installapk_Root(apkPackagePath); }

            @Override // 卸载APK（Root权限）
            public int uninstallapk_Root(String packageName) throws RemoteException { return AlguiNativeMemTool.uninstallapk_Root(packageName); }

            @Override // 执行命令
            public int Cmd(String command) throws RemoteException { return AlguiNativeMemTool.Cmd(command); }

            @Override // 执行Root命令
            public int Cmd_Root(String command) throws RemoteException { return AlguiNativeMemTool.Cmd_Root(command); }

            @Override // 执行JNI层的功能
            public void JniSwitch(int id, boolean isSwitch, String value) throws RemoteException { AlguiNativeMemTool.JniSwitch(id, isSwitch, value); }

           
            @Override
            public int getDword(long addr) throws RemoteException {
                return AlguiNativeMemTool.getDword(addr);
            }

            @Override
            public float getFloat(long addr) throws RemoteException {
                return AlguiNativeMemTool.getFloat(addr);
            }

            @Override
            public double getDouble(long addr) throws RemoteException {
                return AlguiNativeMemTool.getDouble(addr);
            }

            @Override
            public long getQword(long addr) throws RemoteException {
                return AlguiNativeMemTool.getQword(addr);
            }

            @Override
            public int getWord(long addr) throws RemoteException {
                return AlguiNativeMemTool.getWord(addr);
            }

            @Override
            public byte getByte(long addr) throws RemoteException {
                return AlguiNativeMemTool.getByte(addr);
            }

            @Override
            public String getString(long addr) throws RemoteException {
                return AlguiNativeMemTool.getString(addr);
            }

            @Override
            public int getDword_t(long addr) throws RemoteException {
                return AlguiNativeMemTool.getDword_t(addr);
            }

            @Override
            public float getFloat_t(long addr) throws RemoteException {
                return AlguiNativeMemTool.getFloat_t(addr);
            }

            @Override
            public double getDouble_t(long addr) throws RemoteException {
                return AlguiNativeMemTool.getDouble_t(addr);
            }

            @Override
            public long getQword_t(long addr) throws RemoteException {
                return AlguiNativeMemTool.getQword_t(addr);
            }

            @Override
            public int getWord_t(long addr) throws RemoteException {
                return AlguiNativeMemTool.getWord_t(addr);
            }

            @Override
            public byte getByte_t(long addr) throws RemoteException {
                return AlguiNativeMemTool.getByte_t(addr);
            }

            @Override
            public String getString_t(long startAddr) throws RemoteException {
                return AlguiNativeMemTool.getString_t(startAddr);
            }





            /* @Override
             public void setIsSecureWrites(boolean sw) throws RemoteException {
             AlguiMemTool.setIsSecureWrites(sw);
             }

             @Override
             public long[] MemorySearchUnited(String value, int type) throws RemoteException {
             return AlguiMemTool.MemorySearchUnited(value,type);
             }

             @Override
             public long[] ImproveOffsetRange(String value, int type, long offset) throws RemoteException {
             return AlguiMemTool.ImproveOffsetRange(value,type,offset);
             }

             @Override
             public long[] ImproveOffsetUnited(String value, int type, long offset) throws RemoteException {
             return AlguiMemTool.ImproveOffsetUnited(value,type,offset);
             }

             @Override
             public long[] ImproveValue(String value, int type) throws RemoteException {
             return AlguiMemTool.ImproveValue(value,type);
             }

             @Override
             public int stopProcess_Root(String packageName) throws RemoteException {
             return AlguiMemTool.stopProcess_Root(packageName);
             }

             @Override
             public int resumeProcess_Root(String packageName) throws RemoteException {
             return AlguiMemTool.resumeProcess_Root(packageName);
             }

             @Override
             public int Cmd(String command) throws RemoteException {
             return AlguiMemTool.Cmd(command);
             }

             @Override
             public int Cmd_Root(String command) throws RemoteException {
             return AlguiMemTool.Cmd_Root(command);
             }


             @Override
             public int setPackageName(String packageName) throws RemoteException {
             //设置包名
             return AlguiMemTool.setPackageName(packageName);
             }

             @Override
             public int getPID(String packageName) throws RemoteException {
             //获取进程ID
             return AlguiMemTool.getPID(packageName);
             }

             @Override
             public long getModuleBaseAddr(String moduleName, int headType) throws RemoteException {
             //获取模块基址
             return AlguiMemTool.getModuleBaseAddr(moduleName,headType);
             }

             @Override
             public long jump32(long addr) throws RemoteException {
             //跳转指针 32
             return AlguiMemTool.jump32(addr);
             }

             @Override
             public long jump64(long addr) throws RemoteException {
             //跳转指针 64
             return AlguiMemTool.jump64(addr);
             }

             @Override
             public int setMemoryAddrValue(String value, long addr, int type, boolean isFree) throws RemoteException {
             //修改指定地址的指定类型的值
             return AlguiMemTool.setMemoryAddrValue(value,addr,type,isFree);
             }

             @Override
             public String getMemoryAddrData(long addr, int type) throws RemoteException {
             //获取指定地址的指定类型的值
             return AlguiMemTool.getMemoryAddrData(addr, type);
             }

             @Override
             public void setMemoryArea(int memoryArea) throws RemoteException {
             //设置内存范围
             AlguiMemTool.setMemoryArea(memoryArea);
             }

             @Override
             public long[] MemorySearch(String value, int type) throws RemoteException {
             //内存搜索
             return AlguiMemTool.MemorySearch(value, type);
             }

             @Override
             public long[] MemorySearchRange(String value, int type) throws RemoteException {
             //内存搜索范围值
             return AlguiMemTool.MemorySearchRange(value, type);
             }

             @Override
             public long[] ImproveOffset(String value, int type, long offset) throws RemoteException {
             //搜索结果偏移筛选特征码
             return AlguiMemTool.ImproveOffset(value, type, offset);
             }


             @Override
             public int MemoryOffsetWrite(String value, int type, long offset, boolean isFree) throws RemoteException {
             //修改筛选结果开始偏移的指定类型的值
             return AlguiMemTool.MemoryOffsetWrite(value,type,offset,isFree);
             }

             @Override
             public int getResultCount() throws RemoteException {
             //获取搜索结果数量
             return AlguiMemTool.getResultCount();
             }

             @Override
             public long[] getResultList() throws RemoteException {
             //获取搜索结果列表
             return AlguiMemTool.getResultList();
             }

             @Override
             public int printResultListToFile(String filePath) throws RemoteException {
             //将搜索结果列表打印到指定文件中
             return AlguiMemTool.printResultListToFile(filePath);
             }

             @Override
             public int clearResultList() throws RemoteException {
             //清除搜索结果列表
             return AlguiMemTool.clearResultList();
             }

             @Override
             public void setFreezeDelayMs(int delay) throws RemoteException {
             //设置冻结延迟
             AlguiMemTool.setFreezeDelayMs(delay);
             }

             @Override
             public int getFreezeNum() throws RemoteException {
             //获取冻结数量
             return AlguiMemTool.getFreezeNum();
             }

             @Override
             public int addFreezeItem(String value, long addr, int type) throws RemoteException {
             //添加一个冻结项目
             return AlguiMemTool.addFreezeItem(value,addr,type);
             }

             @Override
             public int removeFreezeItem(long addr) throws RemoteException {
             //移除一个冻结项目
             return AlguiMemTool.removeFreezeItem(addr);
             }

             @Override
             public int removeAllFreezeItem() throws RemoteException {
             //移除所有冻结项目
             return AlguiMemTool.removeAllFreezeItem();
             }

             @Override
             public int startAllFreeze() throws RemoteException {
             //开始冻结
             return AlguiMemTool.startAllFreeze();
             }

             @Override
             public int stopAllFreeze() throws RemoteException {
             //停止冻结
             return AlguiMemTool.stopAllFreeze();
             }

             @Override
             public int printFreezeListToFile(String filePath) throws RemoteException {
             //将冻结列表打印到指定文件
             return AlguiMemTool.printFreezeListToFile(filePath);
             }

             @Override
             public String getMemoryAddrMapLine(long address) throws RemoteException {
             //获取指定内存地址的Maps映射行
             return AlguiMemTool.getMemoryAddrMapLine(address);
             }

             @Override
             public String getMapLineMemoryAreaName(String mapLine) throws RemoteException {
             //获取Maps映射行所在内存区域名称
             return AlguiMemTool.getMapLineMemoryAreaName(mapLine);
             }

             @Override
             public String getMemoryAreaIdName(int memid) throws RemoteException {
             //获取指定内存id的内存名称
             return AlguiMemTool.getMemoryAreaIdName(memid);
             }

             @Override
             public String getMemoryAreaName() throws RemoteException {
             //获取当前内存名称
             return AlguiMemTool.getMemoryAreaName();
             }

             @Override
             public String getDataTypeName(int typeId) throws RemoteException {
             //获取指定数据类型id的数据类型名称
             return AlguiMemTool.getDataTypeName(typeId);
             }

             @Override
             public int killprocess_Root(String packageName) throws RemoteException {
             //杀掉指定包名的进程
             return AlguiMemTool.killprocess_Root(packageName);
             }

             @Override
             public void killAllInotify_Root() throws RemoteException {
             //杀掉所有inotify监视器，防止游戏监视文件变化
             AlguiMemTool.killAllInotify_Root();
             }

             @Override
             public int killGG_Root() throws RemoteException {
             //杀掉GG修改器
             return AlguiMemTool.killGG_Root();
             }

             @Override
             public int killXscript_Root() throws RemoteException {
             //杀掉XS脚本
             return AlguiMemTool.killXscript_Root();
             }

             @Override
             public int rebootsystem_Root() throws RemoteException {
             //重启手机
             return AlguiMemTool.rebootsystem_Root();
             }

             @Override
             public int installapk_Root(String apkPackagePath) throws RemoteException {
             //静默安装指定路径的APK安装包
             return AlguiMemTool.installapk_Root(apkPackagePath);
             }

             @Override
             public int uninstallapk_Root(String packageName) throws RemoteException {
             //静默卸载指定包名的APK软件
             return AlguiMemTool.uninstallapk_Root(packageName);
             }


             @Override
             public void NativeDebug(int id) throws RemoteException {
             //Native调试专用
             AlguiMemTool.NativeDebug(id);
             }*/



        };
	}

}
