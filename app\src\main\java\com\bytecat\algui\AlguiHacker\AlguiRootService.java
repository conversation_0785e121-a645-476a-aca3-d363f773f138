package com.bytecat.algui.AlguiHacker;
import android.content.ComponentName;
import android.content.ServiceConnection;
import android.os.IBinder;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/22 21:48
 * @Describe Algui Root服务端
 */
public class AlguiRootService implements ServiceConnection {

    public static final String TAG = "AlguiRootService";
    private static AlguiCallback.RootService call;
    private static boolean isConnect=false;//是否已连接Root
    private static AlguiRootIPC ipc;//IPC通道
    //是否已连接Root
    public static boolean isConnect() {return isConnect;}
    //获取Root环境的方法列表
    public static AlguiRootIPC getRootList() {return ipc;}
    //设置ROOT状态回调接口
    public static void setCallBack(AlguiCallback.RootService c) {call = c;}

    //连接成功
    @Override
    public void onServiceConnected(ComponentName name, IBinder service) {
        //IBinder转换为Stub接口
        ipc = AlguiRootIPC.Stub.asInterface(service);
        isConnect = true;//已连接
        AlguiLog.d(TAG,"AlguiRoot权限连接成功");
        if (call != null)
            call.rootService(true);

    }

    //断开连接
    @Override
    public void onServiceDisconnected(ComponentName name) {
        isConnect = false;//断开连接
        AlguiLog.d(TAG,"AlguiRoot权限断开连接");
        if (call != null)
            call.rootService(false);
    }
}
