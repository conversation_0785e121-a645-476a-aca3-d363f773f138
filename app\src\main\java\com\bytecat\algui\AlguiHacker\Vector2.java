package com.bytecat.algui.AlguiHacker;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/27 00:48
 * @Describe Al<PERSON>i二维
 */
public class Vector2 {

    public static final String TAG = "Vector2";
    public float x;
    public float y;
    public Vector2() {
        this.x = 0;
        this.y = 0;
    }
    public Vector2(float x, float y) {
        this.x = x;
        this.y = y;
    }

    //加法运算
    public Vector2 add(Vector2 other) {
        return new Vector2(this.x + other.x, this.y + other.y);
    }

    //减法运算减
    public Vector2 subtract(Vector2 other) {
        return new Vector2(this.x - other.x, this.y - other.y);
    }

    //向量缩放
    public Vector2 scale(float factor) {
        return new Vector2(this.x * factor, this.y * factor);
    }

    //点积运算
    public float dot(Vector2 other) {
        return this.x * other.x + this.y * other.y;
    }

    //求向量的模（长度）
    public float magnitude() {
        return (float) Math.sqrt(x * x + y * y);
    }

    //归一化
    public Vector2 normalize() {
        float magnitude = magnitude();
        if (magnitude == 0) {
            return new Vector2(0, 0);  //防止除零
        }
        return new Vector2(this.x / magnitude, this.y / magnitude);
    }

    //输出字符串形式
    @Override
    public String toString() {
        return "Vector2(" + x + ", " + y + ")";
    }



}
