package com.bytecat.algui.AlguiHacker;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/27 00:48
 * @Describe Algui三维
 */
public class Vector3 {

    public static final String TAG = "Vector3";
    public float x;
    public float y;
    public float z;

    public Vector3() {
        this.x = 0;
        this.y = 0;
        this.z = 0;
    }

    public Vector3(float x, float y, float z) {
        this.x = x;
        this.y = y;
        this.z = z;
    }

    //加法运算
    public Vector3 add(Vector3 other) {
        return new Vector3(this.x + other.x, this.y + other.y, this.z + other.z);
    }

    //减法运算
    public Vector3 subtract(Vector3 other) {
        return new Vector3(this.x - other.x, this.y - other.y, this.z - other.z);
    }

    //向量缩放
    public Vector3 scale(float factor) {
        return new Vector3(this.x * factor, this.y * factor, this.z * factor);
    }

    //点积运算
    public float dot(Vector3 other) {
        return this.x * other.x + this.y * other.y + this.z * other.z;
    }

    //叉积运算
    public Vector3 cross(Vector3 other) {
        float crossX = this.y * other.z - this.z * other.y;
        float crossY = this.z * other.x - this.x * other.z;
        float crossZ = this.x * other.y - this.y * other.x;
        return new Vector3(crossX, crossY, crossZ);
    }

    //求向量的模（长度）
    public float magnitude() {
        return (float) Math.sqrt(x * x + y * y + z * z);
    }

    //归一化
    public Vector3 normalize() {
        float magnitude = magnitude();
        if (magnitude == 0) {
            return new Vector3(0, 0, 0);  //防止除零
        }
        return new Vector3(this.x / magnitude, this.y / magnitude, this.z / magnitude);
    }

    //输出字符串形式
    @Override
    public String toString() {
        return "Vector3(" + x + ", " + y + ", " + z + ")";
    }
    


}
