package com.bytecat.algui.AlguiManager;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/10 14:37
 * @Describe 监听事件回调接口
 */
import android.graphics.Canvas;
import android.os.Message;
import android.view.SurfaceHolder;
import java.util.HashMap;

public class AlguiCallback {

    public static final String TAG = "AlguiCallback";

    //普通点击事件监听器
    public interface Click {
        public void click(boolean isChecked);//点击
    }

    //项事件监听器
    public interface Item {
        public void item(int itemId);//切换项
    }

    //网络事件监听器
    public interface Web {
        public void web(Message msg);//服务器返回结果
    }

    //网络请求回调接口
    public interface NetworkCallback {
        void onSuccess(String response);
    }
    //输入事件监听器
    public static abstract class Input {
        public abstract void start(String text);//开始 (开始输入)
        public abstract void update(String text);//更新 (正在输入 内容被改变)
        public abstract void end(String text);//结束 (结束输入)
        public void buttonClick(String text) {}//按下按钮 {选择性实现}
    }

    //拖动条拖动事件监听器
    public interface DragBar {
        public void start(double progress);//开始 (开始拖动)
        public void update(double progress);//更新 (正在拖动 进度被改变)
        public void end(double progress);//结束 (结束拖动)

    }

    //Root监听器
    public interface RootService {
        public void rootService(boolean isConnect);
    }
    
    //绘制监听器
    public static abstract class Draw {
        //第一帧调用初始化
        //返回true代表初始化完成开始下一帧更新 
        //返回false代表初始化失败将锁定在此帧一直初始化并检查直到返回true才开始下一帧更新
        public abstract boolean Start(Canvas canvas);//第一帧
        //每一帧调用更新 
        //返回true代表更新完成开始下一帧更新 
        //返回false代表更新失败会跳转到Start函数检查是否初始化完成
        public abstract boolean Update(Canvas canvas);//每帧
        public void End(SurfaceHolder holder){};//渲染线程结束
        //更新画布大小时调用
        public void UpdateCanvasSize(SurfaceHolder holder, int format, int width, int height){};
    }
    
    //微验网络验证监听器
    public interface WY2FA {
        //登录成功 卡密 到期时间 远程变量
        public void success(String kami,String expireTime,HashMap<String, String> field);//登录成功
        
    }
}
