package com.bytecat.algui.AlguiManager;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/01/05 08:39
 */
public class AlguiDocument {

    public static final String TAG = "AlguiDocument";

    private static final String api ="http://wdan.tangdouz.com/?";
    private static final String key ="oYYW_6Z-62s_yLNjFY3eEStCiaUY";


    //写
    public static String getWrite(String fileName, String value)    {
        return String.format("%sf=%s&cz=w&nr=%s&key=%s", api, fileName, value, key);
    }
    //读
    public static String getRead(String fileName)    {
        return String.format("%sf=%s&cz=r&key=%s", api, fileName, key);
    }
    //删
    public static String getDelete(String fileName)    {
        return String.format("%sf=%s&cz=d&key=%s", api, fileName, key);
    }
    //增
    public static String getAdd(String fileName, String value)    {
        return String.format("%sf=%s&cz=a&nr=%s&key=%s&return=syso", api, fileName, value, key);
    }
    
   
}
