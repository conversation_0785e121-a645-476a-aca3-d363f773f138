package com.bytecat.algui.AlguiManager;
import java.io.File;
import java.util.Timer;
import java.util.TimerTask;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/03/09 00:33
 * @Describe 文件监视器
 */
public class AlguiFileObserver {

    public static final String TAG = "AlguiFileObserver";

    private String filePath;
    private long lastModifiedTime;
    private Timer timer;
    private FileWatcherListener listener;
    private File file;
    public AlguiFileObserver(String filePath, FileWatcherListener listener) {
        this.filePath = filePath;
        this.listener = listener;
        file = new File(filePath);
        if (file.exists()) {
            this.lastModifiedTime = file.lastModified();
        } else {
            this.lastModifiedTime = 0;
        }
    }

    //开始文件监控
    public void startWatching(long intervalMillis) {
        timer = new Timer();
        timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    checkFileModification();
                }
            }, 0, intervalMillis); //每隔intervalMillis毫秒检查一次
    }

    //停止文件监控
    public void stopWatching() {
        if (timer != null) {
            timer.cancel();
        }
    }

    //检查文件是否被修改
    private void checkFileModification() {
        if (file.exists()) {
            long currentModifiedTime = file.lastModified();
            if (currentModifiedTime > lastModifiedTime) {
                lastModifiedTime = currentModifiedTime;
                if (listener != null) {
                    listener.onFileModified(filePath);
                }
            }
        }
    }

    //文件发生变化回调接口
    public interface FileWatcherListener {
        void onFileModified(String filePath);
    }




}
