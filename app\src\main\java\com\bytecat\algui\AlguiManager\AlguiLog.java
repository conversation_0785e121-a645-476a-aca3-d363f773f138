package com.bytecat.algui.AlguiManager;
import android.content.Context;
import android.util.Log;
import com.bytecat.algui.AlguiTools.AlguiToolFile;
import com.bytecat.algui.AlguiTools.AlguiToolProcess;
import com.bytecat.algui.AlguiWindows.AlguiWinConsole;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/31 10:37
 * @Describe Algui日志
 */
public class AlguiLog {

    public static final String TAG = "AlguiLog";
    private static Context aContext;
    private static final String fileName="AlguiByteCat.log";//日志文件名
    private static final String debugLogFileName="DEBUG";//调试日志文件名用于动态开关输出调试日志(如果缓存目录存在此文件则输出DEBUG等级的日志)
    private static String ALGUILOGFILE = "/data/user/0/com.bytecat.algui/cache/" + fileName; //默认日志文件路径
    private static boolean isDebug=false;//是否输出DEBUG日志
    private static boolean LOG_ENABLED = true;//日志总开关
    private static boolean LOG_REPEAT=true;//是否写入重复日志
    private static boolean isOne = true;//第一次输出log清除历史log
    private static AlguiWinConsole Console;//log控制台窗口
    private static Set<String> loggedEntries = new HashSet<>();  //用于存储已写入的日志
    private static AlguiFileObserver fileWatcher;


    public static void init(Context c) {
        aContext = c;
        AlguiToolProcess.proc(c);
        if (aContext != null) {
            if (new File(aContext.getCacheDir().getAbsolutePath() + "/" + debugLogFileName).exists())
                isDebug = true;
            ALGUILOGFILE = aContext.getCacheDir().getAbsolutePath() + "/" + fileName;
        }
    }
    // 设置日志开关
    public static void setLogEnabled(boolean enabled) {
        LOG_ENABLED = enabled;
        if (!LOG_ENABLED) {
            clearLog();
        }
    }
    //设置日志是否可重复
    public static void setLogRepeatable(boolean isR) {
        LOG_REPEAT = isR;
        if (isR) {
            loggedEntries.clear();
        }
    }
    //获取LOG控制台窗口
    public static AlguiWinConsole getLogConsole(final Context aContext) {
        if (Console == null) {
            if (isOne) {//清理历史日志
                clearLog();
                isOne = false;
            }
            Console = new AlguiWinConsole(aContext);
            Console.setCatTitle("Algui日志");

            String newContent = AlguiToolFile.readContents(ALGUILOGFILE, null);
            if (newContent != null) {
                Console.getByteText().setText(newContent);
            }
            fileWatcher = new AlguiFileObserver(ALGUILOGFILE, new AlguiFileObserver.FileWatcherListener() {
                    @Override
                    public void onFileModified(String filePath) {
                        final String newContent = AlguiToolFile.readContents(filePath, null);
                        if (newContent != null) 
                            Console.getByteText().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        Console.getByteText().setText(newContent);
                                        //Console.getByteWindow().updateWin();
                                    }
                                });
                    }
                });
            fileWatcher.startWatching(2000);
        }
        return Console;
    }





    //获取日志文件
    public static File getLogFile() {

        File logFile = new File(ALGUILOGFILE);

        // 检查文件的父目录是否存在，如果不存在则创建
        /* File parentDir = logFile.getParentFile();
         if (parentDir != null && !parentDir.exists()) {
         if (!parentDir.mkdirs()) {
         return null;
         }
         }*/

        //检查文件是否存在，如果不存在则创建
        if (!logFile.exists()) {
            try {
                logFile.createNewFile();
            } catch (IOException e) {
                return null;
            }
        }

        return logFile;
    }



    //将日志写入文件
    private static boolean writeLogToFile(String logEntry) {
        try {
            File logFile = getLogFile();
            if (logFile == null)return false;
            BufferedWriter writer = new BufferedWriter(new FileWriter(logFile, true));  // 追加模式
            writer.write(logEntry);
            writer.newLine(); // 换行
            writer.close();
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Failed to write log to file", e);
        }
        return false;
    }
    //清除日志
    public static void clearLog() {
        try {
            File logFile = getLogFile();
            if (logFile == null)return;
            FileOutputStream fos = new FileOutputStream(logFile);
            fos.write(new byte[0]);  // 清空文件内容
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    //按级别并格式化写入日志消息
    public static void send(String level, String tag, String format, Object... args) {
        if (!LOG_ENABLED) return; 
        if (isOne) {
            clearLog();//清除历史
            isOne = false;
        }

        String logMessage = String.format(format, args);
        if (!LOG_REPEAT)
            if (loggedEntries.contains(tag + logMessage)) {
                return; //如果日志已经存在，则跳过写入
            }
        long currentTimeMillis = System.currentTimeMillis();
        Date date = new Date(currentTimeMillis);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String time= sdf.format(date);
        String logEntry = String.format("[%s] [%s] [%s] %s", time, level, tag, logMessage);

        if (writeLogToFile(logEntry)) {
            if (!LOG_REPEAT)
                loggedEntries.add(tag + format);
        }
    }
    public static boolean isDebug() {
        return isDebug;
    }

    // 调试日志
    public static void d(String tag, String format, Object... args) {
        if (isDebug)
            send("DEBUG", tag, format, args);
    }

    // 信息日志
    public static void i(String tag, String format, Object... args) {
        send("INFO", tag, format, args);
    }

    // 警告日志
    public static void w(String tag, String format, Object... args) {
        send("WARN", tag, format, args);
    }

    // 错误日志
    public static void e(String tag, String format, Object... args) {
        send("ERROR", tag, format, args);
    }

    // 致命错误日志
    public static void f(String tag, String format, Object... args) {
        send("FATAL", tag, format, args);
    }
    
    



}
