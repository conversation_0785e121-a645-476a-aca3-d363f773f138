package com.bytecat.algui.AlguiManager;
import android.view.View;
import com.bytecat.algui.AlguiViews.AlguiViewText;
import java.util.ArrayList;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/24 00:24
 * @Describe Alguu全局对象池(弃用)
 */
public class AlguiObjectManager {

    public static final String TAG = "AlguiObjectManager";

    private static final ArrayList<View> AlguiViews = new ArrayList<>();
    
    //添加视图对象
    public static void addView(View... views) {
        for (View v:views) {
            if (v != null) {
                AlguiViews.add(v);
            }
        }
    }
    //获取指定视图类的所有对象
    public static ArrayList<AlguiViewText> getView(String tag) {
        ArrayList<AlguiViewText> views = new ArrayList<>();
        for (View v : AlguiViews) {
            if (v != null && v instanceof AlguiViewText) {
                AlguiViewText textView = (AlguiViewText) v;
                if (textView.getTag() != null && textView.getTag().equals(tag)) {
                    views.add(textView);
                }
            }
        }
        return views;
    }
    
    //设置所有文本点击颜色
    public static void setAlguiTextClickColor(final int... colors) {
        final ArrayList<AlguiViewText> views = (ArrayList<AlguiViewText>)(getView(AlguiViewText.TAG));
        for (final AlguiViewText v:views) {
            if (v != null) {
                v.setCatCallback(new AlguiCallback.Click(){
                        public void click(boolean b) {
                            for (final AlguiViewText u:views) {
                                if (u!= null) {
                                    u.setCatBackColor(0);
                                }
                            }
                            v.setCatBackColor(colors);
                        }
                    }
                );
            }
        }
    }
    //设置所有文本大小
    public static void setAlguiTextSize(final float size) {
        final ArrayList<AlguiViewText> views = (ArrayList<AlguiViewText>)(getView(AlguiViewText.TAG));
        for (final AlguiViewText v:views) {
            if (v != null) {
                  v.setCatTextSize(size);
             
            }
        }
    }
    
    //设置所有文本字体
    public static void setAlguiTextTF(final String str) {
        final ArrayList<AlguiViewText> views = (ArrayList<AlguiViewText>)(getView(AlguiViewText.TAG));
        for (final AlguiViewText v:views) {
            if (v != null) {
                v.setCatTextTFAssets(str);

            }
        }
    }
}
