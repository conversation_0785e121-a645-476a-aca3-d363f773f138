package com.bytecat.algui;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import com.bytecat.algui.Game2FloatWindow;
import com.bytecat.algui.Game3FloatWindow;
import com.bytecat.algui.Game4FloatWindow;
import com.bytecat.algui.Game5FloatWindow;
import com.bytecat.algui.Main;
import com.bytecat.algui.AlguiManager.AlguiLog;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:33
 * @Describe Algui全局悬浮窗后台服务 用于全局显示悬浮窗
 */
public class AlguiService extends Service {

    public static final String TAG = "AlguiService";

    public Context context;

    //用于绑定AlguiService组件和其他组件之间的交互 返回null表示不支持绑定
    @Override
    public IBinder onBind(Intent Intent) {
        return null;
    }
    //创建后台服务
    @Override
    public void onCreate() {
        super.onCreate();
        context = getApplicationContext();
        AlguiLog.d(TAG, "AlguiService服务已创建");
    }

    // 支持多游戏悬浮窗，根据Intent参数选择
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null && intent.getStringExtra("game") != null) {
            String game = intent.getStringExtra("game");
            AlguiLog.d(TAG, "收到启动悬浮窗请求: " + game);
            
            // 使用悬浮窗管理器来控制悬浮窗的启动
            AlguiFloatWindowManager manager = AlguiFloatWindowManager.getInstance();
            
            // 检查是否已有相同游戏的悬浮窗在运行
            if (manager.isFloatWindowRunning(game)) {
                AlguiLog.w(TAG, "游戏 " + game + " 的悬浮窗已在运行");
                return START_NOT_STICKY;
            }
            
            // 如果当前有其他游戏的悬浮窗在运行，先关闭它
            if (manager.hasAnyFloatWindowRunning()) {
                String currentGame = manager.getCurrentActiveGame();
                if (currentGame != null && !currentGame.equals(game)) {
                    AlguiLog.d(TAG, "关闭当前活跃游戏悬浮窗: " + currentGame);
                    manager.closeFloatWindow(currentGame);
                }
            }
            
            // 启动新的悬浮窗
            switch (game) {
                case "天下布魔":
                    Game2FloatWindow.start(context);
                    manager.registerFloatWindow(game, null); // 注册悬浮窗
                    AlguiLog.d(TAG, "成功启动天下布魔悬浮窗");
                    break;
                case "潘吉亚异闻录":
                    Game3FloatWindow.start(context);
                    manager.registerFloatWindow(game, null); // 注册悬浮窗
                    AlguiLog.d(TAG, "成功启动潘吉亚异闻录悬浮窗");
                    break;
                case "贤者同盟":
                    Game4FloatWindow.start(context);
                    manager.registerFloatWindow(game, null); // 注册悬浮窗
                    AlguiLog.d(TAG, "成功启动贤者同盟悬浮窗");
                    break;
                case "樱井物语":
                    Game5FloatWindow.start(context);
                    manager.registerFloatWindow(game, null); // 注册悬浮窗
                    AlguiLog.d(TAG, "成功启动樱井物语悬浮窗");
                    break;
                case "星陨计划":
                    Main.start(context);
                    manager.registerFloatWindow(game, null); // 注册悬浮窗
                    AlguiLog.d(TAG, "成功启动星陨计划悬浮窗");
                    break;
                default:
                    AlguiLog.e(TAG, "未知游戏名称: " + game);
                    break;
            }
        } else {
            AlguiLog.w(TAG, "没有收到游戏参数，不启动任何悬浮窗");
        }
        return START_NOT_STICKY;
    }

    //后台服务销毁
    @Override
    public void onDestroy() {
        super.onDestroy();
        AlguiLog.d(TAG, "AlguiService服务已销毁");
    }

}
