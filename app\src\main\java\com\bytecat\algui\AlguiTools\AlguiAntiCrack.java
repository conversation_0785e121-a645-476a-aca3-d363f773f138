package com.bytecat.algui.AlguiTools;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import java.io.File;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 反破解保护系统
 * 提供多层防护机制，防止软件被破解和逆向
 */
public class AlguiAntiCrack {
    
    private static final String TAG = "AlguiAntiCrack";
    private static ScheduledExecutorService protectionService;
    private static boolean isProtectionActive = false;
    
    /**
     * 启动保护系统
     * @param context 应用上下文
     * @param checkIntervalSeconds 检查间隔（秒）
     */
    public static void startProtection(Context context, int checkIntervalSeconds) {
        if (isProtectionActive) {
            return;
        }
        
        isProtectionActive = true;
        protectionService = Executors.newSingleThreadScheduledExecutor();
        
        Runnable protectionTask = new Runnable() {
            @Override
            public void run() {
                // 执行全面检测
                if (isUnderAttack(context)) {
                    AlguiLog.w(TAG, "检测到破解攻击，执行保护措施");
                    executeProtection();
                }
                
                // 检查远程控制开关
                checkRemoteKillSwitch();
            }
        };
        
        // 立即执行一次，然后按间隔执行
        protectionService.scheduleAtFixedRate(protectionTask, 0, checkIntervalSeconds, TimeUnit.SECONDS);
        AlguiLog.i(TAG, "反破解保护系统已启动，检查间隔: " + checkIntervalSeconds + "秒");
    }
    
    /**
     * 停止保护系统
     */
    public static void stopProtection() {
        if (protectionService != null) {
            protectionService.shutdown();
            isProtectionActive = false;
            AlguiLog.i(TAG, "反破解保护系统已停止");
        }
    }
    
    /**
     * 综合攻击检测
     */
    public static boolean isUnderAttack(Context context) {
        // 1. 检测破解工具
        if (detectCrackingTools()) {
            AlguiLog.w(TAG, "检测到破解工具");
            return true;
        }
        
        // 2. 检测调试环境
        if (detectDebugging()) {
            AlguiLog.w(TAG, "检测到调试环境");
            return true;
        }
        
        // 3. 检测模拟器
        if (detectEmulator()) {
            AlguiLog.w(TAG, "检测到模拟器环境");
            return true;
        }
        
        // 4. 检测Hook框架
        if (detectHookFramework()) {
            AlguiLog.w(TAG, "检测到Hook框架");
            return true;
        }
        
        // 5. 检测应用签名
        if (detectSignatureTampering(context)) {
            AlguiLog.w(TAG, "检测到签名篡改");
            return true;
        }
        
        // 6. 检测网络代理
        if (AlguiToolNetwork.isUsingProxy()) {
            AlguiLog.w(TAG, "检测到网络代理");
            return true;
        }
        
        return false;
    }
    
    /**
     * 检测破解工具
     */
    private static boolean detectCrackingTools() {
        String[] crackingApps = {
            "com.chelpus.lackypatch",           // Lucky Patcher
            "com.dimonvideo.luckypatcher",
            "com.forpda.lp",
            "com.android.vending.billing.InAppBillingService.LACK", 
            "uret.jasi2169.patcher",            // Uret Patcher
            "madkite.freedom",                  // Freedom
            "cc.madkite.freedom",
            "zone.jasi2169.uretpatcher",
            "p.jasi2169.al",
            "com.android.vending.billing.InAppBillingService.LOCK"
        };
        
        String[] crackingFiles = {
            "/data/data/com.chelpus.lackypatch",
            "/data/data/com.dimonvideo.luckypatcher", 
            "/data/data/com.forpda.lp",
            "/data/data/uret.jasi2169.patcher",
            "/data/data/madkite.freedom",
            "/data/data/cc.madkite.freedom",
            "/system/app/LuckyPatcher.apk",
            "/system/priv-app/LuckyPatcher.apk",
            "/data/app/com.chelpus.lackypatch",
            "/data/app/com.android.vending.billing.InAppBillingService.LACK"
        };
        
        // 检查应用包名
        for (String app : crackingApps) {
            try {
                Runtime.getRuntime().exec("pm list packages " + app);
                return true;
            } catch (Exception e) {
                // 继续检查
            }
        }
        
        // 检查文件路径
        for (String path : crackingFiles) {
            if (new File(path).exists()) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检测调试环境
     */
    private static boolean detectDebugging() {
        // 检测调试器连接
        if (android.os.Debug.isDebuggerConnected()) {
            return true;
        }
        
        // 检测调试标志
        try {
            if ((android.os.Debug.getNativeHeapAllocatedSize() != android.os.Debug.getNativeHeapAllocatedSize())) {
                return true;
            }
        } catch (Exception e) {
            // 可能在调试环境中
            return true;
        }
        
        return false;
    }
    
    /**
     * 检测模拟器
     */
    private static boolean detectEmulator() {
        String[] emulatorSigns = {
            "google_sdk", "Emulator", "Android SDK built for x86", "sdk_gphone",
            "generic", "unknown", "Genymotion", "Andy", "ttVM_Hdragon",
            "Droid4X", "nox", "BlueStacks", "MEmu", "LDPlayer", "simulator"
        };
        
        String model = Build.MODEL.toLowerCase();
        String product = Build.PRODUCT.toLowerCase();
        String hardware = Build.HARDWARE.toLowerCase();
        String brand = Build.BRAND.toLowerCase();
        String board = Build.BOARD.toLowerCase();
        
        for (String sign : emulatorSigns) {
            String lowerSign = sign.toLowerCase();
            if (model.contains(lowerSign) || 
                product.contains(lowerSign) ||
                hardware.contains(lowerSign) ||
                brand.contains(lowerSign) ||
                board.contains(lowerSign)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检测Hook框架
     */
    private static boolean detectHookFramework() {
        String[] hookPaths = {
            "/data/data/de.robv.android.xposed.installer",  // Xposed
            "/system/framework/XposedBridge.jar",
            "/data/data/com.topjohnwu.magisk",              // Magisk
            "/sbin/magisk",
            "/data/data/com.frida.server",                  // Frida
            "/system/bin/frida-server",
            "/data/local/tmp/frida-server",
            "/system/xbin/frida-server",
            "/data/system/xposed.prop",
            "/system/lib/libxposed_art.so",
            "/system/lib64/libxposed_art.so"
        };
        
        for (String path : hookPaths) {
            if (new File(path).exists()) {
                return true;
            }
        }
        
        // 检测Xposed环境变量
        try {
            if (System.getProperty("xposed.bridge") != null) {
                return true;
            }
        } catch (Exception e) {
            // 继续检查
        }
        
        return false;
    }
    
    /**
     * 检测应用签名篡改
     */
    private static boolean detectSignatureTampering(Context context) {
        try {
            // 获取当前应用签名
            PackageManager pm = context.getPackageManager();
            String packageName = context.getPackageName();
            
            // 这里应该放你的正确签名哈希值
            String expectedSignature = "YOUR_EXPECTED_SIGNATURE_HASH";
            
            // 获取实际签名并比较
            // 注意：这里需要你提供正确的签名验证逻辑
            
        } catch (Exception e) {
            // 获取签名失败，可能被篡改
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查远程控制开关
     */
    private static void checkRemoteKillSwitch() {
        // 这里可以连接到你的服务器检查远程开关
        // 如果服务器返回关闭指令，立即执行保护措施
        
        // 示例：检查远程变量
        // 你可以在AlguiWin2FA的initNet()中设置一个特殊的远程变量
        // 比如 "app_kill_switch" = "true" 时执行保护
    }
    
    /**
     * 执行保护措施
     */
    private static void executeProtection() {
        AlguiLog.w(TAG, "执行保护措施：应用即将退出");
        
        // 可以在这里添加更多保护措施：
        // 1. 清除敏感数据
        // 2. 发送警告到服务器
        // 3. 记录攻击日志
        
        // 最终保护：强制退出
        System.exit(0);
    }
    
    /**
     * 获取保护状态
     */
    public static boolean isProtectionActive() {
        return isProtectionActive;
    }
}
