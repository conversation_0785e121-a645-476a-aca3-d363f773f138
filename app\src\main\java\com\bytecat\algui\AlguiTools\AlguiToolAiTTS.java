package com.bytecat.algui.AlguiTools;


import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONObject;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/19 02:12
 * @Describe Algui AI文本转语音
 */
public class AlguiToolAiTTS {

    public static final String TAG = "AlguiAI";
 
    //声音模型
    public static final String 女声_普通话_Xiaoxiao = "zh-CN-XiaoxiaoNeural"; // [普通话] Xiaoxiao 女
    public static final String 女声_普通话_Xiaoyi = "zh-CN-XiaoyiNeural"; // [普通话] Xiaoyi 女
    public static final String 女声_辽宁方言_Xiaobei = "zh-CN-liaoning-XiaobeiNeural"; // [辽宁方言] Xiaobei 女
    public static final String 女声_陕西方言_Xiaoni = "zh-CN-shaanxi-XiaoniNeural"; // [陕西方言] Xiaoni 女
    public static final String 女声_粤语_HiuGaai = "zh-HK-HiuGaaiNeural"; // [粤语] HiuGaai 女
    public static final String 女声_粤语_HiuMaan = "zh-HK-HiuMaanNeural"; // [粤语] HiuMaan 女
    public static final String 男声_港式英语_Yan = "en-HK-YanNeural"; // [港式英语] Yan 男
    public static final String 男声_普通话_Yunjian = "zh-CN-YunjianNeural"; // [普通话] Yunjian 男
    public static final String 男声_普通话_Yunxi = "zh-CN-YunxiNeural"; // [普通话] Yunxi 男
    public static final String 男声_普通话_Yunxia = "zh-CN-YunxiaNeural"; // [普通话] Yunxia 男
    public static final String 男声_普通话_Yunyang = "zh-CN-YunyangNeural"; // [普通话] Yunyang 男
    public static final String 男声_粤语_WanLung = "zh-HK-WanLungNeural"; // [粤语] WanLung 男
    public static final String 男声_港式英语_Sam = "en-HK-SamNeural"; // [港式英语] Sam 男
    public static final String 女声_美式英语_Ana = "en-US-AnaNeural"; // [美式英语] Ana 女
    public static final String 女声_美式英语_Aria = "en-US-AriaNeural"; // [美式英语] Aria 女
    public static final String 女声_美式英语_Ava = "en-US-AvaNeural"; // [美式英语] Ava 女
    public static final String 女声_美式英语_Emma = "en-US-EmmaNeural"; // [美式英语] Emma 女
    public static final String 女声_美式英语_Jenny = "en-US-JennyNeural"; // [美式英语] Jenny 女
    public static final String 女声_美式英语_Michelle = "en-US-MichelleNeural"; // [美式英语] Michelle 女
    public static final String 女声_英式英语_Libby = "en-GB-LibbyNeural"; // [英式英语] Libby 女
    public static final String 女声_英式英语_Maisie = "en-GB-MaisieNeural"; // [英式英语] Maisie 女
    public static final String 女声_英式英语_Sonia = "en-GB-SoniaNeural"; // [英式英语] Sonia 女
    public static final String 女声_日语_Nanami = "ja-JP-NanamiNeural"; // [日语] Nanami 女
    public static final String 女声_韩语_SunHi = "ko-KR-SunHiNeural"; // [韩语] SunHi 女
    public static final String 男声_美式英语_Andrew = "en-US-AndrewNeural"; // [美式英语] Andrew 男
    public static final String 男声_美式英语_Brian = "en-US-BrianNeural"; // [美式英语] Brian 男
    public static final String 男声_美式英语_Christopher = "en-US-ChristopherNeural"; // [美式英语] Christopher 男
    public static final String 男声_美式英语_Eric = "en-US-EricNeural"; // [美式英语] Eric 男
    public static final String 男声_美式英语_Guy = "en-US-GuyNeural"; // [美式英语] Guy 男
    public static final String 男声_美式英语_Roger = "en-US-RogerNeural"; // [美式英语] Roger 男
    public static final String 男声_美式英语_Steffan = "en-US-SteffanNeural"; // [美式英语] Steffan 男
    public static final String 男声_英式英语_Ryan = "en-GB-RyanNeural"; // [英式英语] Ryan 男
    public static final String 男声_英式英语_Thomas = "en-GB-ThomasNeural"; // [英式英语] Thomas 男
    public static final String 男声_日语_Keita = "ja-JP-KeitaNeural"; // [日语] Keita 男
    public static final String 男声_韩语_Hyunsu = "ko-KR-HyunsuNeural"; // [韩语] Hyunsu 男
    public static final String 男声_韩语_InJoon = "ko-KR-InJoonNeural"; // [韩语] InJoon 男

/*
    private static final String TAGP = "AES";
    private static final String pQ8rS9tU0vW1xY = "U2FsdGVkX18iSokkT3f/UJFikV7bYI+Uu6rR1tqlLijtGBJNpjYkJH+NWYAn5jwdQmaf5iILCJj9kIxKXgIxWBLPlmV182dCX+xkDMU046k30oBlObzASB0NroBDF01VpmYWIKDZh4XPAleqbPn8gg==";
    private static final String aBcD1eFgH2iJkL = "U2FsdGVkX1/Vpm9jyqYxEFW4+0rr//grGQhPAbLBzZqvRdwYkN/fp0cm32rRVKmJoz8jgZmKNZ5pOc9PcL1TxuRc/3nJpHNjkYUdar4TxT1or153BgjBhgC4syhITqh+";
    private static final String jK6lM7nO8pQ9rS = "6f5c8a9b1e8f70d7e84f1d7c55b9079d7b1b73c12b4e9f9b82d65a763e5b7a91";
   static{
        try {
            S_SERVER = new String(qR7s9_T3hXzYkLmN(
            qR7s9_T3hXzYkLmN(Base64.decode(pQ8rS9tU0vW1xY,Base64.DEFAULT), jK6lM7nO8pQ9rS),
            new String(qR7s9_T3hXzYkLmN(Base64.decode(aBcD1eFgH2iJkL,Base64.DEFAULT), jK6lM7nO8pQ9rS),StandardCharsets.UTF_8)),StandardCharsets.UTF_8);
            AlguiLog.d(S_SERVER);
        } catch (Exception e) {
            AlguiLog.d(e.getMessage());
        }
    }*/
    
    private AlguiToolAiTTS()  
    {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  
    
    
    
    //AI说话 参数：说什么，声音模型
    public static void speak(String content, String sound) {
        String v = String.format("character=%s&text=%s", 
        sound==null||sound.isEmpty()?女声_陕西方言_Xiaoni:sound, 
        content==null||content.isEmpty()?"你没有告诉我要说什么鸭":content
        );
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8"); 
        headers.put("Accept", "application/json");
        AlguiToolNetwork.POST("https://ai.bingal.com/cn/ai-tts/", v, headers , new AlguiToolNetwork.NetworkCallback() {
                @Override
                public void onSuccess(String response) {
                    try {
                        JSONObject jsonObject = new JSONObject(response);
                        JSONObject dataObject = jsonObject.getJSONObject("data");
                        String url = dataObject.getString("url");
                        if (url != null) {
                            AlguiToolAudio.playAudio_Url(url);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onFailure(String error) {
                    AlguiLog.e(TAG,"AI说话异常: " + error);
                }
            });
    }
    
    
    
    

}
