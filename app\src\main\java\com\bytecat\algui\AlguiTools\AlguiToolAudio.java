package com.bytecat.algui.AlguiTools;
import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.util.Base64;
import com.bytecat.algui.AlguiManager.AlguiLog;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:12
 * @Describe 音频工具
 */
public class AlguiToolAudio {

    public static final String TAG = "AlguiToolAudio";

    private AlguiToolAudio() {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  



    //将base64编码音频下载到本地音频文件 (参数2传入绝对路径)
    public static File audioBase2file(String base64, String filePath) {
        if (base64 == null || filePath == null) {
            return null;
        }
        byte[] decodedAudio = Base64.decode(base64, Base64.DEFAULT); // 解码Base64编码的音频数据
        try {
            File audioFile = new File(filePath); // 创建音频文件对象
            FileOutputStream outputStream = new FileOutputStream(audioFile); // 创建文件输出流，用于将音频数据写入文件
            outputStream.write(decodedAudio); // 将音频数据写入文件
            outputStream.close(); // 关闭文件输出流
            return audioFile; // 返回音频文件对象
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    //通用播放音频方法
    //支持：网络音频，base64音频，本地文件音频，文件名(自动识别为assets文件夹的音频)
    public static MediaPlayer playAudio(Context context, String Url_Base64_FilePath) {
        MediaPlayer m=null;
        if (Url_Base64_FilePath != null) {
            //字符串开头是http或https为网络资源
            if (Url_Base64_FilePath.startsWith("http://") || Url_Base64_FilePath.startsWith("https://")) {
                m = playAudio_Url(Url_Base64_FilePath);
                //对于base64资源检测长度
            } else if (Url_Base64_FilePath.length() > 32 + 16) {
                m = playAudio_Base64(context, Url_Base64_FilePath);
                //其它情况默认识别为本地资源
            } else {
                m = playAudio_File(context, Url_Base64_FilePath);
            }
        } 
        return m;
    }

    //播放base64音频
    private static int fileID=0;//全局音频文件ID
    private static final Map<String, String> audioBase64Map = new HashMap<>(); //已缓存的base64音频列表
    public static MediaPlayer playAudio_Base64(Context context, String base64) {
        MediaPlayer m=null;
        if (base64 != null && context != null) {
            String filePath = audioBase64Map.get(base64); //查找文件路径
            try {
                if (filePath == null) {
                    //如果文件路径不存在，则下载并保存
                    filePath = context.getCacheDir().getAbsolutePath() + "/AlguiAudio" + fileID + ".ogg"; //构造文件路径
                    if (audioBase2file(base64, filePath) == null) //下载并保存文件
                        return m;//失败结束
                    AlguiToolFileEnc.encryptFile(filePath); //进行加密
                    audioBase64Map.put(base64, filePath); //存入映射表
                    fileID++; //增加全局文件ID，避免文件名重复
                }
                AlguiToolFileEnc.decryptFile(filePath); //解密
                m = playAudio_File(context, filePath);    //播放音频
                AlguiToolFileEnc.encryptFile(filePath); //加密
            } catch (Exception e) {
                //捕获加密解密过程中可能的异常，但不应影响播放
                e.printStackTrace();
            }
        }
        return m;
    }

    //播放指定路径的音频文件
    public static MediaPlayer playAudio_File(Context context, String filePath) {
        MediaPlayer mediaPlayer=null;
        if (filePath != null) {
            try {
                mediaPlayer = new MediaPlayer(); // 创建媒体播放器对象
                mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC); // 设置媒体流类型为音乐流
                //设置数据源
                if (filePath.toLowerCase().startsWith("/assets/") || filePath.toLowerCase().startsWith("assets/") || !filePath.contains("/")) {
                    //对于assets文件夹下的文件
                    //查找最后一个/的位置
                    int lastSlashIndex = filePath.lastIndexOf('/');
                    //获取最后一个/之后的子字符串(文件名)
                    if (lastSlashIndex != -1) {
                        filePath = filePath.substring(lastSlashIndex + 1);
                    } 
                    AssetFileDescriptor afd = context.getAssets().openFd(filePath);
                    mediaPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
                } else {
                    //其它情况为本地路径文件
                    mediaPlayer.setDataSource(filePath); 
                }

                mediaPlayer.prepare(); // 准备媒体播放器
                mediaPlayer.start(); // 开始播放音频
                mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                        @Override
                        public void onCompletion(MediaPlayer mp) {
                            // 播放完成后释放资源
                            mp.release();
                        }
                    });


            } catch (IOException e) {
                e.printStackTrace();
                mediaPlayer.release();
                mediaPlayer = null;
            }
        }
        return mediaPlayer;
    }



    // 播放网络音频
    public static MediaPlayer playAudio_Url(String audioUrl) {
        MediaPlayer mediaPlayer=null;
        // 检查音频URL是否为空或无效
        if (audioUrl != null) {
            // 创建MediaPlayer实例
            mediaPlayer = new MediaPlayer();

            try {
                // 设置数据源为网络音频URL
                mediaPlayer.setDataSource(audioUrl);
                // 异步准备音频
                mediaPlayer.prepare();
                // 设置音频准备完成的监听器
                mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                        @Override
                        public void onPrepared(MediaPlayer mp) {
                            mp.start(); // 准备完成后开始播放
                        }
                    });
                // 设置音频播放完成的监听器
                mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                        @Override
                        public void onCompletion(MediaPlayer mp) {
                            mp.release(); // 播放完成后释放资源
                        }
                    });
                // 设置音频播放错误的监听器
                mediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
                        @Override
                        public boolean onError(MediaPlayer mp, int what, int extra) {
                            AlguiLog.e(TAG,"播放网络音频失败！错误代码: " + what);
                            mp.release(); // 出现错误时释放资源
                            return true; // 错误已处理
                        }
                    });

            } catch (IOException e) {
                // 处理IO异常
                e.printStackTrace();
                AlguiLog.e(TAG,"在播放网络音频时发生了IO错误：" + e.getMessage());
                mediaPlayer.release(); // 释放资源
                mediaPlayer=null;
            } catch (Exception e) {
                // 处理其他异常
                e.printStackTrace();
                AlguiLog.e(TAG,"在播放网络音频时发生了错误：" + e.getMessage());
                mediaPlayer.release(); // 释放资源
                mediaPlayer=null;
            }
        }
        return mediaPlayer;
    }
}
