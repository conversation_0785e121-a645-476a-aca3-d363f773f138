package com.bytecat.algui.AlguiTools;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/30 02:48 Algui缓存工具
 */
import android.content.Context;
import android.content.SharedPreferences;
import java.util.Set;
import java.util.HashSet;

public class AlguiToolCache {

    public static final String TAG = "AlguiToolCach";

    private static String cacheFileName="Algui";

    //获取文件名
    public static String getFileName(String name) {
        return cacheFileName;
    }
    //设置文件名
    public static void setFileName(String name) {
        cacheFileName = name;
    }

    //获取缓存文件对象
    private static SharedPreferences getSharedPreferences(Context context) {
        return context.getSharedPreferences(cacheFileName, Context.MODE_PRIVATE);
    }

    //保存数据到缓存文件 变量名，数据
    public static boolean saveData(Context context, String variableName, Object data) {
        SharedPreferences.Editor editor = getSharedPreferences(context).edit();

        if (data instanceof String) {
            editor.putString(variableName, (String) data);
        } else if (data instanceof Integer) {
            editor.putInt(variableName, (Integer) data);
        } else if (data instanceof Boolean) {
            editor.putBoolean(variableName, (Boolean) data);
        } else if (data instanceof Float) {
            editor.putFloat(variableName, (Float) data);
        } else if (data instanceof Long) {
            editor.putLong(variableName, (Long) data);
        } else {
            return false; // 不支持存储其他类型
        }
        editor.apply();
        return true;
    }

    //从缓存文件读取数据 变量名，默认值
    public static Object getData(Context context, String variableName, Object defaultValue) {
        SharedPreferences prefs = getSharedPreferences(context);
        if (defaultValue instanceof String) {
            return prefs.getString(variableName, (String) defaultValue);
        } else if (defaultValue instanceof Integer) {
            return prefs.getInt(variableName, (Integer) defaultValue);
        } else if (defaultValue instanceof Boolean) {
            return prefs.getBoolean(variableName, (Boolean) defaultValue);
        } else if (defaultValue instanceof Float) {
            return prefs.getFloat(variableName, (Float) defaultValue);
        } else if (defaultValue instanceof Long) {
            return prefs.getLong(variableName, (Long) defaultValue);
        } 
        return null; // 不支持其他类型
    }

    //删除缓存文件指定变量名的数据
    public static boolean deleteData(Context context, String variableName) {
        SharedPreferences.Editor editor = getSharedPreferences(context).edit();
        editor.remove(variableName);
        return editor.commit();
    }



}
