package com.bytecat.algui.AlguiTools;


import android.util.Log;
import java.io.File;
import java.io.FileOutputStream;
import java.io.RandomAccessFile;
import java.security.NoSuchAlgorithmException;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/17 08:45
 * @Describe Algui文件加密解密工具
 */
public class AlguiToolFileEnc {

    public static final String TAG = "AlguiToolFileEnc";

    private static final String ALGORITHM = "AES";//使用AES算法

    private static SecretKey KEY;//存储随机密钥
    //类加载时初始生成随机密钥
    static{
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
            keyGen.init(128); // 使用128位密钥
            KEY = keyGen.generateKey();
        } catch (NoSuchAlgorithmException e) {
            
        }
    }
    
    private AlguiToolFileEnc()  
    {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  

    /**
     * 加密文件方法
     * @param filePath 要加密的文件绝对路径
     * @throws Exception 可能抛出的异常，例如文件操作异常或加密异常
     */
    public static void encryptFile(String filePath) throws Exception {
        doCrypto(Cipher.ENCRYPT_MODE, filePath);
    }

    /**
     * 解密文件方法
     * @param filePath 要解密的文件绝对路径
     * @throws Exception 可能抛出的异常，例如文件操作异常或解密异常
     */
    public static void decryptFile(String filePath) throws Exception {
        doCrypto(Cipher.DECRYPT_MODE, filePath);
    }



    /**
     * 通用加解密方法
     * @param cipherMode 指定操作模式，Cipher.ENCRYPT_MODE 表示加密，Cipher.DECRYPT_MODE 表示解密
     * @param filePath 要加解密的文件，包含要加密或解密的数据
     * @throws Exception 可能抛出的异常，例如文件操作异常或加解密异常
     */
    public static void doCrypto(int cipherMode, String filePath) throws Exception {
        if (filePath == null) {
            return;
        }
        
        if(KEY==null){
           //可能被逆 结束程序
           System.exit(0);
        }
        //获取加密算法实例
        Cipher cipher = Cipher.getInstance(ALGORITHM != null ?ALGORITHM: "AES");
        //初始化设置加解密模式和密钥
        cipher.init(cipherMode, KEY);

        //原文件
        File file = new File(filePath);
        //临时文件
        File tempFile = new File(filePath + ".tmp");

        try (RandomAccessFile raf = new RandomAccessFile(file, "rws");
        FileOutputStream tempOutputStream = new FileOutputStream(tempFile)) {

            //读取原文件的所有字节
            byte[] inputBytes = new byte[(int) raf.length()];
            raf.readFully(inputBytes);

            //执行加密或解密操作
            byte[] outputBytes = cipher.doFinal(inputBytes);

            //将处理后的字节写入临时文件
            tempOutputStream.write(outputBytes);
        }
     
        //替换原文件为加密/解密后的文件
        //删除原文件
        if (!file.delete()) {
            throw new RuntimeException("加解密文件-错误：F1 -无法删除原文件");
        }
        //将临时文件重命名为原文件
        if (!tempFile.renameTo(file)) {
            throw new RuntimeException("加解密文件-错误：F2 -无法重命名原文件");
        }
    }
}
