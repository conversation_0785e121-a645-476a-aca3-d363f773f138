package com.bytecat.algui.AlguiTools;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ImageDecoder;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.AnimatedImageDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.util.Base64;
import android.util.Log;
import android.widget.Toast;
import com.bytecat.algui.AlguiActivity;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/24 22:36
 * @Describe 图像工具
 */
public class AlguiToolImage {

    public static final String TAG = "AlguiToolImage";



    //获取图像
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    //注意：回调是为网络图像准备的所以网络图像要在callback回调中获取图像，其它的可以不实现回调因为都是直接返回获取到的图像
    public static Drawable getImage(Context context, String Url_Base64_FilePath, AlguiCallback.Web callback) {
        Drawable d=null;
        if (Url_Base64_FilePath != null) {
            //字符串开头是http或https则设置为网络图片
            if (Url_Base64_FilePath.startsWith("http://") || Url_Base64_FilePath.startsWith("https://")) {
                getImageURL(Url_Base64_FilePath, callback);
                //对于base64图像检测长度
            } else if (Url_Base64_FilePath.length() > 32 + 16) {
                d = getImageBase64(Url_Base64_FilePath);
                //其它情况默认识别为本地图像文件
            } else {
                d = getImageFile(Url_Base64_FilePath, context);
            }
        } 
        return d;
    }

    //获取网络图片
    public static void getImageURL(final String url, final AlguiCallback.Web callback) {
        if (url != null) {
            // 添加调试日志
            AlguiLog.d(TAG, "开始加载网络图片: " + url);
            
            new AsyncTask<Void, Void, Drawable>() {
                @Override
                protected Drawable doInBackground(Void... voids) {
                    try {
                        // 简化网络检查逻辑
                        Context context = AlguiActivity.MainActivity;
                        if (context != null) {
                            // 检查网络连接
                            if (!AlguiToolNetwork.isNetworkAvailable(context)) {
                                AlguiLog.e(TAG, "网络连接不可用");
                                return null;
                            }
                        } else {
                            AlguiLog.w(TAG, "无法获取Context，跳过网络检查，直接尝试加载图片");
                        }

                        // 创建URL连接
                        URL imageUrl = new URL(url);
                        HttpURLConnection connection = (HttpURLConnection) imageUrl.openConnection();
                        
                        // 如果是HTTPS连接，尝试简化处理
                        if (connection instanceof HttpsURLConnection) {
                            HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                            // 设置不验证主机名
                            httpsConnection.setHostnameVerifier(new HostnameVerifier() {
                                @Override
                                public boolean verify(String hostname, SSLSession session) {
                                    return true;
                                }
                            });
                        }
                        
                        connection.setRequestMethod("GET");
                        connection.setConnectTimeout(10000);
                        connection.setReadTimeout(10000);
                        connection.setDoInput(true);

                        // 获取响应码
                        int responseCode = connection.getResponseCode();
                        AlguiLog.d(TAG, "图片加载响应码: " + responseCode);

                        if (responseCode == HttpURLConnection.HTTP_OK) {
                            InputStream inputStream = connection.getInputStream();
                            Drawable drawable = Drawable.createFromStream(inputStream, null);
                            inputStream.close();
                            
                            if (drawable != null) {
                                AlguiLog.d(TAG, "图片加载成功: " + url);
                                return drawable;
                            } else {
                                AlguiLog.e(TAG, "图片解析失败: " + url);
                            }
                        } else {
                            AlguiLog.e(TAG, "HTTP错误: " + responseCode + " - " + url);
                        }
                    } catch (Exception e) {
                        AlguiLog.e(TAG, "图片加载异常: " + e.getMessage() + " - " + url);
                    }
                    return null;
                }

                @Override
                protected void onPostExecute(Drawable result) {
                    if (callback != null) {
                        Message msg = new Message();
                        if (result != null) {
                            msg.what = 200;
                            msg.obj = result;
                            AlguiLog.d(TAG, "图片加载完成，发送成功消息");
                        } else {
                            msg.what = 404;
                            AlguiLog.e(TAG, "图片加载失败，发送错误消息");
                        }
                        callback.web(msg);
                    }
                }
            }.execute();
        } else {
            AlguiLog.e(TAG, "图片URL为空");
            if (callback != null) {
                Message msg = new Message();
                msg.what = 651;
                callback.web(msg);
            }
        }
    }

    /**
     * 获取网络图片（带SSL证书处理）
     * 如果SSL证书验证失败，会尝试使用备用方法
     */
    public static void getImageURLWithSSLHandling(final String url, final AlguiCallback.Web callback) {
        if (url != null) {
            AlguiLog.d(TAG, "开始加载网络图片（带SSL处理）: " + url);
            
            new AsyncTask<Void, Void, Drawable>() {
                @Override
                protected Drawable doInBackground(Void... voids) {
                    // 首先尝试正常加载
                    Drawable result = loadImageWithSSL(url);
                    if (result != null) {
                        return result;
                    }
                    
                    // 如果失败，尝试使用HTTP（如果原URL是HTTPS）
                    if (url.startsWith("https://")) {
                        String httpUrl = url.replace("https://", "http://");
                        AlguiLog.d(TAG, "SSL加载失败，尝试HTTP: " + httpUrl);
                        return loadImageWithSSL(httpUrl);
                    }
                    
                    return null;
                }

                @Override
                protected void onPostExecute(Drawable result) {
                    if (callback != null) {
                        Message msg = new Message();
                        if (result != null) {
                            msg.what = 200;
                            msg.obj = result;
                            AlguiLog.d(TAG, "图片加载完成，发送成功消息");
                        } else {
                            msg.what = 404;
                            AlguiLog.e(TAG, "图片加载失败，发送错误消息");
                        }
                        callback.web(msg);
                    }
                }
            }.execute();
        } else {
            AlguiLog.e(TAG, "图片URL为空");
            if (callback != null) {
                Message msg = new Message();
                msg.what = 651;
                callback.web(msg);
            }
        }
    }

    /**
     * 使用SSL处理加载图片
     */
    private static Drawable loadImageWithSSL(String url) {
        try {
            // 简化网络检查逻辑
            Context context = AlguiActivity.MainActivity;
            if (context != null) {
                // 检查网络连接
                if (!AlguiToolNetwork.isNetworkAvailable(context)) {
                    AlguiLog.e(TAG, "网络连接不可用");
                    return null;
                }
            } else {
                AlguiLog.w(TAG, "无法获取Context，跳过网络检查，直接尝试加载图片");
            }

            // 创建URL连接
            URL imageUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) imageUrl.openConnection();
            
            // 如果是HTTPS连接，尝试简化处理
            if (connection instanceof HttpsURLConnection) {
                HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                // 设置不验证主机名
                httpsConnection.setHostnameVerifier(new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                });
            }
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.setDoInput(true);

            // 获取响应码
            int responseCode = connection.getResponseCode();
            AlguiLog.d(TAG, "图片加载响应码: " + responseCode);

            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = connection.getInputStream();
                Drawable drawable = Drawable.createFromStream(inputStream, null);
                inputStream.close();
                
                if (drawable != null) {
                    AlguiLog.d(TAG, "图片加载成功: " + url);
                    return drawable;
                } else {
                    AlguiLog.e(TAG, "图片解析失败: " + url);
                }
            } else {
                AlguiLog.e(TAG, "HTTP错误: " + responseCode + " - " + url);
            }
        } catch (Exception e) {
            AlguiLog.e(TAG, "图片加载异常: " + e.getMessage() + " - " + url);
        }
        return null;
    }

    //获取本地图像
    //1.可以传入 本地文件路径 例如：/data/user/0/com.bytecat.algui/cache/image.png
    //注意：本地文件路径别人手机上没有这个图片，所以这只针对于从网络下载图像到本地，然后引用此下载好的图像
    //2.可以传入 图像文件名 自动在Assets文件夹下搜索此图像文件
    //3.可以传入 项目根目录Assets路径 例如：/assets/image.png
    public static Drawable getImageFile(String filePath, Context context) {
        Drawable d=null;
        if (filePath != null) {
            try {
                //开头是assets文件夹或者是一个图片文件名则获取assets文件夹图像
                if (filePath.toLowerCase().startsWith("/assets/") || filePath.toLowerCase().startsWith("assets/") || !filePath.contains("/")) {
                    //查找最后一个/的位置
                    int lastSlashIndex = filePath.lastIndexOf('/');
                    //获取最后一个/之后的子字符串(图片文件名)
                    if (lastSlashIndex != -1) {
                        filePath = filePath.substring(lastSlashIndex + 1);
                    } 
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {//对于安卓9以上
                        d = ImageDecoder.decodeDrawable(ImageDecoder.createSource(context.getAssets(), filePath));
                    } else {
                        try {
                            Bitmap bitmap = BitmapFactory.decodeStream(context.getAssets().open(filePath));
                            if (bitmap != null) {
                                d = new BitmapDrawable(context.getResources(), bitmap);
                            }
                        } catch (IOException e) {}
                    }
                } else {
                    //其它情况为本地路径图片
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {//对于安卓9以上
                        d = ImageDecoder.decodeDrawable(ImageDecoder.createSource(new File(filePath)));
                    } else {
                        Bitmap bitmap = BitmapFactory.decodeFile(filePath);
                        if (bitmap != null) {
                            d = new BitmapDrawable(context.getResources(), bitmap);
                        }
                    }

                }
            } catch (IOException e) {
                d = null;
                e.printStackTrace();
            }
        }
        if (d != null) {
            //对于gif则开始动画
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P)
                if (d instanceof AnimatedImageDrawable)
                    ((AnimatedImageDrawable) d).start();
        }
        return d;
    }


    //获取Base64图像
    //对Base64长度有限制，太大可能出现异常
    public static Drawable getImageBase64(String base64String) {
        Drawable d=null;
        if (base64String != null) {
            try {
                byte[] decodedBytes = Base64.decode(base64String, Base64.DEFAULT);
                InputStream inputStream = new ByteArrayInputStream(decodedBytes);
                d = Drawable.createFromStream(inputStream, null);
            } catch (IllegalArgumentException e) {
                d = null;
                e.printStackTrace();
            }
        }
        if (d != null) {
            //对于gif则开始动画
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P)
            if (d instanceof AnimatedImageDrawable)
                ((AnimatedImageDrawable) d).start();
        }
        return d;
    }



    //下载图片到本地
    public static File saveDrawableToFile(Context context, Drawable drawable, String filePath) {
        if (drawable == null || filePath == null) {
            return null;
        }
        //结尾是分隔符代表没有图片名称
        if (filePath.endsWith("/")) {
            filePath = filePath + "AlguiImage.png";//使用默认文件名
        }

        File imageFile = new File(filePath);
        String fileName = imageFile.getName();//获取文件名image.png
        // 检查路径是否是外部存储目录并且是单个图片文件名则下载到相册
        if ((filePath.startsWith("/storage/emulated/0/") || filePath.startsWith("/mnt/sdcard/")) || !filePath.contains("/")) {
            if (!fileName.contains(".")) {
                fileName += ".png";//没有扩展名则添加
            }
            // 对于 Android 10（API 29）及以上版本，使用 getExternalFilesDir
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                File appSpecificDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
                if (appSpecificDir != null) {
                    imageFile = new File(appSpecificDir, fileName);
                    filePath = imageFile.getAbsolutePath();
                }
            } else {
                // 低版本可以使用公共存储路径
                File downloadDirectory = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES);
                if (!downloadDirectory.exists()) {
                    downloadDirectory.mkdirs();
                }
                imageFile = new File(downloadDirectory, fileName);
                filePath = imageFile.getAbsolutePath();
            }
        }




        //文件已存在则重命名
        if (imageFile.exists()) {
            // 获取文件名和文件后缀

            //获取名称image
            String fileBaseName = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
            //获取扩展名.png
            String fileExtension = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
            String path = imageFile.getParent() + "/";

            int i = 1;
            //生成带编号的文件名 直到文件不存在
            while (imageFile.exists()) {
                filePath = path + fileBaseName + i + fileExtension;
                imageFile = new File(filePath);
                i++;
            }
        }


        // 将 Drawable 转换为 Bitmap
        Bitmap bitmap = drawableToBitmap(drawable);

        // 创建父目录（如果不存在的话）
        File parentDir = imageFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        try {
            FileOutputStream fos = new FileOutputStream(imageFile);
            // 保存为 PNG 格式
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
            fos.flush();
            AlguiLog.d(TAG, "下载图像完成 路径：" + filePath);
        } catch (IOException e) {
            imageFile = null;
            AlguiLog.d(TAG, "下载图像异常 异常：" + e.getMessage());
            e.printStackTrace();
        }

        return imageFile;
    }

    // 将 Bitmap 转换为 Drawable
    public static Drawable bitmapToDrawable(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        return new BitmapDrawable(Resources.getSystem(), bitmap);
    }
    // 将 Drawable 转换为 Bitmap
    public static Bitmap drawableToBitmap(Drawable drawable) {
        if (drawable == null) {
            return null;
        }
        if (drawable instanceof BitmapDrawable) {
            return ((BitmapDrawable) drawable).getBitmap().copy(Bitmap.Config.ARGB_8888, true);
        }

        int width = drawable.getIntrinsicWidth();
        int height = drawable.getIntrinsicHeight();

        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);

        return bitmap;
    }

    //处理图像模糊
    public static Drawable psImageBlur(Drawable image, int radius, Context context) {
        //确保不为动态图片并且模糊半径不是负数
        if (image != null && image instanceof BitmapDrawable && radius > 0) {
            try {
            Bitmap bitmap = ((BitmapDrawable) image).getBitmap().copy(Bitmap.Config.ARGB_8888, true);
                // 1. 先缩放到最大边不超过200像素，防止大图爆内存
                int maxSize = 200; // 可根据实际UI需求调整
            int w = bitmap.getWidth();
            int h = bitmap.getHeight();
                if (w > maxSize || h > maxSize) {
                    float scale = Math.min((float)maxSize / w, (float)maxSize / h);
                    int newW = Math.max(1, (int)(w * scale));
                    int newH = Math.max(1, (int)(h * scale));
                    bitmap = Bitmap.createScaledBitmap(bitmap, newW, newH, true);
                    w = newW;
                    h = newH;
                }
            int[] pix = new int[w * h];
            bitmap.getPixels(pix, 0, w, 0, 0, w, h);
            int wm = w - 1;
            int hm = h - 1;
            int wh = w * h;
            int div = radius + radius + 1;
            int r[] = new int[wh];
            int g[] = new int[wh];
            int b[] = new int[wh];
            int rsum, gsum, bsum, x, y, i, p, yp, yi, yw;
            int vmin[] = new int[Math.max(w, h)];
            int divsum = (div + 1) >> 1;
            divsum *= divsum;
            int dv[] = new int[256 * divsum];
            for (i = 0; i < 256 * divsum; i++) {
                dv[i] = (i / divsum);
            }
            yw = yi = 0;
            int[][] stack = new int[div][3];
            int stackpointer;
            int stackstart;
            int[] sir;
            int rbs;
            int r1 = radius + 1;
            int routsum, goutsum, boutsum;
            int rinsum, ginsum, binsum;
            for (y = 0; y < h; y++) {
                rinsum = ginsum = binsum = routsum = goutsum = boutsum = rsum = gsum = bsum = 0;
                for (i = -radius; i <= radius; i++) {
                    p = pix[yi + Math.min(wm, Math.max(i, 0))];
                    sir = stack[i + radius];
                    sir[0] = (p & 0xff0000) >> 16;
                    sir[1] = (p & 0x00ff00) >> 8;
                    sir[2] = (p & 0x0000ff);
                    rbs = r1 - Math.abs(i);
                    rsum += sir[0] * rbs;
                    gsum += sir[1] * rbs;
                    bsum += sir[2] * rbs;
                    if (i > 0) {
                        rinsum += sir[0];
                        ginsum += sir[1];
                        binsum += sir[2];
                    } else {
                        routsum += sir[0];
                        goutsum += sir[1];
                        boutsum += sir[2];
                    }
                }
                stackpointer = radius;
                for (x = 0; x < w; x++) {
                    r[yi] = dv[rsum];
                    g[yi] = dv[gsum];
                    b[yi] = dv[bsum];
                    rsum -= routsum;
                    gsum -= goutsum;
                    bsum -= boutsum;
                    stackstart = stackpointer - radius + div;
                    sir = stack[stackstart % div];
                    routsum -= sir[0];
                    goutsum -= sir[1];
                    boutsum -= sir[2];
                    if (y == 0) {
                        vmin[x] = Math.min(x + radius + 1, wm);
                    }
                    p = pix[yw + vmin[x]];
                    sir[0] = (p & 0xff0000) >> 16;
                    sir[1] = (p & 0x00ff00) >> 8;
                    sir[2] = (p & 0x0000ff);
                    rinsum += sir[0];
                    ginsum += sir[1];
                    binsum += sir[2];
                    rsum += rinsum;
                    gsum += ginsum;
                    bsum += binsum;
                    stackpointer = (stackpointer + 1) % div;
                    sir = stack[(stackpointer) % div];
                    routsum += sir[0];
                    goutsum += sir[1];
                    boutsum += sir[2];
                    rinsum -= sir[0];
                    ginsum -= sir[1];
                    binsum -= sir[2];
                    yi++;
                }
                yw += w;
            }
            for (x = 0; x < w; x++) {
                rinsum = ginsum = binsum = routsum = goutsum = boutsum = rsum = gsum = bsum = 0;
                yp = -radius * w;
                for (i = -radius; i <= radius; i++) {
                    yi = Math.max(0, yp) + x;
                    sir = stack[i + radius];
                    sir[0] = r[yi];
                    sir[1] = g[yi];
                    sir[2] = b[yi];
                    rbs = r1 - Math.abs(i);
                    rsum += r[yi] * rbs;
                    gsum += g[yi] * rbs;
                    bsum += b[yi] * rbs;
                    if (i > 0) {
                        rinsum += sir[0];
                        ginsum += sir[1];
                        binsum += sir[2];
                    } else {
                        routsum += sir[0];
                        goutsum += sir[1];
                        boutsum += sir[2];
                    }
                        if (i < h - 1) {
                        yp += w;
                    }
                }
                yi = x;
                stackpointer = radius;
                for (y = 0; y < h; y++) {
                    pix[yi] = (0xff000000 & pix[yi]) | (dv[rsum] << 16) | (dv[gsum] << 8) | dv[bsum];
                    rsum -= routsum;
                    gsum -= goutsum;
                    bsum -= boutsum;
                    stackstart = stackpointer - radius + div;
                    sir = stack[stackstart % div];
                    routsum -= sir[0];
                    goutsum -= sir[1];
                    boutsum -= sir[2];
                    if (x == 0) {
                            vmin[y] = Math.min(y + r1, h - 1) * w;
                    }
                    p = x + vmin[y];
                    sir[0] = r[p];
                    sir[1] = g[p];
                    sir[2] = b[p];
                    rinsum += sir[0];
                    ginsum += sir[1];
                    binsum += sir[2];
                    rsum += rinsum;
                    gsum += ginsum;
                    bsum += binsum;
                    stackpointer = (stackpointer + 1) % div;
                    sir = stack[stackpointer];
                    routsum += sir[0];
                    goutsum += sir[1];
                    boutsum += sir[2];
                    rinsum -= sir[0];
                    ginsum -= sir[1];
                    binsum -= sir[2];
                    yi += w;
                }
            }
            bitmap.setPixels(pix, 0, w, 0, 0, w, h);
            image = new BitmapDrawable(context.getResources(), bitmap);
            } catch (OutOfMemoryError e) {
                // 内存溢出，降级处理，返回原图并打印异常
                e.printStackTrace();
                return image;
            }
        }
        return image;
    }


}
