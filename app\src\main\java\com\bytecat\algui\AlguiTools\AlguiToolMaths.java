package com.bytecat.algui.AlguiTools;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/27 00:43
 * @Describe Algui数学
 */
import com.bytecat.algui.AlguiHacker.Vector2;
import com.bytecat.algui.AlguiHacker.Vector3;

public class AlguiToolMaths {

    public static final String TAG = "AlguiToolMaths";

    //世界3d坐标到相对4x4矩阵的3d坐标 参数：3d坐标，4x4矩阵阵列，屏幕分辨率
    public static Vector3 worldV3To4x4V3(Vector3 v3, float[] matrixArray, float px, float py) {
        Vector3 v23=new Vector3();
        if (v3 != null && matrixArray.length >= 4 * 4) {
            //计算屏幕中心点
            float pxMidpoint=px / 2;
            float pyMidpoint=py / 2;
            v23.z = matrixArray[3] * v3.x + matrixArray[7] * v3.z + matrixArray[11] * v3.y + matrixArray[15];
            if (v23.z == 0) v23.z = 1;  //防止除零异常
            v23.x = pxMidpoint + (matrixArray[0] * v3.x + matrixArray[4] * v3.z + matrixArray[8] * v3.y + matrixArray[12]) / v23.z * pxMidpoint;
            v23.y = pyMidpoint - (matrixArray[1] * v3.x + matrixArray[5] * v3.z + matrixArray[9] * v3.y + matrixArray[13]) / v23.z * pyMidpoint;
            
        }
        return v23;
    }
    
    
}
