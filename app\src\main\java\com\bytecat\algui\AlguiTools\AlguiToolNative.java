package com.bytecat.algui.AlguiTools;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/10/28 09:26
 * @Describe 库工具
 */


import com.bytecat.algui.AlguiManager.AlguiLog;
import java.util.HashSet;
import java.util.Set;

public class AlguiToolNative {

    public static final String TAG = "AlguiToolNative";

    private AlguiToolNative() {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  
    
    //用来跟踪已加载的库
    private static Set<String> loadedLibraries = new HashSet<>();
    //加载动态库
    public static boolean loadLibrary(String name) {
        boolean result = true;

        // 检查库是否已经加载
        if (loadedLibraries.contains(name)) {
            AlguiLog.d(TAG,"\""+name + "\"库已经加载，无需重复加载");
            return result;
        }

        AlguiLog.d(TAG,"开始加载\""+name+"\"库");
        try {
            System.loadLibrary(name);
            loadedLibraries.add(name); //记录该库
            AlguiLog.d(TAG,"\""+name + "\"库已加载");
        } catch (UnsatisfiedLinkError e) {
            AlguiLog.d(TAG,"\"" + name + "\" 库加载异常：" + e.getMessage());
            e.printStackTrace();
            result = false;
        }

        return result;
    }


}
