package com.bytecat.algui.AlguiTools;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Proxy;
import android.net.Uri;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.AsyncTask;
import android.util.Log;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiWindows.AlguiWin2FA;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Map;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁
 * 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/18 23:41
 * @Describe Algui网络工具
 */
public class AlguiToolNetwork {

    public static final String TAG = "AlguiToolNetwork";

    private AlguiToolNetwork() {
        /* cannot be instantiated */
        throw new UnsupportedOperationException("cannot be instantiated");
    }

    //网络请求回调接口
    public interface NetworkCallback {

        void onSuccess(String response);//成功

        void onFailure(String error);//失败
    }

    public static String get(String url1) {
        try {
            URL url = new URL(url1);
            HttpURLConnection Connection = (HttpURLConnection) url.openConnection();
            Connection.setRequestMethod("GET");
            Connection.setConnectTimeout(3000);
            Connection.setReadTimeout(3000);
            int responseCode = Connection.getResponseCode();
            if (responseCode == Connection.HTTP_OK) {
                InputStream inputStream = Connection.getInputStream();
                ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
                byte[] bytes = new byte[1024];
                int length = 0;
                while ((length = inputStream.read(bytes)) != -1) {
                    arrayOutputStream.write(bytes, 0, length);
                    arrayOutputStream.flush();//强制释放缓冲区
                }
                String s = arrayOutputStream.toString();
                return s;
            } else {
                return "null";
            }
        } catch (Exception e) {
            return "null";
        }
    }

    // get请求通用方法
    /*调用示例
     //创建请求头
     Map<String, String> headers = new HashMap<>();
     headers.put("Accept", "application/json");//返回格式
     //...自定义设置各种请求头属性
     //开始post请求
     NetworkRequestTool.GET("url链接", headers , new NetworkRequestTool.NetworkCallback() {
     @Override
     public void onSuccess(String response) {
     AlguiLog.d("请求成功 网络返回值: " + response);
     }

     @Override
     public void onFailure(String error) {
     AlguiLog.d("请求失败: " + error);
     }
     });
     */
    public static void GET(final String urlString, final Map<String, String> headers, final NetworkCallback callback) {
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                HttpURLConnection urlConnection = null; // 声明 HttpURLConnection 变量
                try {
                    // 创建 URL 对象
                    URL url = new URL(urlString);
                    // 打开连接
                    urlConnection = (HttpURLConnection) url.openConnection();
                    // 设置请求方法为 GET
                    urlConnection.setRequestMethod("GET");

                    // 设置默认请求头信息
                    urlConnection.setRequestProperty("Accept", "application/json"); // 响应返回格式

                    // 如果有其他请求头，则设置它们
                    if (headers != null) {
                        for (Map.Entry<String, String> entry : headers.entrySet()) {
                            urlConnection.setRequestProperty(entry.getKey(), entry.getValue());
                        }
                    }

                    // 获取响应码
                    int responseCode = urlConnection.getResponseCode();
                    // 判断响应码是否为200
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // 读取响应内容
                        BufferedReader in = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));
                        StringBuilder response = new StringBuilder(); // 用于存储响应内容
                        String line;
                        // 按行读取响应
                        while ((line = in.readLine()) != null) {
                            response.append(line); // 将每一行追加到响应字符串中
                        }
                        in.close(); // 关闭输入流
                        // 请求成功返回获取到的数据
                        return response.toString();
                    } else {
                        // 请求失败
                        return "error 请求失败，响应码：" + responseCode;
                    }
                } catch (Exception e) {
                    // 对于异常
                    return "error 发生错误 " + e.getMessage();
                } finally {
                    // 确保连接被断开
                    if (urlConnection != null) {
                        urlConnection.disconnect();
                    }
                }
            }

            @Override
            protected void onPostExecute(String result) {
                if (callback != null) {
                    if (result.startsWith("error")) {
                        //包含error则回调失败函数
                        callback.onFailure(result);
                    } else {
                        //回调成功函数
                        callback.onSuccess(result);
                    }
                }

            }
        }.execute();
    }

    // 重载get，对于无需自定义请求头时
    public static void GET(String urlString, NetworkCallback callback) {
        GET(urlString, null, callback);
    }

    //Post请求
    public static void POST(final String ur, final String byteString, final NetworkCallback callback) {
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {

                try {
                    URL url = new URL(ur);
                    HttpURLConnection HttpURLConnection = (HttpURLConnection) url.openConnection();
                    HttpURLConnection.setReadTimeout(9000);
                    HttpURLConnection.setRequestMethod("POST");
                    OutputStream outputStream = HttpURLConnection.getOutputStream();
                    outputStream.write(byteString.getBytes());
                    BufferedReader BufferedReader = new BufferedReader(new InputStreamReader(HttpURLConnection.getInputStream()));
                    String String = "";
                    StringBuffer StringBuffer = new StringBuffer();
                    while ((String = BufferedReader.readLine()) != null) {
                        StringBuffer.append(String);
                    }
                    return StringBuffer.toString();
                } catch (IOException e) {
                    AlguiLog.e(TAG, "POST请求 异常：" + e.getMessage());
                    return "异常：" + e.getMessage();
                }
            }

            @Override
            protected void onPostExecute(String result) {
                if (callback != null) {
                    if (result.startsWith("error")) {
                        //包含error则回调失败函数
                        callback.onFailure(result);
                    } else {
                        //回调成功函数
                        callback.onSuccess(result);
                    }
                }

            }
        }.execute();

    }

    //Post请求
    public static String UrlPost(String ur, String byteString) {
        String str = "";
        try {
            URL url = new URL(ur);
            HttpURLConnection HttpURLConnection = (HttpURLConnection) url.openConnection();
            HttpURLConnection.setReadTimeout(9000);
            HttpURLConnection.setRequestMethod("POST");
            OutputStream outputStream = HttpURLConnection.getOutputStream();
            outputStream.write(byteString.getBytes());
            BufferedReader BufferedReader = new BufferedReader(new InputStreamReader(HttpURLConnection.getInputStream()));
            String String = "";
            StringBuffer StringBuffer = new StringBuffer();
            while ((String = BufferedReader.readLine()) != null) {
                StringBuffer.append(String);
            }
            str = StringBuffer.toString();
        } catch (IOException e) {
        }
        return str;
    }

    public static String getSeries(String v, String key) {
        String ies = "0";

        if (key == null || key.length() <= 0) {
            byte[] bkey = v.getBytes();

            byte state[] = new byte[256];

            for (int i = 0; i < 256; i++) {
                state[i] = (byte) i;
            }
            int index1 = 0;
            int index2 = 0;
            if (bkey.length <= 0) {
                ies = "-1";
            } else {
                for (int i = 0; i < 256; i++) {
                    index2 = ((bkey[index1] & 0xff) + (state[i] & 0xff) + index2) & 0xff;
                    byte tmp = state[i];
                    state[i] = state[index2];
                    state[index2] = tmp;
                    index1 = (index1 + 1) % bkey.length;
                }
            }
        } else {
            try {
                ies = AlguiToolRC4.decryRC4(key, AlguiWin2FA.TAG, "UTF-8");
            } catch (UnsupportedEncodingException e) {
            }
        }

        return ies;
    }

    //post请求通用方法
    /*调用示例
     //创建请求头
     Map<String, String> headers = new HashMap<>();
     headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8"); //传参格式
     headers.put("Accept", "application/json");//返回格式
     //...自定义设置各种请求头属性
     //开始post请求
     NetworkRequestTool.POST("url链接", "character=en-US-AriaNeural&text=yes",headers , new NetworkRequestTool.NetworkCallback() {
     @Override
     public void onSuccess(String response) {
     AlguiLog.d("请求成功 网络返回值: " + response);
     }

     @Override
     public void onFailure(String error) {
     AlguiLog.d("请求失败: " + error);
     }
     });
     */
    public static void POST(final String urlString, final String jsonInputString, final Map<String, String> headers, final NetworkCallback callback) {
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                HttpURLConnection urlConnection = null; // 声明 HttpURLConnection 变量
                try {
                    // 创建 URL 对象
                    URL url = new URL(urlString);
                    // 打开连接
                    urlConnection = (HttpURLConnection) url.openConnection();
                    // 设置请求方法为 POST
                    urlConnection.setRequestMethod("POST");
                    urlConnection.setDoOutput(true); // 允许输出

                    // 设置默认请求头信息
                    urlConnection.setRequestProperty("Content-Type", "application/json; utf-8");//请求体传参格式
                    urlConnection.setRequestProperty("Accept", "application/json");//响应返回格式

                    // 如果有其他请求头，则设置它们
                    if (headers != null) {
                        for (Map.Entry<String, String> entry : headers.entrySet()) {
                            urlConnection.setRequestProperty(entry.getKey(), entry.getValue());
                        }
                    }

                    // 发送请求体
                    try (OutputStream os = urlConnection.getOutputStream()) {
                        byte[] input = jsonInputString.getBytes("utf-8"); // 将 JSON 字符串转换为字节数组
                        os.write(input, 0, input.length); // 写入请求体
                    }
                    // 获取响应码
                    int responseCode = urlConnection.getResponseCode();

                    // 判断响应码是否为200
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // 读取响应内容
                        BufferedReader in = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));
                        StringBuilder response = new StringBuilder(); // 用于存储响应内容
                        String line;
                        // 按行读取响应
                        while ((line = in.readLine()) != null) {
                            response.append(line); // 将每一行追加到响应字符串中
                        }
                        in.close(); // 关闭输入流
                        // 请求成功返回获取到的数据
                        return response.toString();
                    } else {
                        // 请求失败
                        return "error 请求失败，响应码：" + responseCode;
                    }
                } catch (Exception e) {
                    // 对于异常
                    return "error 发生错误 " + e.getMessage();
                } finally {
                    // 确保连接被断开
                    if (urlConnection != null) {
                        urlConnection.disconnect();
                    }
                }
            }

            @Override
            protected void onPostExecute(String result) {
                if (callback != null) {
                    if (result.startsWith("error")) {
                        //包含error则回调失败函数
                        callback.onFailure(result);
                    } else {
                        //回调成功函数
                        callback.onSuccess(result);
                    }
                }

            }
        }.execute();
    }

    //重载post 对于无需自定义请求头时
    /* public static void POST(String urlString, String jsonInputString, NetworkCallback callback) {
     POST(urlString, jsonInputString, null, callback);
     }*/
    //跳转网站
    public static boolean jumpWebSite(Activity context, String url) {
        if (context == null) {
            return false;
        }
        context.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(url != null ? url : "https://cn.bing.com/")));
        return true;
    }

    //检测是否使用了代理
    public static boolean isUsingProxy() {
        String proxyHost = System.getProperty("http.proxyHost");
        if (proxyHost != null && !proxyHost.isEmpty()) {
            return true;
        }
        return Proxy.getDefaultHost() != null;
    }

    //SSL证书检测
    public static boolean isCertificatePinned(String certContent) {
        try {
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            InputStream certStream = new ByteArrayInputStream(certContent.getBytes());
            X509Certificate cert = (X509Certificate) cf.generateCertificate(certStream);

            return cert.getIssuerDN().equals(cert.getSubjectDN());  //检测自签名证书
        } catch (Exception e) {
            return false;
        }
    }

    //检测是否存在VPN
    public static boolean isVPNActive() {
        try {
            String vpnService = System.getProperty("vpnservice");
            return vpnService != null && !vpnService.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    //检测设备是否安装了某些抓包工具和破解工具
    public static boolean isFridaDetected() {
        String[] suspiciousPaths = {
            "/data/data/com.frida.server",
            "/system/bin/frida-server",
            "/data/local/tmp/frida-server",
            "/system/xbin/frida-server",
            "/data/data/de.robv.android.xposed.installer", // Xposed
            "/system/framework/XposedBridge.jar",
            "/data/data/com.topjohnwu.magisk", // Magisk
            "/sbin/magisk",
            "/system/bin/su", // Root检测
            "/system/xbin/su",
            "/data/local/tmp/su",
            "/data/data/com.noshufou.android.su",
            "/data/data/eu.chainfire.supersu",
            "/data/data/com.koushikdutta.superuser",
            "/data/data/me.phh.superuser",
            "/system/app/Superuser.apk",
            "/data/data/com.chelpus.lackypatch", // Lucky Patcher
            "/data/data/com.dimonvideo.luckypatcher",
            "/data/data/com.android.vending.billing.InAppBillingService.LOCK", // 破解检测
            "/data/system/xposed.prop" // Xposed属性文件
        };

        for (String path : suspiciousPaths) {
            if (new File(path).exists()) {
                return true;
            }
        }
        return false;
    }

    //检测模拟器环境
    public static boolean isEmulator() {
        String[] emulatorSigns = {
            "google_sdk", "Emulator", "Android SDK built for x86", "sdk_gphone",
            "generic", "unknown", "Genymotion", "Andy", "ttVM_Hdragon",
            "Droid4X", "nox", "BlueStacks", "MEmu", "LDPlayer"
        };

        String model = android.os.Build.MODEL.toLowerCase();
        String product = android.os.Build.PRODUCT.toLowerCase();
        String hardware = android.os.Build.HARDWARE.toLowerCase();
        String brand = android.os.Build.BRAND.toLowerCase();

        for (String sign : emulatorSigns) {
            if (model.contains(sign.toLowerCase())
                    || product.contains(sign.toLowerCase())
                    || hardware.contains(sign.toLowerCase())
                    || brand.contains(sign.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    //检测调试器
    public static boolean isDebuggerConnected() {
        return android.os.Debug.isDebuggerConnected();
    }

    //综合攻击检测 - 增强版
    public static boolean isUnderAttack() {
        // 网络层面检测
        if (isUsingProxy()) {
            return true;
        }
        if (isVPNActive()) {
            return true;
        }

        // 破解工具检测
        if (isFridaDetected()) {
            return true;
        }

        // 环境检测
        if (isEmulator()) {
            return true;
        }

        // 调试检测
        if (isDebuggerConnected()) {
            return true;
        }

        return false;
    }

    public static void downloadFile(Context context, final String fileUrl, final String filePath, final NetworkCallback callback) {
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                InputStream inputStream = null;
                OutputStream outputStream = null;
                HttpURLConnection urlConnection = null;
                try {

                    URL url = new URL(fileUrl);
                    urlConnection = (HttpURLConnection) url.openConnection();
                    urlConnection.setRequestMethod("GET");
                    urlConnection.setDoOutput(true);
                    urlConnection.connect();
                    inputStream = urlConnection.getInputStream();

                    File outputFile = new File(filePath);
                    outputStream = new FileOutputStream(outputFile);

                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    return "下载完成！路径：" + filePath;
                } catch (Exception e) {
                    return "下载失败！" + e.getMessage();
                } finally {
                    try {
                        if (inputStream != null) {
                            inputStream.close();
                        }
                        if (outputStream != null) {
                            outputStream.close();
                        }
                        if (urlConnection != null) {
                            urlConnection.disconnect();
                        }
                    } catch (Exception e) {
                        Log.e("FileDownloader", "Error closing streams", e);
                    }
                }
            }

            @Override
            protected void onPostExecute(String result) {
                if (callback != null) {
                    if (result.startsWith("error")) {
                        //包含error则回调失败函数
                        callback.onFailure(result);
                    } else {
                        //回调成功函数
                        callback.onSuccess(result);
                    }
                }

            }
        }.execute();
    }

    /**
     * 检查网络连接状态
     *
     * @param context 上下文
     * @return 网络是否可用
     */
    public static boolean isNetworkAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    /**
     * 获取网络类型
     *
     * @param context 上下文
     * @return 网络类型字符串
     */
    public static String getNetworkType(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            if (activeNetworkInfo != null && activeNetworkInfo.isConnected()) {
                switch (activeNetworkInfo.getType()) {
                    case ConnectivityManager.TYPE_WIFI:
                        return "WiFi";
                    case ConnectivityManager.TYPE_MOBILE:
                        return "移动数据";
                    default:
                        return "其他";
                }
            }
        }
        return "无网络";
    }

}
