package com.bytecat.algui.AlguiTools;
import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.provider.Settings;
import android.widget.Toast;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:39
 * @Describe App权限工具
 */
public class AlguiToolPermission {

    public static final String TAG = "AlguiToolPermission";


    private AlguiToolPermission() {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  
    //申请权限结果回调接口
    public interface PermissionCallback { void run(boolean consequence); }

    //申请权限
    public static void getPermission(Activity a, String permission) {
        if (a.checkSelfPermission(permission) != PackageManager.PERMISSION_GRANTED) 
            a.requestPermissions(new String[]{permission}, 1);
    }

    //申请权限并在允许后回调
    public static void getPermission(final Activity a, final String permission, final PermissionCallback call) {
        if (a.checkSelfPermission(permission) != PackageManager.PERMISSION_GRANTED) {
            a.requestPermissions(new String[]{permission}, 1);


            final Handler handler = new Handler();
            final Runnable checkPermissionRunnable = new Runnable() {
                int max=20;//超时时间(秒)
                int m;//计时
                @Override
                public void run() {
                    if (a.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED) {
                        if (call != null) {
                            call.run(true);
                        }
                        handler.removeCallbacks(this);
                        return;
                    } else {
                        //超时则未授予
                        if (m >= max) {
                            if (call != null) {
                                call.run(false);
                            }
                            handler.removeCallbacks(this);
                            return;
                        }
                        handler.postDelayed(this, 1000); //每秒检查一次
                        m++;
                    }
                }
            };
            handler.post(checkPermissionRunnable);
        } else {
            if (call != null) {
                call.run(true);
            }
        }

    }

    //申请悬浮窗权限
    public static void getWindow(Context mContext) {
        if (mContext == null) {
            return;
        }

        if (!(Build.VERSION.SDK_INT < Build.VERSION_CODES.M || Settings.canDrawOverlays(mContext))) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + mContext.getPackageName()));
            mContext.startActivity(intent);
            Toast.makeText(mContext, "请允许应用悬浮窗权限！", Toast.LENGTH_LONG).show(); 
        }
        return;
    }

    //申请悬浮窗权限 并监听允许了权限才回调
    public static void getWindow(final Context mContext, final PermissionCallback call) {
        getWindow(mContext);//申请悬浮窗权限
        //开始监听是否授予了悬浮窗权限

        final Handler handler = new Handler();
        final Runnable checkPermissionRunnable = new Runnable() {
            int max=20;//超时时间(秒)
            int m;//计时
            @Override
            public void run() {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M || Settings.canDrawOverlays(mContext)) {
                    if (call != null) {
                        call.run(true);
                    }
                    handler.removeCallbacks(this);
                    return;
                } else {
                    //超时则未授予
                    if (m >= max) {
                        if (call != null) {
                            call.run(false);
                        }
                        handler.removeCallbacks(this);
                        return;
                    }
                    handler.postDelayed(this, 1000); //每秒检查一次
                    m++;
                }
            }
        };
        handler.post(checkPermissionRunnable);
    }



    //检查当前应用程序AndroidManifest.xml安卓清单文件中 是否存在某个权限  分别传入上下文和权限代号
    //实例：
    /*if (AppUtilsTool.isAndroidManifestAuthority(context, "android.permission.SYSTEM_ALERT_WINDOW")) {
     Log.d("艾琳debug","清单文件中存在悬浮窗权限");
     }else{
     Log.d("艾琳debug","清单文件中不存在悬浮窗权限");
     }*/
    public static boolean isAndroidManifestPermissionExist(Context context, String Authority) {
        String permission = Authority;
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), PackageManager.GET_PERMISSIONS);
            String[] permissions = packageInfo.requestedPermissions;
            if (permissions != null) {
                for (String p : permissions) {
                    if (p.equals(permission)) {
                        return true;
                    }
                }
            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return false;
    }


}

