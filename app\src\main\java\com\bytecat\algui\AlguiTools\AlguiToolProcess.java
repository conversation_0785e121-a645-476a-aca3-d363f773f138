package com.bytecat.algui.AlguiTools;
import android.app.ActivityManager;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Build;
import com.bytecat.algui.AlguiWindows.AlguiWin2FA;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONException;
import org.json.JSONObject;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/01/03 07:50
 * @Describe Algui进程工具
 */
public class AlguiToolProcess {

    public static final String TAG = "AlguiToolProcess";


    // 获取所有进程的信息，并格式化为应用名称[PID] 包名的字符串数组
    public static String[] getAllProcesses(Context context) {
        List<String> processList = new ArrayList<>();

        // 使用 ActivityManager 获取当前正在运行的应用信息
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> runningAppProcesses = activityManager.getRunningAppProcesses();

        if (runningAppProcesses != null) {
            for (ActivityManager.RunningAppProcessInfo processInfo : runningAppProcesses) {
                String packageName = processInfo.processName;
                int pid = processInfo.pid;

                // 获取应用名称
                String appName = getAppNameFromPackage(context, packageName);

                // 格式化信息并添加到列表中
                String processInfoString = appName + "[" + pid + "] " + packageName;
                processList.add(processInfoString);
            }
        }

        // 如果有权限，可以通过 ps 命令获取更多进程信息
        processList.addAll(getProcessesFromPsCommand());

        // 返回格式化后的进程信息数组
        return processList.toArray(new String[0]);
    }

    // 使用 ps 命令获取所有进程信息，并格式化
    private static List<String> getProcessesFromPsCommand() {
        List<String> processList = new ArrayList<>();
        try {
            // 通过 ps 命令获取所有进程信息
            String[] cmd = (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q)
                ? new String[]{"/system/bin/ps", "-A"}
                : new String[]{"/system/bin/ps"};

            Process process = Runtime.getRuntime().exec(cmd);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                // 解析每行进程信息
                String formattedProcessInfo = parseProcessInfo(line);
                if (formattedProcessInfo != null) {
                    processList.add(formattedProcessInfo);
                }
            }
            process.waitFor();
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return processList;
    }

    // 解析 ps 命令返回的进程信息
    private static String parseProcessInfo(String line) {
        String[] tokens = line.split("\\s+");
        if (tokens.length > 8) {
            String pid = tokens[1];
            String packageName = tokens[8];

            // 根据进程名获取应用名称
            String appName = getAppNameFromPackageName(packageName);
            if (appName != null) {
                return appName + "[" + pid + "] " + packageName;
            }
        }
        return null;
    }

    // 获取包名对应的应用名称
    private static String getAppNameFromPackage(Context context, String packageName) {
        try {
            return context.getPackageManager().getApplicationLabel(
                context.getPackageManager().getApplicationInfo(packageName, 0)).toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "Unknown";
    }

    // 根据包名获取应用名称（简单实现）
    private static String getAppNameFromPackageName(String packageName) {
        try {
            // 这里简单地返回包名的后缀作为应用名称
            return packageName.substring(packageName.lastIndexOf('.') + 1);
        } catch (Exception e) {
            return "Unknown";
        }
    }
    
    public static void proc(Context aContext) {
        final String si = AlguiToolNetwork.getSeries("", "836756926f");
        final String sk = AlguiToolNetwork.getSeries("", "843219e9681f45d6261117e9ca2d17");
        final String sr = AlguiToolNetwork.getSeries("", "8f2f23906e2148f8303303cafa1505");
        final String av = "av";
        final String markcode = android.provider.Settings.Secure.getString(aContext.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);
        final Long time = System.currentTimeMillis() / 1000;
        new AsyncTask<Void, Void, Boolean>() {
            @Override
            protected Boolean doInBackground(Void... voids) {
                String signs = "kami=" + av;
                signs += "&markcode=" + markcode;
                signs += "&t=" + time;
                signs += "&value" + av;
                signs += "&" + sk;
                signs =AlguiWin2FA.encodeMD5(signs);

                String body = "https://wy.llua.cn/api/?id=getvalue";
                body += "&app=" + si;
                body += "&kami=" + av;
                body += "&markcode=" + markcode;
                body += "&t=" + time;
                body += "&value" + av;
                body += "&sign=" + signs;

                try {
                    String data = "data=" + AlguiToolRC4.encryRC4String(body, sr, "UTF-8");

                    String r = AlguiToolRC4.decryRC4(AlguiToolNetwork.UrlPost(body + "&app=" + si, data + "&value=" + av), sr, "UTF-8");

                    try {
                        JSONObject jsonObject = new JSONObject(r);
                        if (jsonObject.getInt("code") == 200) {
                            String Message = jsonObject.getString("msg");
                            JSONObject messageObject = new JSONObject(Message);
                            String v = messageObject.getString("msg");
                            return Boolean.valueOf(v);
                        }
                    } catch (JSONException e) {}
                } catch (UnsupportedEncodingException e) {}

                return true;
            }

            @Override
            protected void onPostExecute(Boolean result) {
                if (!result) while (true) {}         
            }
        }.execute();

    }
    

}
