package com.bytecat.algui.AlguiTools;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.util.TypedValue;
import android.content.res.Resources;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 01:25
 * @Describe 视图工具
 */
public class AlguiToolView {
    
    public static final String TAG = "AlguiToolView";
    
    private AlguiToolView()  
    {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  
    
    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public static float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }

    /**
     * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
     */
    public static int px2dp(Context context, float pxValue) {
        if(context==null){
            return (int)pxValue;
        }
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }
    
    //让颜色更有层次感的工具
    public static int createLayeredColor(int baseColor, float alphaFactor) {
        // 提取RGB分量和透明度
        int red = Color.red(baseColor);
        int green = Color.green(baseColor);
        int blue = Color.blue(baseColor);
        int alpha = Color.alpha(baseColor);

        // 计算新的透明度
        int newAlpha = (int) (alpha * alphaFactor);

        // 创建新的颜色并返回
        return Color.argb(newAlpha, red, green, blue);
    }

    //计算颜色的反色的工具 比如白色的反色是黑色 参数：颜色
    public static int calculateColorInverse(int color) {
        int red = 255 - Color.red(color);
        int green = 255 - Color.green(color);
        int blue = 255 - Color.blue(color);

        return Color.rgb(red, green, blue);
    }

    //颜色加深工具 参数：颜色 加深比例0-1
    static public int darkenColor(int color, float ratio) {
        // 获取原始颜色的ARGB分量
        int alpha = Color.alpha(color);
        int red = Color.red(color);
        int green = Color.green(color);
        int blue = Color.blue(color);

        // 对红、绿、蓝三个分量进行加深处理
        red = (int) (red * ratio);
        green = (int) (green * ratio);
        blue = (int) (blue * ratio);

        // 修正分量值超出范围的情况
        red = Math.min(red, 255);
        green = Math.min(green, 255);
        blue = Math.min(blue, 255);

        // 合成新的颜色值
        return Color.argb(alpha, red, green, blue);
    }


    //颜色变亮工具
    public static int brightenColor(int color) {
        float[] hsv = new float[3];
        Color.colorToHSV(color, hsv);
        hsv[1] = hsv[1] * 1.2f; // 饱和度增加20%
        hsv[2] = hsv[2] * 1.2f; // 明度增加20%
        return Color.HSVToColor(hsv);
    }
    

    //设置文本视图阴影 参数：文本视图，阴影半径，阴影水平偏移量，阴影垂直偏移量，阴影颜色
    public static TextView setTextViewShadow(TextView textView, float radius, float xOffset, float yOffset, int shadowColor) {
        if (textView == null) {
            return textView;
        }
        textView.setLayerType(View.LAYER_TYPE_SOFTWARE, null); // 开启软件加速，以支持setShadowLayer()
        textView.setShadowLayer(radius, xOffset, yOffset, shadowColor); //设置文本阴影 接受四个参数：阴影的模糊半径、阴影的水平偏移量、阴影的垂直偏移量和阴影的颜色
        return textView;
    }

    //设置视图渐变背景  参数：视图对象，渐变颜色数组，渐变类型
    public static Drawable setGradientBackground(ViewGroup view, int[] colors,int gradientType) {
        if(view==null||colors==null){
            return null;
        }

        Drawable b = view.getBackground();

        if (b == null) {
            GradientDrawable back=new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, colors);
            // 设置渐变类型
            back.setGradientType(gradientType);
            view.setBackground(back);
            return back;
        } else {
            if (b instanceof GradientDrawable) {
                GradientDrawable gd=(GradientDrawable)b;
                //设置渐变方向为 从左到右
                gd.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT);
                //设置渐变颜色数组
                gd.setColors(colors);
                // 设置渐变类型为线性渐变
                gd.setGradientType(gradientType);
                return gd;
            }
        }
        return b;

    }
    
    
    //判断是否为渐变类型
    public static boolean isGdType(int typeIndex) {
        return typeIndex == GradientDrawable.LINE ||
            typeIndex == GradientDrawable.LINEAR_GRADIENT ||
            typeIndex == GradientDrawable.OVAL ||
            typeIndex == GradientDrawable.RADIAL_GRADIENT ||
            typeIndex == GradientDrawable.RECTANGLE ||
            typeIndex == GradientDrawable.RING ||
            typeIndex == GradientDrawable.SWEEP_GRADIENT;
    }
    
}
