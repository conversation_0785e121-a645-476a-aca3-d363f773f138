package com.bytecat.algui.AlguiViews;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/11/08 20:28
 * @Describe Algui流式布局 添加视图时默认横向从左到右依次添加，调用endl代表当前行已结束需换行添加
 */
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.drawable.GradientDrawable;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import java.util.ArrayList;
import java.util.Arrays;

public class AlguiFlowLayout extends LinearLayout {

    public static final String TAG = "AlguiFlowLayout";
    Context context;

    LinearLayout.LayoutParams params;//布局参数
    GradientDrawable mainBackground;//背景
    ArrayList<AlguiLinearLayout> lineList = new ArrayList<>();//所有行

    //样式
    float sWidth, sHeight;         // 宽高
    float sWeight;                   // 权重
    float[] sPadding, sMargins;      // 内边距和外边距
    int[] sBackColors;             // 背景颜色
    float sStrokeSize = -1;        // 描边大小
    int sStrokeColor = -1;         // 描边颜色
    float sFilletRadiu = -1;       // 圆角半径
    int sOrientation;//方向
    float[] allLineMargins=new float[]{0,0,0,0};//所有行的外边距

    public float getByteStyleWidth() { return sWidth; }             // 获取宽度
    public float getByteStyleHeight() { return sHeight; }           // 获取高度
    public float getByteStyleWeight() { return sWeight; }             // 获取权重
    public float[] getByteStylePadding() { return sPadding; }         // 获取内边距
    public float[] getByteStyleMargins() { return sMargins; }         // 获取外边距
    public int[] getByteStyleBackColors() { return sBackColors; }   // 获取背景颜色
    public float getByteStyleBorderSize() { return sStrokeSize; }   // 获取描边大小
    public int getByteStyleBorderColor() { return sStrokeColor; }   // 获取描边颜色
    public float getByteStyleRadius() { return sFilletRadiu; }      // 获取圆角半径
    public float getByteStyleOrientation() { return isVertical ?VERTICAL: HORIZONTAL; }      // 获取方向
    public float[] getByteLineMargins() { return allLineMargins; }      // 获取所有行外边距

    //拓展方法
    public LinearLayout.LayoutParams getByteParams() { return params; }//获取布局参数
    public GradientDrawable getByteBack() {   return mainBackground; }//获取背景
    public ArrayList<AlguiLinearLayout> getByteLineList() {return lineList;}//获取所有行
    //获取当前行 (最后一行)
    public AlguiLinearLayout getByteLine() {
        //如果还没有行则创建
        if (lineList.size() <= 0) {
            endl();
        }
        return getByteLine(lineList.size() - 1);
    }
    //获取指定行
    public AlguiLinearLayout getByteLine(int index) {
        AlguiLinearLayout l=null;
        if (index >= 0 && index < lineList.size()) {
            l = lineList.get(index);
        } 
        return l;
    }

    float
    mRadius,//圆角半径
    tBorderSize; //描边大小
    int tBorderColor;//描边颜色

    int lineMaxView=0;//所有行最大视图数量自动换行 (0则不自动换行需手动换行)

    boolean isVertical;//是否垂直
    public static final int VERTICAL = 0x1;//垂直
    public static final int HORIZONTAL = 0x0;//横向

    public AlguiFlowLayout(Context context) {
        super(context);
        this.context = context;
        init();
        setCatOrientation(HORIZONTAL);//默认横向
    }

    //初始化父容器
    private void init() {
        params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT, 1);
        setLayoutParams(params);
        setClipChildren(true);//子视图超过边界时自动裁剪
        setClipToOutline(true);//根据父视图轮廓裁剪
        mainBackground = new GradientDrawable();
        setBackground(mainBackground);
    }

    //设置行的方向
    //(垂直=父容器横向添加行，行垂直添加视图 & 横向=父容器垂直添加行，行横向添加视图)
    public AlguiFlowLayout setCatOrientation(int orientation) {
        sOrientation = orientation;
        switch (orientation) {
            default:
            case VERTICAL:
                isVertical = true;
                break;
            case HORIZONTAL:
                isVertical = false;
                break;
        }
        super.setOrientation(isVertical ?HORIZONTAL: VERTICAL);//设置父容器方向
        return this;
    }

    //设置行的最大视图数量 超出则自动换行
    public AlguiFlowLayout setCatLineMaxView(int num) {
        lineMaxView = num;
        return this;
    }
    //设置所有行的外边距
    public AlguiFlowLayout setCatLineMargins(float left, float top, float right, float bottom) {
        allLineMargins=new float[]{left,top,right,bottom};
        for (AlguiLinearLayout line:lineList) {
            if (line != null) {
                line.setCatMargins(allLineMargins[0],allLineMargins[1],allLineMargins[2],allLineMargins[3]);
            }
        }
        return this;
    }




    //设置父布局
    public AlguiFlowLayout setCatParentLayout(ViewGroup vg) {
        if (vg != null)
            vg.addView(this);
        return this;
    }

    //删除所有视图
    public AlguiFlowLayout remAllView() {
        //清除所有行
        for (AlguiLinearLayout l : lineList) {
            if(l!=null)
            remLine(l);
        }
        super.removeAllViews();//清除容器内容
        return this;
    }
    //删除子视图
    public AlguiFlowLayout remView(View... view) {
        int i=0;
        for (AlguiLinearLayout l:lineList) {
            if (l != null) {
                for (View v:view) {
                    if (v != null) {
                        if (l.indexOfChild(v) != -1) {
                            l.removeView(v);
                            i++;
                        } else if (super.indexOfChild(v) != -1) {
                            super.removeView(v);
                            i++;
                        }
                        if (i >= view.length) {
                            return this;
                        }
                    }
                }
            }

        }
        return this;
    }
    //删除指定行的视图
    public AlguiFlowLayout remViewToLine(int index, View... views) {
        AlguiLinearLayout l = getByteLine(index);
        if (l != null) {
            l.remView(views);
        }
        return this;
    }
    //删除指定行 (按索引)
    public AlguiFlowLayout remLine(int... indexs) {
        for (int index:indexs) {
            AlguiLinearLayout l = getByteLine(index);
            if (l != null) {
                remLine(l);//删除
            }
        }
        return this;
    }
    //删除指定行 (按对象)
    public AlguiFlowLayout remLine(AlguiLinearLayout... objs) {
        for (AlguiLinearLayout obj:objs) {
            if (obj != null && lineList.contains(obj)) {
                obj.remAllView();//删除该行所有子视图
                super.removeView(obj);//从父容器删除
                lineList.remove(obj);//从行列表删除
                obj = null;//回收
            }
        }
        return this;
    }

    //行结束 (换行)
    public AlguiLinearLayout endl() {
        //创建新的行
        AlguiLinearLayout line = new AlguiLinearLayout(context);
        line.setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        line.setCatWeight(1);
        line.setCatMargins(allLineMargins[0], allLineMargins[1], allLineMargins[2], allLineMargins[3]);
        line.setOrientation(isVertical ?VERTICAL: HORIZONTAL);//设置方向
        line.setClipToOutline(true);//根据父视图轮廓裁剪
        line.setGravity(isVertical ?Gravity.CENTER_HORIZONTAL: Gravity.CENTER_VERTICAL);
        super.addView(line, -1, line.getByteParams());//添加当前行到主容器
        lineList.add(line);//保存当前行到列表

        return line;
    }
    //自动换行
    private void aEndl() {
        //只有存在一行最大视图数量才自动换行
        if (lineMaxView > 0) {
            int num = getByteLine().getChildCount();//获取当前行子视图数量
            //当前数量为一行最大视图数量则自动换行
            if (num >= lineMaxView) {
                endl();
            }
        }

    }

    //添加视图到指定行
    public AlguiFlowLayout addViewToLine(int index, View... views) {
        AlguiLinearLayout l = getByteLine(index);
        if (l != null) {
            l.addView(views);
        }
        return this;
    }
    //重写添加视图的函数 确保新视图添加到末尾行当中而不是根容器
    //在最后一行添加一些视图 中途null代表换行
    public AlguiFlowLayout addView(View... view) {
        for (View aView : view) {
            if (aView != null) {
                aEndl();//自动换行
                AlguiLinearLayout l = getByteLine();//获取最后一行
                if (l.indexOfChild(aView) == -1)
                    l.addView(aView);//在最后一行添加视图
            } else {
                //null代表手动换行
                endl();
            }
        }
        return this;
    }
    @Override
    public void addView(View child) {
        if (child != null) {    
            aEndl();//自动换行
            AlguiLinearLayout l = getByteLine();//获取最后一行
            if (l.indexOfChild(child) == -1)
                l.addView(child);//在最后一行添加视图
        }

    }
    @Override
    public void addView(View child, int index) {
        if (child != null) {
            aEndl();//自动换行
            AlguiLinearLayout l = getByteLine();//获取最后一行
            if (l.indexOfChild(child) == -1)
                l.addView(child);//在最后一行添加视图
        }
    }
    @Override
    public void addView(View child, int width, int height) {

        if (child != null) {
            aEndl();//自动换行
            AlguiLinearLayout l = getByteLine();//获取最后一行
            if (l.indexOfChild(child) == -1)
                l.addView(child);//在最后一行添加视图
        }
    }
    //重写后内部往父类添加视图时必须使用此重载 index直接填-1即可
    //原因：其它重载在父类内部会间接最终调用到这个重载
    // 由于重写了所以父类会调用子类重写的重载
    // 那么往父类添加insideLayout时就会无限递归 所以内部必须使用此重装添加
    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        if (child != null) {
            aEndl();//自动换行
            AlguiLinearLayout l = getByteLine();//获取最后一行
            if (l.indexOfChild(child) == -1)
                l.addView(child);//在最后一行添加视图
        }
    }
    @Override
    public void addView(View child, ViewGroup.LayoutParams params) {
        if (child != null) {
            aEndl();//自动换行
            AlguiLinearLayout l = getByteLine();//获取最后一行
            if (l.indexOfChild(child) == -1)
                l.addView(child);//在最后一行添加视图
        }
    }










    //设置大小
    public AlguiFlowLayout setCatSize(float w, float h) {
        sWidth = w;
        sHeight = h;
        if ((int)w != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int) w != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)w != ViewGroup.LayoutParams.FILL_PARENT) {
            params.width = (int)dp2px(w);
        } else {
            params.width = (int)w;
        }

        if ((int)h != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int)h != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)h != ViewGroup.LayoutParams.FILL_PARENT) {
            params.height = (int)dp2px(h);
        } else {
            params.height = (int)h;
        }
        requestLayout();//重新计算布局
        return this;
    }

    //设置权重
    public AlguiFlowLayout setCatWeight(float weight) {
        sWeight = weight;
        params.weight = weight;
        requestLayout();//重新计算布局
        return this;
    }

    //设置内边距
    public AlguiFlowLayout setCatPadding(float left, float top, float right, float bottom) {
        sPadding = new float[]{left,top,right,bottom};
        setPadding(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }

    //设置外边距
    public AlguiFlowLayout setCatMargins(float left, float top, float right, float bottom) {
        sMargins = new float[]{left,top,right,bottom};
        params.setMargins(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }
    //设置背景颜色
    public AlguiFlowLayout setCatBackColor(int... backColor) {
        sBackColors = backColor;
        if (backColor.length == 1) {
            //单个颜色
            mainBackground.setColor(backColor[0]);
        } else if (backColor.length > 1) {
            //多个颜色，使用渐变
            mainBackground.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT); //渐变方向
            //最后一个元素如果是渐变类型则应用否则默认线性渐变
            if (isGdType(backColor[backColor.length - 1])) {
                mainBackground.setGradientType(backColor[backColor.length - 1]);
                int[] newArray = Arrays.copyOf(backColor, backColor.length - 1);//删除最后一个元素
                mainBackground.setColors(newArray);//设置颜色
            } else {
                mainBackground.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                mainBackground.setColors(backColor);//设置颜色
            }
        }

        return this;
    }


    //设置圆角半径
    public AlguiFlowLayout setCatRadiu(float radiu) {
        sFilletRadiu = radiu;
        mRadius = dp2px(radiu);
        mainBackground.setCornerRadius(mRadius);
        invalidate();
        return this;
    }

    //设置描边
    public AlguiFlowLayout setCatBorder(float borderSize, int borderColor) {
        sStrokeSize = borderSize;
        sStrokeColor = borderColor;
        //先设置内边距防止描边过大时覆盖子视图
        float bs = dp2px(borderSize);//新的描边
        int w=(int)((bs - tBorderSize) / 2);//计算内边距差值
        this.setPadding(getPaddingLeft() + w, getPaddingTop() + w, getPaddingRight() + w, getPaddingBottom() + w);
        //应用
        tBorderSize = bs;
        tBorderColor = borderColor;
        invalidate();
        return this;
    }


    //绘制子视图
    //这里使其描边沿圆角进行描边
    @Override
    protected void dispatchDraw(Canvas canvas) {
        int width=getWidth();
        int height=getHeight();
        //先规划圆角轮廓路径
        Path path = new Path();//存储圆角路径
        RectF rectF = new RectF();
        rectF.set(0, 0, width, height);
        path.addRoundRect(rectF, mRadius, mRadius, Path.Direction.CW);

        //保存当前画布状态
        int saveCount = canvas.save();
        canvas.clipPath(path);//裁剪子布局超出圆角的部分
        super.dispatchDraw(canvas); //然后绘制子视图

        //开始绘制描边
        Paint borderPaint = new Paint();
        borderPaint.setColor(tBorderColor); //设置描边颜色
        borderPaint.setStyle(Paint.Style.STROKE);
        borderPaint.setStrokeWidth(tBorderSize); //设置描边宽度
        canvas.drawPath(path, borderPaint);//沿圆角路径绘制描边
        //恢复画布状态
        canvas.restoreToCount(saveCount);
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }

    //判断是否为渐变类型
    public boolean isGdType(int typeIndex) {
        return typeIndex == GradientDrawable.LINE ||
            typeIndex == GradientDrawable.LINEAR_GRADIENT ||
            typeIndex == GradientDrawable.OVAL ||
            typeIndex == GradientDrawable.RADIAL_GRADIENT ||
            typeIndex == GradientDrawable.RECTANGLE ||
            typeIndex == GradientDrawable.RING ||
            typeIndex == GradientDrawable.SWEEP_GRADIENT;
    }
}
