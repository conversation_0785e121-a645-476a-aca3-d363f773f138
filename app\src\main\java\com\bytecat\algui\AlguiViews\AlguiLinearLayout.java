package com.bytecat.algui.AlguiViews;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.drawable.GradientDrawable;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import java.util.Arrays;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 01:25
 * @Describe Algui线性布局
 */
public class AlguiLinearLayout extends LinearLayout {

    public static final String TAG = "AlguiLinearLayout";

    Context context;
    LinearLayout.LayoutParams params;//布局参数
    GradientDrawable mainBackground;//背景

    float
    mRadius,//圆角半径
    tBorderSize; //描边大小
    int tBorderColor;//描边颜色

    //样式
    float sWidth, sHeight;         // 宽高
    float sWeight;                   // 权重
    float[] sPadding, sMargins;      // 内边距和外边距
    int[] sBackColors;             // 背景颜色
    float sStrokeSize = -1;        // 描边大小
    int sStrokeColor = -1;         // 描边颜色
    float sFilletRadiu = -1;

    public ViewGroup getContainer() {
        return null;
    }       // 圆角半径

    public float getByteStyleWidth() { return sWidth; }             // 获取宽度
    public float getByteStyleHeight() { return sHeight; }           // 获取高度
    public float getByteStyleWeight() { return sWeight; }             // 获取权重
    public float[] getByteStylePadding() { return sPadding; }         // 获取内边距
    public float[] getByteStyleMargins() { return sMargins; }         // 获取外边距
    public int[] getByteStyleBackColors() { return sBackColors; }   // 获取背景颜色
    public float getByteStyleBorderSize() { return sStrokeSize; }   // 获取描边大小
    public int getByteStyleBorderColor() { return sStrokeColor; }   // 获取描边颜色
    public float getByteStyleRadius() { return sFilletRadiu; }      // 获取圆角半径
    
    /**
     * 获取布局参数
     * 
     * @return LinearLayout.LayoutParams 返回当前的布局参数
     */
    public LinearLayout.LayoutParams getByteParams() {
        return params;
    }



    /**
     * 获取背景
     * 
     * @return GradientDrawable 返回当前的背景Drawable
     */
    public GradientDrawable getByteBack() {
        return mainBackground;
    }



    public AlguiLinearLayout(Context context) {
        super(context);
        this.context = context;
        init();
    }

    private void init() {
        params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        setLayoutParams(params);
        setClipChildren(true);//子视图超过边界时自动裁剪
        setClipToOutline(true);//根据父视图轮廓裁剪
        mainBackground = new GradientDrawable();
        setBackground(mainBackground);

    }

public AlguiLinearLayout setCatOrientation(int orientation) {
    this.setOrientation(orientation); // 调用原生的 setOrientation 方法
    return this;
}



public AlguiLinearLayout setLayoutParams(int LayoutParams) {
    this.setLayoutParams(LayoutParams); 
    return this;
}

    //设置大小
    public AlguiLinearLayout setCatSize(float w, float h) {
        sWidth=w;
        sHeight=h;
        if ((int)w != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int) w != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)w != ViewGroup.LayoutParams.FILL_PARENT) {
            params.width = (int)dp2px(w);
        } else {
            params.width = (int)w;
        }

        if ((int)h != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int)h != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)h != ViewGroup.LayoutParams.FILL_PARENT) {
            params.height = (int)dp2px(h);
        } else {
            params.height = (int)h;
        }
        requestLayout();//重新计算布局
        return this;
    }

    //设置权重
    public AlguiLinearLayout setCatWeight(float weight) {
        sWeight=weight;
        params.weight = weight;
        requestLayout();//重新计算布局
        return this;
    }

    //设置内边距
    public AlguiLinearLayout setCatPadding(float left, float top, float right, float bottom) {
       sPadding = new float[]{left,top,right,bottom};
        setPadding(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }

    //设置外边距
    public AlguiLinearLayout setCatMargins(float left, float top, float right, float bottom) {
        sMargins = new float[]{left,top,right,bottom};
        params.setMargins(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }
    //设置背景颜色
    public AlguiLinearLayout setCatBackColor(int... backColor) {
        sBackColors=backColor;
        if (backColor.length == 1) {
            //单个颜色
            mainBackground.setColor(backColor[0]);
        } else if (backColor.length > 1) {
            //多个颜色，使用渐变
            mainBackground.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT); //渐变方向
            //最后一个元素如果是渐变类型则应用否则默认线性渐变
            if (isGdType(backColor[backColor.length - 1])) {
                mainBackground.setGradientType(backColor[backColor.length - 1]);
                int[] newArray = Arrays.copyOf(backColor, backColor.length - 1);//删除最后一个元素
                mainBackground.setColors(newArray);//设置颜色
            } else {
                mainBackground.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                mainBackground.setColors(backColor);//设置颜色
            }
        }

        return this;
    }


    //设置圆角半径
    public AlguiLinearLayout setCatRadiu(float radiu) {
        sFilletRadiu=radiu;
        mRadius = dp2px(radiu);
        mainBackground.setCornerRadius(mRadius);
        invalidate();
        return this;
    }

    //设置描边
    public AlguiLinearLayout setCatBorder(float borderSize, int borderColor) {
        sStrokeSize=borderSize;
        sStrokeColor=borderColor;
        //先设置内边距防止描边过大时覆盖子视图
        float bs = dp2px(borderSize);//新的描边
        int w=(int)((bs - tBorderSize) / 2);//计算内边距差值
        this.setPadding(getPaddingLeft() + w, getPaddingTop() + w, getPaddingRight() + w, getPaddingBottom() + w);
        //应用
        tBorderSize = bs;
        tBorderColor = borderColor;
        invalidate();
        return this;
    }




    //设置父布局
    public AlguiLinearLayout setCatParentLayout(ViewGroup vg) {
        if (vg != null)
            vg.addView(this);
        return this;
    }

    //添加子视图 (仿Xml) 
    public AlguiLinearLayout addView(View... view) {
        for (View aView : view) {
            if (aView != null) {
                if (super.indexOfChild(aView) == -1)
                    addView(aView);
            }
        }
        return this;
    }

    //删除子视图
    public AlguiLinearLayout remView(View... view) {
        for (View aView : view) {
            if (aView != null) {
                if (super.indexOfChild(aView) != -1)
                    removeView(aView);
            }
        }
        return this;
    }

    //删除所有子视图
    public AlguiLinearLayout remAllView() {
        removeAllViews();
        return this;
    }

    //绘制子视图
    //这里使其描边能够沿圆角进行描边
    @Override
    protected void dispatchDraw(Canvas canvas) {
        int width=getWidth();
        int height=getHeight();
        //先规划圆角轮廓路径
        Path path = new Path();//存储圆角路径
        RectF rectF = new RectF();
        rectF.set(0, 0, width, height);
        path.addRoundRect(rectF, mRadius, mRadius, Path.Direction.CW);

        //保存当前画布状态
        int saveCount = canvas.save();
        canvas.clipPath(path);//裁剪子布局超出圆角的部分
        super.dispatchDraw(canvas); //然后绘制子视图

        //开始绘制描边
        Paint borderPaint = new Paint();
        borderPaint.setColor(tBorderColor); //设置描边颜色
        borderPaint.setStyle(Paint.Style.STROKE);
        borderPaint.setStrokeWidth(tBorderSize); //设置描边宽度
        canvas.drawPath(path, borderPaint);//沿圆角路径绘制描边
        //恢复画布状态
        canvas.restoreToCount(saveCount);
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }

    //判断是否为渐变类型
    public boolean isGdType(int typeIndex) {
        return typeIndex == GradientDrawable.LINE ||
            typeIndex == GradientDrawable.LINEAR_GRADIENT ||
            typeIndex == GradientDrawable.OVAL ||
            typeIndex == GradientDrawable.RADIAL_GRADIENT ||
            typeIndex == GradientDrawable.RECTANGLE ||
            typeIndex == GradientDrawable.RING ||
            typeIndex == GradientDrawable.SWEEP_GRADIENT;
    }
}
