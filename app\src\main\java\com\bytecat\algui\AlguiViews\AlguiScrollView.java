package com.bytecat.algui.AlguiViews;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2025/03/24 14:39
 * @Describe 滑动视图
 */
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.ScrollView;

public class AlguiScrollView extends ScrollView {

    public static final String TAG = "AlguiScrollView";


    public AlguiScrollView(Context context) {
        super(context);
    }

    public AlguiScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public AlguiScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        maxY = getChildAt(0).getMeasuredHeight() - getMeasuredHeight();
    }

    //在Y轴上可以滑动的最大距离 = 总长度 - 当前展示区域长度
    private int maxY;
    private boolean isDisableIntercept;//是否禁用父滑动拦截
    public AlguiScrollView setCatDisableIntercent(boolean b){
        isDisableIntercept=b;
        return this;
    }
    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if(isDisableIntercept){
            getParent().requestDisallowInterceptTouchEvent(true);//禁止父容器拦截滑动
            return super.dispatchTouchEvent(ev);
        }
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                getParent().getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_MOVE:
                if (getScrollY() > 0 && getScrollY() < maxY)
                    getParent().requestDisallowInterceptTouchEvent(true);
                else
                    getParent().requestDisallowInterceptTouchEvent(false);
                /*if (getScrollY()==0)
                 Log.i("艾琳测试","滑到头了");
                 if (getChildAt(0).getMeasuredHeight() == getScrollY() + getHeight())
                 Log.i("艾琳测试","滑到底了");*/
                break;
            case MotionEvent.ACTION_UP:
                getParent().getParent().requestDisallowInterceptTouchEvent(false);
                break;
        }
        
        return super.dispatchTouchEvent(ev);
    }


}
