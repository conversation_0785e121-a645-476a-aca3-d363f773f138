package com.bytecat.algui.AlguiViews;
import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.text.InputType;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import com.bytecat.algui.AlguiActivity;
import com.bytecat.algui.AlguiHacker.AlguiRootClient;
import com.bytecat.algui.AlguiHacker.AlguiRootService;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.bytecat.algui.AlguiViews.AlguiViewCheckBox;
import com.bytecat.algui.AlguiViews.AlguiViewDragBar;
import com.bytecat.algui.AlguiViews.AlguiViewFoldMenu;
import com.bytecat.algui.AlguiViews.AlguiViewImage;
import com.bytecat.algui.AlguiViews.AlguiViewInputBox;
import com.bytecat.algui.AlguiViews.AlguiViewItem;
import com.bytecat.algui.AlguiViews.AlguiViewLine;
import com.bytecat.algui.AlguiViews.AlguiViewSoundWave;
import com.bytecat.algui.AlguiViews.AlguiViewText;
import com.bytecat.algui.AlguiViews.AlguiViewTriangle;
import com.bytecat.algui.AlguiViews.AlguiViewWeb;
import com.bytecat.algui.AlguiWindows.AlguiWinConsole;
import com.bytecat.algui.AlguiWindows.AlguiWinDialog;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.bytecat.algui.AlguiWindows.AlguiWindow;
import com.topjohnwu.superuser.ipc.RootService;
import java.util.ArrayList;
import android.transition.Visibility;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/22 21:48
 * @Describe Algui快速构建器
 声明：以下只提供简单封装给不会使用的人快速使用
 因此还有很多特性没有封装自己利用以下方法进行挖掘，[查阅AlguiSDK.java文件]

 图片
 均支持：网络图片链接，base64编码，本地图片文件路径，图片文件名(识别为assets文件夹下的)
 格式均支持：GIF动图(.gif)，普通图片(.png .jpg)

 高度化定制教程：
 调用封装方法后调用.setCat...会出现和此组件相关的设置样式属性等等的提示，看得懂英文就很简单
 比如：
 Text(layout,"文本").setCatTextColor(0xFFFFFFFF); 
 就是创建文本后设置文本颜色为白色

 调用封装方法后.getByte...是获取和此组件绑定的某些子对象，可以自定义这些子对象的属性
 比如：
 WinMenu("菜单窗口").getByteMenuBottomLayout().addView(Text(null,"关闭"));
 就是创建菜单窗口后获取菜单窗口底部布局并在底部布局添加一个文本
 */

public class AlguiV {

    public static final String TAG = "AlguiV";
    Context aContext;

    //单例
    private static AlguiV obj;
    private AlguiV(Context context) {aContext = context;}
    public static AlguiV Get(Context context) {
        if (obj == null)
            obj = new AlguiV(context);
        return obj;
    }



    //&&&&&&&&&&&&&&&&&&&&&&对于窗口&&&&&&&&&&&&&&&&&&&&&&
    //创建一个普通菜单窗口
    public AlguiWinMenu WinMenu(CharSequence title, Object... args) {
        AlguiWinMenu obj=new AlguiWinMenu(aContext)
            .setCatMenuTitle(title, args)
            .showBall()
            ;
        return obj;
    }
    //创建一个日志控制台窗口 
    //使用AlguiLog.i("标签","信息") 在控制台输出信息 
    //哪里都可以向控制台输出，无需担心非UI线程
    public AlguiWinConsole WinLog() {
        return  AlguiLog.getLogConsole(aContext).show();
    }

    //创建一个文本对话框窗口 
    public AlguiWinDialog WinDialogText(int style[], CharSequence title, CharSequence text, Object... args) {
        final AlguiWinDialog dialog = new AlguiWinDialog(aContext)
            .setCatIsCanEnd(true)
            .setCatStyle(style)
            .setCatIcon(AlguiAssets.Icon.inform_info)
            .setCatTitle(title, args)
            .setCatNoButtonText(null)
            .setCatNoButtonClick(null)
            .setCatYesButtonText("我知道了")
            .show();
        dialog.setCatYesButtonClick(new AlguiCallback.Click() {
                public void click(boolean b) {
                    dialog.end();
                }
            });

        new AlguiViewText(aContext)
            .setCatText(text, args)
            .setCatTextColor(style[1])
            .setCatTextSize(9)
            .setCatParentLayout(dialog);

        return dialog;
    }
    //创建一个文本对话框窗口 (黑色)
    public AlguiWinDialog WinDialogText_Black(CharSequence title, CharSequence text, Object... args) {
        return WinDialogText(AlguiWinDialog.Style.BLACK, title, text, args);
    }
    //创建一个文本对话框窗口 (白色)
    public AlguiWinDialog WinDialogText_White(CharSequence title, CharSequence text, Object... args) {
        return WinDialogText(AlguiWinDialog.Style.WHITE, title, text, args);
    }
    //创建一个文本对话框窗口 (蓝色)
    public AlguiWinDialog WinDialogText_Blue(CharSequence title, CharSequence text, Object... args) {
        return WinDialogText(AlguiWinDialog.Style.BLUE, title, text, args);
    }

    //发送一个通知到通知窗口(黑色)
    public AlguiWinInform WinSendInfo_Black(String Image_Url_Base64_FilePath, CharSequence titleText, CharSequence infoText, int showTime) {
        return AlguiWinInform.Get(aContext) .showInfo_Black(Image_Url_Base64_FilePath, titleText, infoText, showTime);
    }
    //发送一个通知到通知窗口(白色)
    public AlguiWinInform WinSendInfo_White(String Image_Url_Base64_FilePath, CharSequence titleText, CharSequence infoText, int showTime) {
        return AlguiWinInform.Get(aContext) .showInfo_White(Image_Url_Base64_FilePath, titleText, infoText, showTime);
    }
    //发送一个通知到通知窗口(蓝色)
    public AlguiWinInform WinSendInfo_Blue(String Image_Url_Base64_FilePath, CharSequence titleText, CharSequence infoText, int showTime) {
        return AlguiWinInform.Get(aContext) .showInfo_Blue(Image_Url_Base64_FilePath, titleText, infoText, showTime);
    }

    //静态绘制视图到屏幕指定坐标 参数传视图，坐标原点，xy坐标相对原点偏移，悬浮视图是否可点击
    //如果需要高性能的动态帧绘制请使用AlguiWinDraw
    public AlguiWindow WinDraw(View v, int gravity, float x, float y, boolean isClick) {
        AlguiWindow w  = new AlguiWindow(aContext)
            .setCatPosGravity(gravity)
            .setCatPos(x, y)
            .setCatView(v)
            .show()
            ;
        if (!isClick)
            w.addFlag(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        return w;
    }

    //&&&&&&&&&&&&&&&&&&&&&&对于布局&&&&&&&&&&&&&&&&&&&&&&
   


    //&&&&&&&&&&&&&&&&&&&&&&对于折叠菜单&&&&&&&&&&&&&&&&&&&&&&
    //创建一个普通折叠菜单
    public AlguiViewFoldMenu FoldMenu(ViewGroup fatherLayout, CharSequence title) {
        AlguiViewFoldMenu obj=new AlguiViewFoldMenu(aContext)
            .setCatText(title)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个子折叠菜单
    public AlguiViewFoldMenu FoldMenuSon(ViewGroup fatherLayout, CharSequence title) {
        AlguiViewFoldMenu obj=new AlguiViewFoldMenu(aContext)
            .setCatTextSize(6.5f)
            .setCatTitleBackColor(0)
            .setCatText(title)
            .setCatParentLayout(fatherLayout)
            ;
        obj.getByteTitleLayout().setCatPadding(6, 0, 6, 0);
        return obj;
    }
    //创建一个单选折叠菜单 传标题，切换选项监听器，选项文本列表(添加顺序决定ID，0开始)
    public AlguiViewItem FoldMenuSwitch(ViewGroup fatherLayout, CharSequence title, final AlguiCallback.Item call, final CharSequence... items) {
        AlguiLinearLayout tL=new AlguiLinearLayout(aContext)
            .setCatMargins(5, 0, 0, 0)
            .setCatPadding(5, 5, 5, 5)
            .setCatBackColor(0xFF274A72)
            ;
        AlguiViewTriangle t = new AlguiViewTriangle(aContext)
            .setCatSize(5)
            .setCatColor(0xFFFFFFFF)
            .setCatType(AlguiViewTriangle.Type.EQUILATERAL_TOP)
            .setCatParentLayout(tL)
            ;

        final AlguiViewItem obj=new AlguiViewItem(aContext)
            //.setCatSize(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT)
            //.setCatWeight(0)
            .setCatPadding(5, 0, 0, 0)
            .setCatBackColor(0xFF20324D)
            .setCatText(title)
            .setCatParentLayout(fatherLayout)
            .addItem(0, tL)
            ;

        obj.setCatCallback(new AlguiCallback.Item(){
                boolean isInit=false;

                AlguiLinearLayout layout;
                PopupWindow popupWindow;
                ArrayList<AlguiViewItem> itemVs = new ArrayList<>();
                int sel=0;
                public void item(int b) {
                    if (!isInit) {

                        layout = new AlguiLinearLayout(aContext);
                        layout.setOrientation(LinearLayout.VERTICAL);
                        layout.setGravity(Gravity.CENTER_HORIZONTAL);
                        layout.setCatBackColor(0xce000000);

                        //滚动列表
                        int i=0;
                        for (final CharSequence text:items) {
                            if (text != null) {
                                final AlguiViewItem v =new AlguiViewItem(aContext, text);
                                v.setCatBackColor(0);
                                v.setCatText(text);
                                v.setCatId(i);
                                v.setCatCallback(new AlguiCallback.Item(){
                                        public void item(int u) {
                                            obj.setCatText(text);
                                            popupWindow.dismiss();
                                            if (call != null)
                                                call.item(v.getByteId());
                                            sel = v.getByteId();
                                        }
                                    }
                                );
                                layout.addView(v);
                                itemVs.add(v);
                                i++;
                            }
                        }
                        popupWindow = new PopupWindow(layout, obj.getWidth(), LinearLayout.LayoutParams.WRAP_CONTENT);

                        popupWindow.setOutsideTouchable(true);
                        isInit = true;
                    }
                    popupWindow.setWidth(obj.getWidth());
                    for (AlguiViewItem v: itemVs) {
                        if (v != null) {
                            if (v.getByteId() == sel) {
                                v.select();
                            }
                        }

                    }
                    popupWindow.showAsDropDown(obj, 0, 0);
                }
            }
        );

        return obj;
    }


    //&&&&&&&&&&&&&&&&&&&&&&对于文本&&&&&&&&&&&&&&&&&&&&&&
    //创建一个普通文本
    public AlguiViewText Text(ViewGroup fatherLayout, CharSequence text, Object... args) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatText(text, args)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个Html文本
    public AlguiViewText TextHtml(ViewGroup fatherLayout, String text, Object... args) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatTextHtml(text, args)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个自动识别文本中链接的文本
    public AlguiViewText Textlink(ViewGroup fatherLayout, CharSequence text, Object... args) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatText(text, args)
            .setCatTextIsLink(true)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个文本标题
    public AlguiViewText TextTitle(ViewGroup fatherLayout, CharSequence text, Object... args) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatTextSize(9)
            .setCatTextTFAssets("lz.ttf", Typeface.BOLD)
            .setCatText(text, args)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个子文本
    public AlguiViewText TextSon(ViewGroup fatherLayout, CharSequence text, Object... args) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatText(" • " + text, args)
            .setCatTextColor(0xFFBDBDBD)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个文本信息框
    public AlguiViewInputBox TextInfo(ViewGroup fatherLayout, CharSequence text) {
        AlguiViewInputBox obj=new AlguiViewInputBox(aContext)
            .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            .setCatWeight(1)
            .setCatButtonText(null)
            .setCatInputText(text)
            .setCatInputTextColor(0xFFFFFFFF)
            //.setCatPadding(3, 3, 3, 3)
            .setCatBackColor(0xFF20324D)
            .setCatBorder(0.4f, 0xff294A7A)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个文本标签
    public AlguiViewText TextTag(ViewGroup fatherLayout, CharSequence text, int color, Object... args) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatText(text, args)
            .setCatPadding(2, 0, 2, 0)
            .setCatTextColor(0xFFFFFFFF)
            .setCatBackColor(color)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个可弹出提示的帮助文本
    public AlguiViewText TextHelp(ViewGroup fatherLayout, CharSequence title, final CharSequence text, final Object... args) {
        final AlguiViewText obj=new AlguiViewText(aContext)
            .setCatText("[" + title + "]", args)
            .setCatTextColor(0xAEADB1B7)
            .setCatParentLayout(fatherLayout)
            ;
        obj.setCatCallback(new AlguiCallback.Click(){
                boolean isInit=false;
                AlguiViewText textV;
                PopupWindow popupWindow;
                public void click(boolean b) {
                    if (!isInit) {

                        textV = new AlguiViewText(aContext)
                            .setCatText(text, args)
                            .setCatPadding(5, 2, 5, 2)
                            .setCatBackColor(0xce151617)
                            //.setCatBorder(0.4f, 0xCEADB1B7)
                            ;

                        popupWindow = new PopupWindow(textV, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                        popupWindow.setOutsideTouchable(true);
                        isInit = true;
                    }

                    popupWindow.showAsDropDown(obj, 0, 0);
                }
            }
        );
        return obj;
    }
    //创建一个滚动文本
    public AlguiViewText TextRoll(ViewGroup fatherLayout, CharSequence text, Object... args) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatText(text, args)
            .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            .setCatWeight(1)
            .setCatTextRoll(true)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个渐变文本
    public AlguiViewText TextColors(ViewGroup fatherLayout, CharSequence text, int... colors) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatText(text)
            .setCatTextColor(colors)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个动态渐变文本
    public AlguiViewText TextColorsMove(ViewGroup fatherLayout, CharSequence text, int... colors) {
        AlguiViewText obj=new AlguiViewText(aContext)
            .setCatText(text)
            .setCatTextColor(colors)
            .setCatTextMoveGrad(true)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }


    //&&&&&&&&&&&&&&&&&&&&&&对于按钮&&&&&&&&&&&&&&&&&&&&&&
    //创建一个普通按钮
    public AlguiViewButton Button(ViewGroup fatherLayout, CharSequence text) {
        AlguiViewButton obj=new AlguiViewButton(aContext)
            .setCatText(text)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个长按钮
    public AlguiViewButton ButtonLong(ViewGroup fatherLayout, CharSequence text) {
        AlguiViewButton obj=new AlguiViewButton(aContext)
            .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            .setCatWeight(1)
            .setCatText(text)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个切换选项的按钮 传选项列表 每个选项都是一个视图 它们的ID自动根据添加顺序从0开始0,1,2...
    public AlguiViewItem SwitchItem(ViewGroup fatherLayout, CharSequence text, View... items) {
        AlguiViewItem obj=new AlguiViewItem(aContext)
            .setCatText(text)
            .setCatParentLayout(fatherLayout)
            .addItem(items)
            ;
        return obj;
    }

    //创建一个长按钮项
    public AlguiViewItem ButtonItemLong(ViewGroup fatherLayout, CharSequence text) {
        AlguiViewItem obj=new AlguiViewItem(aContext)
            .setCatText(text)
            .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            .setCatWeight(0)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个按钮项
    public AlguiViewItem ButtonItem(ViewGroup fatherLayout, CharSequence text) {
        AlguiViewItem obj=new AlguiViewItem(aContext)
            .setCatText(text)
            .setCatSize(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            .setCatWeight(0)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个图标按钮项
    public AlguiViewItem ButtonIcon(ViewGroup fatherLayout, CharSequence text, String Url_Base64_FilePath) {
        AlguiViewItem obj=new AlguiViewItem(aContext)
            .setCatText(text)
            .setCatSize(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            .setCatWeight(0)
            .setCatIcon(Url_Base64_FilePath)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个勾选项 监听器判断0未选中1选中
    public AlguiViewItem TickItem(ViewGroup fatherLayout, CharSequence text) {
        View v=new View(aContext);
        v.setVisibility(View.GONE);
        AlguiViewItem obj=new AlguiViewItem(aContext)
            .setCatText(text)
            .setCatParentLayout(fatherLayout)
            .addItem(0, v)
            .addItem(1, new AlguiViewImage(aContext, AlguiAssets.Icon.hook).setCatSize(11, 11).setCatColor(0xFFFFFFFF))
            ;
        return obj;
    }
    //创建一个切换开关选项按钮 监听器判断0关1开
    public AlguiViewItem Switch(ViewGroup fatherLayout, CharSequence text) {
        AlguiViewItem obj=new AlguiViewItem(aContext)
            .setCatText(text)
            .setCatParentLayout(fatherLayout)
            .addItem(0, new AlguiViewText(aContext, "关闭"))
            .addItem(1, new AlguiViewText(aContext, "开启"))
            ;
        return obj;
    }
    //创建一个普通复选框
    public AlguiViewCheckBox CheckBox(ViewGroup fatherLayout, CharSequence text) {
        AlguiViewCheckBox obj=new AlguiViewCheckBox(aContext)
            .setCatText(text)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }




    //&&&&&&&&&&&&&&&&&&&&&&对于拖动条&&&&&&&&&&&&&&&&&&&&&&
    //进度设置文本时占位符{v}代表当前进度 例如：进度{v}% 最终显示：进度30% 没有占位符时进度默认加在文本后面
    //创建一个普通拖动条，自定义小数位和递增递减按钮在哪个小数位调节
    public AlguiViewDragBar Drag(ViewGroup fatherLayout, CharSequence text, double min, double def, double max, int bit, int tuneBit) {
        AlguiViewDragBar obj=new AlguiViewDragBar(aContext)
            .setCatText(text)
            .setCatValueBit(bit)
            .setCatValueTuneBit(tuneBit)
            .setCatValueMin(min)
            .setCatValueMax(max)
            .setCatValue(def)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个整数拖动条 
    public AlguiViewDragBar DragInt(ViewGroup fatherLayout, CharSequence text, int min, int def, int max) {
        return Drag(fatherLayout, text, min, def, max, 0, 0);
    }
    //创建一个一位小数拖动条 
    public AlguiViewDragBar DragFloat1(ViewGroup fatherLayout, CharSequence text, float min, float def, float max) {
        return Drag(fatherLayout, text, min, def, max, 1, 1);
    }
    //创建一个两位小数拖动条 
    public AlguiViewDragBar DragFloat2(ViewGroup fatherLayout, CharSequence text, float min, float def, float max) {
        return Drag(fatherLayout, text, min, def, max, 2, 2);
    }
    //创建一个三位小数拖动条 
    public AlguiViewDragBar DragFloat3(ViewGroup fatherLayout, CharSequence text, float min, float def, float max) {
        return Drag(fatherLayout, text, min, def, max, 3, 3);
    }





    //&&&&&&&&&&&&&&&&&&&&&&对于输入框&&&&&&&&&&&&&&&&&&&&&&
    //创建一个普通文本输入框
    public AlguiViewInputBox InputText(ViewGroup fatherLayout, CharSequence text) {
        return InputText(fatherLayout, text, null);
    }
    //创建一个普通文本输入框 (带按钮)
    public AlguiViewInputBox InputText(ViewGroup fatherLayout, CharSequence text, CharSequence bText) {
        AlguiViewInputBox obj=new AlguiViewInputBox(aContext)
            .setCatButtonText(bText)
            .setCatInputHint(text)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个整数输入框
    public AlguiViewInputBox InputInt(ViewGroup fatherLayout, CharSequence text) {
        return InputInt(fatherLayout, text, null);
    }
    //创建一个整数输入框 (带按钮)
    public AlguiViewInputBox InputInt(ViewGroup fatherLayout, CharSequence text, CharSequence bText) {
        AlguiViewInputBox obj=new AlguiViewInputBox(aContext)
            .setCatButtonText(bText)
            .setCatInputHint(text)
            .setCatInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_SIGNED)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个小数输入框
    public AlguiViewInputBox InputFloat(ViewGroup fatherLayout, CharSequence text) {
        return InputFloat(fatherLayout, text, null);
    }
    //创建一个小数输入框 (带按钮)
    public AlguiViewInputBox InputFloat(ViewGroup fatherLayout, CharSequence text, CharSequence bText) {
        AlguiViewInputBox obj=new AlguiViewInputBox(aContext)
            .setCatButtonText(bText)
            .setCatInputHint(text)
            .setCatInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_SIGNED | InputType.TYPE_NUMBER_FLAG_DECIMAL)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }




    //&&&&&&&&&&&&&&&&&&&&&&对于其它&&&&&&&&&&&&&&&&&&&&&&

    //创建一个普通图片
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiViewImage Image(ViewGroup fatherLayout, String Url_Base64_FilePath) {
        AlguiViewImage obj=new AlguiViewImage(aContext)
            .setCatSize(50, 50)
            .setCatImage(Url_Base64_FilePath)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }

    //创建一个普通网页视图 支持 加载网站，加载HTML代码，加载HTML文件
    public AlguiViewWeb Web(ViewGroup fatherLayout, String website_htmlcode_htmlfile) {
        AlguiViewWeb obj=new AlguiViewWeb(aContext, AlguiActivity.MainActivity)
            .setCatWeb(website_htmlcode_htmlfile)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个普通三角形
    public AlguiViewTriangle Triangle(ViewGroup fatherLayout, int dirType) {
        AlguiViewTriangle obj=new AlguiViewTriangle(aContext)
            .setCatType(dirType)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个普通声音监测视图
    public AlguiViewSoundWave SoundMonitoring(ViewGroup fatherLayout, int style) {
        AlguiViewSoundWave obj=new AlguiViewSoundWave(aContext, AlguiActivity.MainActivity)
            .setCatStyle(style)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个线条 (自定义样式)
    public AlguiViewLine Line(ViewGroup fatherLayout, int color, int style) {
        AlguiViewLine obj=new AlguiViewLine(aContext)
            .setCatStyle(style)
            .setCatColor(color)
            .setCatParentLayout(fatherLayout)
            ;
        return obj;
    }
    //创建一个线条
    public AlguiViewLine Line(ViewGroup fatherLayout, int color) {
        return Line(fatherLayout, color, AlguiViewLine.Style.STYLE_SOLID);
    }




    //&&&&&&&&&&&&&&&&&&&&&&预制内容&&&&&&&&&&&&&&&&&&&&&&

    //选择运行环境对话框
    public void WinDialogEnv() {
        AlguiToolNetwork.GET("https://v1.hitokoto.cn/?&encode=text&c=a", null , new AlguiToolNetwork.NetworkCallback() {
                //请求成功
                @Override
                public void onSuccess(String response) {
                    if (response == null)
                        return;

                    final AlguiWinDialog dialog= WinDialogText_White("请选择运行环境", response);
                    dialog.setCatIsCanEnd(false);
                    dialog.setCatNoButtonText("正常环境");
                    dialog.setCatNoButtonClick(new AlguiCallback.Click(){
                            public void click(boolean sb) {
                                dialog.end();
                            }

                        }
                    );
                    dialog.setCatYesButtonText("ROOT环境");
                    dialog.setCatYesButtonClick(new AlguiCallback.Click(){
                            public void click(boolean sb) {
                                boolean b = AlguiRootService.isConnect();
                                if (!b) {
                                    AlguiRootService.setCallBack(new AlguiCallback.RootService(){
                                            public void rootService(boolean isOK) {
                                                if (isOK) {
                                                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info_gta5);
                                                    AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.magisk, "Root环境加载成功", "请谨慎操作，避免数据丢失！", 5);

                                                } else {
                                                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_alert);
                                                    AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info2, "Root环境已断开", null, 5);
                                                }
                                            }
                                        }
                                    );
                                    RootService.bind(new Intent(aContext, AlguiRootClient.class), new AlguiRootService());
                                } else {
                                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info_gta5);
                                    AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.magisk, "Root环境加载成功", "请谨慎操作，避免数据丢失！", 5);
                                }
                                dialog.end();
                            }

                        }
                    );
                }
                //请求失败
                @Override
                public void onFailure(String error) {

                }
            });



    }

}
