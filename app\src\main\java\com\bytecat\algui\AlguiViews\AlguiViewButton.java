package com.bytecat.algui.AlguiViews;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/11/12 21:09
 * @Describe Algui按钮
 */
import android.content.Context;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import com.bytecat.algui.AlguiManager.AlguiCallback;

public class AlguiViewButton extends AlguiViewText {

    public static final String TAG = "AlguiViewButton";

    //点击事件回调反馈
    AlguiCallback.Click call;
    boolean isInitClick = false;//是否已经初始化点击事件
    boolean isChecked = false;
    //初始化内部点击事件
    private void initClick() {
        setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                            //按下
                        case MotionEvent.ACTION_DOWN:
                            //进行宽高90%缩放动画
                            v.animate().scaleX(0.90f).scaleY(0.90f).setDuration(100);
                            
                            break;
                            //松开
                        case MotionEvent.ACTION_UP:
                            //恢复缩放动画
                            v.animate().scaleX(1f).scaleY(1f).setDuration(100);
                            callClick(!isChecked);//执行点击事件
                            break;
                            //事件取消时执行 (备份一份恢复操作放在这里 防止在动画期间迅速关闭窗口时动画卡一半崩溃)
                        case MotionEvent.ACTION_CANCEL:
                            //恢复缩放动画
                            v.animate().scaleX(1f).scaleY(1f).setDuration(100);
                            
                            break;
                    }
                    return true; 
                }
            });
        isInitClick = true;
    }
    //设置点击事件回调反馈接口
    public AlguiViewButton setCatCallback(AlguiCallback.Click c) {
        if (c == null) {
            setOnTouchListener(null);
            isInitClick = false;
        } else {
            call = c;
            if (!isInitClick) {
                initClick();
            }
        }
        return this;
    }
    //获取点击事件回调反馈接口
    public AlguiCallback.Click getByteCallback() {
        return call;
    }
    //代码执行点击事件
    public AlguiViewButton callClick(boolean b) {
        isChecked = b;
        if (call != null)
            call.click(isChecked);//回调
        return this;
    }




    //设置文本发光效果
    public AlguiViewButton setCatTextGlow(float radius, int color) {
        super.setCatTextGlow(radius, color);
        return this;
    }
    //设置文本动态渐变效果启动状态
    public AlguiViewButton setCatTextMoveGrad(boolean b) {
        super.setCatTextMoveGrad(b);
        return this;
    }
    //设置文本渐变速度
    public AlguiViewButton setCatTextGradSpeed(float s) {
        super.setCatTextGradSpeed(s);
        return this;
    }
    //设置文本滚动效果启动状态
    public AlguiViewButton setCatTextRoll(boolean b) {
        super.setCatTextRoll(b);
        return this;
    }
    //设置文本滚动速度
    public AlguiViewButton setCatTextRollSpeed(float s) {
        super.setCatTextRollSpeed(s);
        return this;
    }
    //设置文本
    public AlguiViewButton setCatText(CharSequence text,Object... args) {
        super.setCatText(text,args);
        return this;
    }
    //设置Html文本
    public AlguiViewButton setCatTextHtml(String text) {
        super.setCatTextHtml(text);
        return this;
    }
    //设置文本颜色
    public AlguiViewButton setCatTextColor(int... color) {
        super.setCatTextColor(color);
        return this;
    }
    //设置文本大小
    public AlguiViewButton setCatTextSize(float size) {
        super.setCatTextSize(size);
        return this;
    }
    //设置Assets文件夹字体文件作为文本字体
    public AlguiViewButton setCatTextTFAssets(String assetsTfFileName) {
        super.setCatTextTFAssets(assetsTfFileName, Typeface.NORMAL);
        return this;
    }//重载+样式
    public AlguiViewButton setCatTextTFAssets(String assetsTfFileName, int style) {
        super.setCatTextTFAssets(assetsTfFileName, style);
        return this;
    }
    //设置文本重力
    public AlguiViewButton setCatTextGravity(int g) {
        super.setCatTextGravity(g);
        return this;
    }
    //设置文本是否响应超链接
    public AlguiViewButton setCatTextIsLink(boolean b) {
        super.setCatTextIsLink(b);
        return this;
    }

    //设置布局大小
    public AlguiViewButton setCatSize(float w, float h) {
        super.setCatSize(w, h);
        return this;
    }
 
    //设置权重
    public AlguiViewButton setCatWeight(float weight) {
        super.setCatWeight(weight);
        return this;
    }

    //设置内边距
    public AlguiViewButton setCatPadding(float left, float top, float right, float bottom) {
        super.setCatPadding(left, top, right, bottom);
        return this;
    }

    //设置外边距
    public AlguiViewButton setCatMargins(float left, float top, float right, float bottom) {
        super.setCatMargins(left, top, right, bottom);
        return this;
    }
  
    //子类设置背景颜色
    public AlguiViewButton setCatBackColor(int... backColor) {
        super.setCatBackColor(backColor);
        return this;
    }
    //设置圆角半径
    public AlguiViewButton setCatRadiu(float r) {
        super.setCatRadiu(r);
        return this;
    }
    //设置描边
    public AlguiViewButton setCatBorder(float size, int color) {
        super.setCatBorder(size, color);
        return this;
    }

    //设置父布局
    public AlguiViewButton setCatParentLayout(ViewGroup vg) {
        super.setCatParentLayout(vg);
        return this;
    }


    public AlguiViewButton(Context c) {
        super(c);
        setCatText(TAG);//设置文本
        setCatTextSize(7);//文本大小
        setCatTextColor(0xFFFFFFFF);//文本颜色
        setCatTextGravity(Gravity.CENTER);//文本对齐方式
        setCatPadding(5, 3, 5, 3);//内边距
        setCatBackColor(0xff274A72);//设置背景颜色 (支持多颜色渐变)
        initClick();//初始化内部点击事件
    }   

    public AlguiViewButton(Context c, CharSequence text) {
        this(c);
        setCatText(text);
    }


}
