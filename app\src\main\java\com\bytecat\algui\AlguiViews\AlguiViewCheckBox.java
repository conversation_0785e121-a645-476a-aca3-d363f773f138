package com.bytecat.algui.AlguiViews;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/11/08 17:09
 * @Describe Algui复选框
 */
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiViews.AlguiFrameLayout;

public class AlguiViewCheckBox extends AlguiLinearLayout {

    public static final String TAG = "AlguiViewCheckBox";

    Context aContext;
    AlguiFrameLayout box;//复选框盒子
    AlguiViewImage selectIcon;//复选框选中视图
    AlguiViewText title;//复选框标题


    // 获取复选框盒子
    public AlguiFrameLayout getByteBox() {
        return box;
    }



    // 获取复选框选中图标
    public AlguiViewImage getByteSelectIcon() {
        return selectIcon;
    }



    // 获取复选框标题
    public AlguiViewText getByteTitle() {
        return title;
    }




    //点击事件回调反馈
    boolean isSelect = false; // 复选框选中状态
    AlguiCallback.Click call;
    boolean isInitClick=false;//是否已经初始化点击事件
    //初始化内部点击事件
    private void initClick() {
        setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setCatSelect(!isSelect);//执行点击切换选中状态
                }
            });
        isInitClick = true;
    }
    //设置点击事件回调反馈接口
    public AlguiViewCheckBox setCatCallback(AlguiCallback.Click c) {
        if (c == null) {
            setOnClickListener(null);
            isInitClick = false;
        } else {
            call = c;
            if (!isInitClick) {
                initClick();
            }
        }
        return this;
    }
    //获取点击事件回调反馈接口
    public AlguiCallback.Click getByteCallback() {
        return call;
    }
    //设置选中状态
    public AlguiViewCheckBox setCatSelect(boolean isSelect) {
        this.isSelect = isSelect;
        
            selectIcon.setVisibility(isSelect ?View.VISIBLE: View.GONE);//控制选中图标显隐

        if (call != null)
            call.click(isSelect);//回调
        return this;
    }
  


    //继承修复父类方法链，让父类方法链嵌套在子类的方法链中
    // 设置大小
    public AlguiViewCheckBox setCatSize(float w, float h) {
        super.setCatSize(w, h);  // 调用父类的 setCatSize 方法
        return this;
    }

    // 设置权重
    public AlguiViewCheckBox setCatWeight(float weight) {
        super.setCatWeight(weight);  // 调用父类的 setCatWeight 方法
        return this;
    }

    // 设置内边距
    public AlguiViewCheckBox setCatPadding(float left, float top, float right, float bottom) {
        super.setCatPadding(left, top, right, bottom);  // 调用父类的 setCatPadding 方法
        return this;
    }

    // 设置外边距
    public AlguiViewCheckBox setCatMargins(float left, float top, float right, float bottom) {
        super.setCatMargins(left, top, right, bottom);  // 调用父类的 setCatMargins 方法
        return this;
    }

    // 设置背景颜色
    public AlguiViewCheckBox setCatBackColor(int... backColor) {
        super.setCatBackColor(backColor);  // 调用父类的 setCatBackColor 方法
        return this;
    }

    // 设置圆角半径
    public AlguiViewCheckBox setCatRadiu(float radiu) {
        super.setCatRadiu(radiu);  // 调用父类的 setCatRadiu 方法
        return this;
    }

    // 设置描边
    public AlguiViewCheckBox setCatBorder(float borderSize, int borderColor) {
        super.setCatBorder(borderSize, borderColor);  // 调用父类的 setCatBorder 方法
        return this;
    }

    // 设置父布局
    public AlguiViewCheckBox setCatParentLayout(ViewGroup vg) {
        super.setCatParentLayout(vg);  // 调用父类的 setCatParentLayout 方法
        return this;
    }


    // 设置文本大小
    public AlguiViewCheckBox setCatTextSize(float size) {
        title.setCatTextSize(size);//设置标题大小
        //盒子应该为正方形，并且大小随标题x2
        box.setCatSize(size * 2.1f, size * 2.1f);
        super.setCatSize(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);

        return this;
    }

    //设置盒子圆角
    public AlguiViewCheckBox setCatBoxRadiu(float r) {
        box.setCatRadiu(r);
        return this;
    }

    //设置盒子描边
    public AlguiViewCheckBox setCatBoxBorder(float size, int color) {
        box.setCatBorder(size, color);
        return this;
    }

    //设置盒子背景颜色
    public AlguiViewCheckBox setCatBoxBackColor(int... color) {
        box.setCatBackColor(color);
        return this;
    }
    //设置盒子图标颜色
    public AlguiViewCheckBox setCatBoxIconColor(int color) {
        selectIcon.setCatColor(color);
        return this;
    }
   
    //设置盒子选中图标
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiViewCheckBox setCatBoxIcon(String Url_Base64_AssetsFile) {
        selectIcon.setCatImage(Url_Base64_AssetsFile);
        return this;
    }


    //设置文本
    public AlguiViewCheckBox setCatText(CharSequence text,Object... args) {
        title.setCatText(text,args);
        return this;
    }

    //设置文本颜色
    public AlguiViewCheckBox setCatTextColor(int... color) {
        title.setCatTextColor(color);

        return this;
    }
    //设置文本动态渐变效果启动状态
    public AlguiViewCheckBox setCatTextMoveGrad(boolean b) {
        title.setCatTextMoveGrad(b);
        return this;
    }
    //设置文本发光
    public AlguiViewCheckBox setCatTextGlow(float radius, int color) {
        title.setCatTextGlow(radius, color);
        return this;
    }

    //设置Assets文件夹字体文件作为文本字体
    public AlguiViewCheckBox setCatTextTFAssets(String assetsTfFileName) {
        title.setCatTextTFAssets(assetsTfFileName, Typeface.NORMAL);
        return this;
    }//重载+样式
    public AlguiViewCheckBox setCatTextTFAssets(String assetsTfFileName, int style) {
        title.setCatTextTFAssets(assetsTfFileName, style);
        return this;
    }
    //设置选中图标可见性
    /*public AlguiCheckBox setIconVisibility(boolean b) {
     if (b) {
     selectIcon.setVisibility(View.VISIBLE);//显示选中视图
     } else {
     selectIcon.setVisibility(View.GONE);//隐藏选中视图
     }
     isSelect = b;
     return this;
     }*/

    public AlguiViewCheckBox(Context c) {
        super(c);
        aContext = c;
        init();
    }

    public AlguiViewCheckBox(Context c, CharSequence text) {
        this(c);
        title.setCatText(text);
    }
    private void init() {
        //根布局
        setCatSize(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        setOrientation(LinearLayout.HORIZONTAL);//横向
        setGravity(Gravity.CENTER_VERTICAL);//垂直居中
        //复选框容器
        box = new AlguiFrameLayout(aContext);
        box.setCatMargins(0, 0, 3f, 0);//外边距
        box.setCatPadding(1, 1, 1, 1);
        box.setCatBackColor(0xFF20324D);//背景颜色
        //复选框选中视图
        selectIcon = new AlguiViewImage(aContext);
        selectIcon.setCatImageBase64(AlguiAssets.Icon.hook);
        selectIcon.setCatColor(0xFF4296FA);
        //标题
        title = new AlguiViewText(aContext);
        title.setCatSize(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        title.setCatText(TAG);
        title.setCatTextColor(0xFFFFFFFF);
        title.setCatMargins(0, 0, 3f, 0);//外边距

        box.addView(selectIcon);//复选框容器添加选中视图
        addView(box);//根布局添加复选框容器
        addView(title);//根布局添加标题

        initClick();
        setCatSelect(isSelect);//默认选中状态
        setCatTextSize(7);//设置缩放大小

    }


    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }











}
