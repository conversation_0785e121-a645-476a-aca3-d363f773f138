package com.bytecat.algui.AlguiViews;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/15 13:49
 * @Describe Algui拖动条 (只针对于横向拖动条不支持竖向)
 */
import android.content.Context;
import android.graphics.Typeface;
import android.graphics.drawable.ClipDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.LayerDrawable;
import android.os.Build;
import android.text.SpannableStringBuilder;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import java.util.Arrays;

public class AlguiViewDragBar extends AlguiLinearLayout {

    public static final String TAG = "AlguiViewDragBar";
    //占位符
    public static final String PROGRESS_PLACEHOLDER = "{v}";//代表拖动条进度

    Context aContext;
    AlguiFrameLayout dragLayout;//拖动条布局
    SeekBar drag;//拖动条
    GradientDrawable dragBack;//拖动条背景
    GradientDrawable dragFore;//拖动条前景
    GradientDrawable dragSlider;//拖动条滑块
    AlguiViewText dragText;//拖动条文本
    AlguiViewButton minusB;//递减按钮
    AlguiViewButton addB;//递增按钮
    boolean isUserUpdate;//是否只允许用户更新进度

    int ph=-1;//进度文本高度
    CharSequence valueText="";//进度文本
    int occStart;//进度占位符起始位置
    int occEnd;//进度占位符结束位置
    int __bit__;//小数位数
    int __TuneBit__;//递增递减在哪个小数位

    //只存储未经转换为整数的虚进度
    double __P__;//当前进度
    double __min__;//最小进度
    double __max__;//最大进度

    //对外部提供get拓展方法
    public AlguiFrameLayout getByteDragLayout() {
        return dragLayout;
    }

    public SeekBar getByteDragBar() {
        return drag;
    }

    public GradientDrawable getByteDragBack() {
        return dragBack;
    }

    public GradientDrawable getByteDragFore() {
        return dragFore;
    }

    public GradientDrawable getByteDragSlider() {
        return dragSlider;
    }

    public AlguiViewText getByteDragText() {
        return dragText;
    }

    public AlguiViewButton getByteMinusButton() {
        return minusB;
    }

    public AlguiViewButton getByteAddButton() {
        return addB;
    }


    //拖动事件回调反馈
    AlguiCallback.DragBar call;
    boolean isInitDrag = false;//是否已经初始化拖动事件
    //初始化内部拖动事件
    private void initDrag() {
        //输入框输入文本监听器
        drag.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                //更新double进度
                public double update(int p) {
                    String vstr=Integer.toString(p);
                    __P__ = p;

                    //对于有小数
                    if (__bit__ != 0) {
                        //将当前进度转换为对应小数位的double
                        vstr = String.format("%." + __bit__ + "f", int2double(p, -__bit__));
                        try {
                            __P__ = Double.parseDouble(vstr); //保存进度double
                        } catch (NumberFormatException e) {
                            dragText.setCatText(vstr + "异常" + ":" + e.getMessage());
                        }
                    }

                    //更新文本
                    if (occStart >= 0) 
                        dragText.setCatText(new SpannableStringBuilder(valueText).replace(occStart, occEnd, vstr));
                    else
                        dragText.setCatText(valueText + vstr);
                    //dragText.setCatText("double:" + __P__ + "|int:" + p + "|str:" + vstr);

                    return __P__;
                }

                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {
                    // 当开始拖动拖动条时调用
                    update(seekBar.getProgress());
                    if (call != null)
                        call.start(__P__);
                }
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    if(isUserUpdate&&!fromUser)return;
                    // 当拖动条进度改变时调用
                    //将当前进度转换为对应小数位的double
                    update(progress);
                    if (call != null)
                        call.update(__P__);
                }

                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {
                    // 当停止拖动拖动条时调用
                    update(seekBar.getProgress());
                    if (call != null)
                        call.end(__P__);
                }
            });
        //递减
        minusB.setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    if (call != null)
                        call.start(__P__);
                    __P__ -= Math.pow(10, -__TuneBit__);
                    setCatValue(__P__);
                    if (call != null)
                        call.end(__P__);
                }
            }
        );

        //递增
        addB.setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    if (call != null)
                        call.start(__P__);
                    __P__ += Math.pow(10, -__TuneBit__);
                    setCatValue(__P__);
                    if (call != null)
                        call.end(__P__);
                }
            }
        );
        isInitDrag = true;
    }

    //设置拖动事件回调反馈接口
    public AlguiViewDragBar setCatCallback(AlguiCallback.DragBar c) {
        if (c == null) {
            drag.setOnSeekBarChangeListener(null);       
            minusB.setCatCallback(null);
            addB.setCatCallback(null);
            isInitDrag = false;
        } else {
            call = c;
            if (!isInitDrag) {
                initDrag();
            }
        }
        return this;
    }

    //获取拖动事件回调反馈接口
    public AlguiCallback.DragBar getByteCallback() {
        return call;
    }
    //设置是否只接受用户更新进度
    public AlguiViewDragBar setCatIsUserUpdate(boolean isUserUpdate){
        this.isUserUpdate=isUserUpdate;
        return this;
    }
    //核心转换器
    //int和double互转 参数：值，小数位数(例 -3三位小数1变0.001 3转换回整数0.001变1)
    public static double int2double(double num, int bit) {
        //计算乘积 10的bit次方
        double factor = Math.pow(10, bit); 
        return num * factor;
    }


    //设置进度小数位数
    public AlguiViewDragBar setCatValueBit(int bit) {
        __bit__ = bit;
        //更新进度
        setCatValueMax(__max__);
        setCatValueMin(__min__);
        setCatValue(__P__);
        setCatValueTuneBit(__bit__);
        return this;
    }
    //设置在哪个小数位进行递增递减调节
    public AlguiViewDragBar setCatValueTuneBit(int bit) {
        if (bit > __bit__)
            bit = __bit__;
        __TuneBit__ = bit;
        return this;
    }
    //设置进度
    public AlguiViewDragBar setCatValue(double p) {
        __P__ = p;
        if (__P__ < __min__)
            __P__ = __min__;

        if (__P__ > __max__)
            __P__ = __max__;

        final int v ;
        if (__bit__ != 0) {
            v = (int)int2double(__P__, __bit__);
        } else {
            v = (int)__P__;
        }
        //必须在UI渲染线程更新进度 否则无法更新进度条进度UI
        drag.post(new Runnable() 
            {
                @Override
                public void run() {
                    drag.setProgress(v);
                }
            });
        return this;
    }
    //设置最小进度
    public AlguiViewDragBar setCatValueMin(double min) {
        __min__ = min;     
        if (__min__ >= __max__)
        //__min__ = __max__ - 1;
        //必须先确保最大进度始终比最小进度大再设置最小进度 否则设置会异常丢失一位
            drag.setMax((int)min + 10);


        if (__bit__ != 0) {
            int v = (int)int2double(__min__, __bit__);
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O)//安卓8以上
                drag.setMin(v);
        } else {
            int v = (int)__min__;
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O)//安卓8以上
                drag.setMin(v);
        }

        if (__P__ < __min__) {
            setCatValue(__min__);
        }
        return this;
    }
    //设置最大进度
    public AlguiViewDragBar setCatValueMax(double max) {
        __max__ = max;
        if (__max__ <= __min__)
            __max__ = __min__ + 1;


        if (__bit__ != 0) {
            int v = (int)int2double(__max__, __bit__);
            drag.setMax(v);
        } else {
            int v=(int)__max__;
            drag.setMax(v);
        }
        if (__P__ > __max__) {
            setCatValue(__max__);
        }
        return this;
    }

    //&继承父类方法链
    // 设置大小
    public AlguiViewDragBar setCatSize(float w, float h) {
        super.setCatSize(w, h); // 调用父类的方法
        return this;
    }

    // 设置权重
    public AlguiViewDragBar setCatWeight(float weight) {
        super.setCatWeight(weight); // 调用父类的方法
        return this;
    }

    // 设置内边距
    public AlguiViewDragBar setCatPadding(float left, float top, float right, float bottom) {
        super.setCatPadding(left, top, right, bottom); // 调用父类的方法
        return this;
    }

    // 设置外边距
    public AlguiViewDragBar setCatMargins(float left, float top, float right, float bottom) {
        super.setCatMargins(left, top, right, bottom); // 调用父类的方法
        return this;
    }

    // 设置圆角半径
    public AlguiViewDragBar setCatRadiu(float radiu) {
        super.setCatRadiu(radiu); // 调用父类的方法
        return this;
    }

    // 设置描边
    public AlguiViewDragBar setCatBorder(float borderSize, int borderColor) {
        super.setCatBorder(borderSize, borderColor); // 调用父类的方法
        return this;
    }

    // 设置父布局
    public AlguiViewDragBar setCatParentLayout(ViewGroup vg) {
        super.setCatParentLayout(vg); // 调用父类的方法
        return this;
    }






    //拖动条-----
    //设置进度文本
    public AlguiViewDragBar setCatText(CharSequence text, Object... args) {
        valueText = text;
        //获取进度占位符起始和结束位置
        occStart = text.toString().indexOf(PROGRESS_PLACEHOLDER);
        occEnd = occStart + PROGRESS_PLACEHOLDER.length();
        String str ="";
        if (__bit__ != 0)
            str = String.format("%." + __bit__ + "f", __P__);
        else
            str = Integer.toString((int)__P__);

        //存在进度占位符时在占位符处补全进度值
        if (occStart >= 0) {
            dragText.setCatText(new SpannableStringBuilder(text).replace(occStart, occEnd, str), args);
        } else {
            dragText.setCatText(text + str, args);
        }
        return this;
    }

    //设置文本颜色
    public AlguiViewDragBar setCatTextColor(int... color) {
        dragText.setCatTextColor(color);
        return this;
    }
    //设置文本动态渐变效果启动状态
    public AlguiViewDragBar setCatTextMoveGrad(boolean b) {
        dragText.setCatTextMoveGrad(b);
        return this;
    }
    //设置文本发光
    public AlguiViewDragBar setCatTextGlow(float radius, int color) {
        dragText.setCatTextGlow(radius, color);
        return this;
    }
    //设置文本字体 (Assets文件夹下的字体文件名)
    public AlguiViewDragBar setCatTextTFAssets(String assetsTfFileName) {
        dragText.setCatTextTFAssets(assetsTfFileName, Typeface.NORMAL);
        return this;
    }//重载+样式
    public AlguiViewDragBar setCatTextTFAssets(String assetsTfFileName, int style) {
        dragText.setCatTextTFAssets(assetsTfFileName, style);
        return this;
    }
    // 设置拖动条背景颜色
    public AlguiViewDragBar setCatBackColor(int... backColor) {
        if (backColor.length == 1) {
            //单个颜色
            dragBack.setColor(backColor[0]);
        } else if (backColor.length > 1) {
            //多个颜色，使用渐变
            dragBack.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT); //渐变方向
            //最后一个元素如果是渐变类型则应用否则默认线性渐变
            if (isGdType(backColor[backColor.length - 1])) {
                dragBack.setGradientType(backColor[backColor.length - 1]);
                int[] newArray = Arrays.copyOf(backColor, backColor.length - 1);//删除最后一个元素
                dragBack.setColors(newArray);//设置颜色
            } else {
                dragBack.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                dragBack.setColors(backColor);//设置颜色
            }
        }
        return this;
    }

    // 设置拖动条背景描边
    public AlguiViewDragBar setCatBackBorder(float borderSize, int borderColor) {
        dragBack.setStroke((int)dp2px(borderSize), borderColor);
        return this;
    }

    // 设置拖动条前景颜色
    public AlguiViewDragBar setCatForeColor(int... backColor) {
        if (backColor.length == 1) {
            //单个颜色
            dragFore.setColor(backColor[0]);
        } else if (backColor.length > 1) {
            //多个颜色，使用渐变
            dragFore.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT); //渐变方向
            //最后一个元素如果是渐变类型则应用否则默认线性渐变
            if (isGdType(backColor[backColor.length - 1])) {
                dragFore.setGradientType(backColor[backColor.length - 1]);
                int[] newArray = Arrays.copyOf(backColor, backColor.length - 1);//删除最后一个元素
                dragFore.setColors(newArray);//设置颜色
            } else {
                dragFore.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                dragFore.setColors(backColor);//设置颜色
            }
        }
        return this;
    }

    // 设置拖动条前景描边
    public AlguiViewDragBar setCatForeBorder(float borderSize, int borderColor) {
        dragFore.setStroke((int)dp2px(borderSize), borderColor);
        return this;
    }

    // 设置拖动条滑块颜色
    public AlguiViewDragBar setCatSliderColor(int... backColor) {
        if (backColor.length == 1) {
            //单个颜色
            dragSlider.setColor(backColor[0]);
        } else if (backColor.length > 1) {
            //多个颜色，使用渐变
            dragSlider.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT); //渐变方向
            //最后一个元素如果是渐变类型则应用否则默认线性渐变
            if (isGdType(backColor[backColor.length - 1])) {
                dragSlider.setGradientType(backColor[backColor.length - 1]);
                int[] newArray = Arrays.copyOf(backColor, backColor.length - 1);//删除最后一个元素
                dragSlider.setColors(newArray);//设置颜色
            } else {
                dragSlider.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                dragSlider.setColors(backColor);//设置颜色
            }
        }
        return this;
    }

    // 设置拖动条滑块描边
    public AlguiViewDragBar setCatSliderBorder(float borderSize, int borderColor) {
        dragSlider.setStroke((int)dp2px(borderSize), borderColor);
        return this;
    }




    // 设置按钮点击边框颜色
    public AlguiViewDragBar setCatButtonClickBorderColor(int... backColor) {
        super.setCatBackColor(backColor); // 调用父类的方法
        return this;
    }

    //设置隐藏递增递减按钮
    public AlguiViewDragBar setCatHideButton(boolean isHideButton) {
        if (isHideButton) {
            //隐藏
            addB.setVisibility(LinearLayout.GONE);
            minusB.setVisibility(LinearLayout.GONE);
        } else {
            //显示
            addB.setVisibility(LinearLayout.VISIBLE);
            minusB.setVisibility(LinearLayout.VISIBLE);
        }
        return this;
    }






    //递增按钮-----

    //设置递增按钮背景颜色
    public AlguiViewDragBar setCatAddButtonBackColor(int... backColor) {
        addB.setCatBackColor(backColor);
        return this;
    }
    //设置递增按钮描边
    public AlguiViewDragBar setCatAddButtonBorder(float size, int color) {
        addB.setCatBorder(size, color);
        return this;
    }
    //设置递增按钮文本
    public AlguiViewDragBar setCatAddButtonText(CharSequence text) {
        addB.setCatText(text);
        return this;
    }
    //设置递增按钮文本颜色
    public AlguiViewDragBar setCatAddButtonTextColor(int... color) {
        addB.setCatTextColor(color);
        return this;
    }
    //设置递增按钮文本动态渐变效果启动状态
    public AlguiViewDragBar setCatAddButtonTextMoveGrad(boolean b) {
        addB.setCatTextMoveGrad(b);
        return this;
    }
    //设置递增按钮文本发光
    public AlguiViewDragBar setCatAddButtonTextGlow(float radius, int color) {
        addB.setCatTextGlow(radius, color);
        return this;
    }
    //设置递增按钮文本字体 (Assets文件夹下的字体文件名)
    public AlguiViewDragBar setCatAddButtonTextTFAssets(String assetsTfFileName) {
        addB.setCatTextTFAssets(assetsTfFileName, Typeface.NORMAL);
        return this;
    }//重载+样式
    public AlguiViewDragBar setCatAddButtonTextTFAssets(String assetsTfFileName, int style) {
        addB.setCatTextTFAssets(assetsTfFileName, style);
        return this;
    }






    //递减按钮-----

    //设置递减按钮背景颜色
    public AlguiViewDragBar setCatMinusButtonBackColor(int... backColor) {
        minusB.setCatBackColor(backColor);
        return this;
    }
    //设置递减按钮描边
    public AlguiViewDragBar setCatMinusButtonBorder(float size, int color) {
        minusB.setCatBorder(size, color);
        return this;
    }
    //设置递减按钮文本
    public AlguiViewDragBar setCatMinusButtonText(CharSequence text) {
        minusB.setCatText(text);
        return this;
    }
    //设置递减按钮文本颜色
    public AlguiViewDragBar setCatMinusButtonTextColor(int... color) {
        minusB.setCatTextColor(color);
        return this;
    }
    //设置递减按钮文本动态渐变效果启动状态
    public AlguiViewDragBar setCatMinusButtonTextMoveGrad(boolean b) {
        minusB.setCatTextMoveGrad(b);
        return this;
    }
    //设置递减按钮文本发光
    public AlguiViewDragBar setCatMinusButtonTextGlow(float radius, int color) {
        minusB.setCatTextGlow(radius, color);
        return this;
    }
    //设置递减按钮文本字体 (Assets文件夹下的字体文件名)
    public AlguiViewDragBar setCatMinusButtonTextTFAssets(String assetsTfFileName) {
        minusB.setCatTextTFAssets(assetsTfFileName, Typeface.NORMAL);
        return this;
    }//重载+样式
    public AlguiViewDragBar setCatMinusButtonTextTFAssets(String assetsTfFileName, int style) {
        minusB.setCatTextTFAssets(assetsTfFileName, style);
        return this;
    }

    public AlguiViewDragBar(Context context) {
        super(context);
        aContext = context;
        init();
    }

    public AlguiViewDragBar(Context context, CharSequence text) {
        this(context);
        setCatText(text);
    }

    //设置所有文本大小 (相对进度文本改变所有子视图大小)
    public AlguiViewDragBar setCatTextSize(float size) {
        dragText.setCatTextSize(size);//进度文本大小
        minusB.setCatTextSize(size);//递减按钮文本大小
        addB.setCatTextSize(size);//递增按钮文本大小
        //等待文本测量完成才设置拖动条的样式 确保拖动条高度与文本高度一致
        dragText.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    //移除监听器以避免重复调用
                    //dragText.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    //获取文本高度
                    int textHeight=dragText.getHeight();
                    //只有在文本高度发生改变时才更新
                    if (textHeight == ph && ph != -1)
                        return;
                    //AlguiLog.d(textHeight + "高");
                    //设置拖动条背景大小：宽度拉满高度为文本高度
                    dragBack.setSize(AlguiFrameLayout.LayoutParams.MATCH_PARENT, textHeight);
                    //设置拖动条前景大小：宽度拉满高度为文本高度
                    dragFore.setSize(AlguiFrameLayout.LayoutParams.MATCH_PARENT, textHeight);
                    //设置拖动条滑块大小：宽高为文本高度 确保正方形
                    dragSlider.setSize(textHeight, textHeight);

                    //设置递减递增按钮宽高为文本高度 确保正方形
                    minusB.setLayoutParams(new AlguiLinearLayout.LayoutParams(textHeight, textHeight));
                    addB.setLayoutParams(new AlguiLinearLayout.LayoutParams(textHeight, textHeight));
                    //应用样式
                    drag.setProgressDrawable(new LayerDrawable(new Drawable[]{
                                                                   dragBack,
                                                                   new ClipDrawable(dragFore, Gravity.LEFT, ClipDrawable.HORIZONTAL)
                                                               }));
                    drag.setThumb(dragSlider);
                    ph = textHeight;//更新文本高度
                }
            });
        return this;
    }


    private void init() {
        //根布局
        super.setCatSize(LinearLayout.LayoutParams.MATCH_PARENT,
                         LinearLayout.LayoutParams.WRAP_CONTENT);
        super.setCatWeight(1);
        super.setCatBackColor(0xFF858585);//将背景颜色作为按钮点击反馈边框颜色 因为按钮有缩放动画
        super.setOrientation(LinearLayout.HORIZONTAL);//横向
        super.setGravity(Gravity.CENTER_VERTICAL);//垂直居中

        //拖动条布局
        dragLayout = new AlguiFrameLayout(aContext);
        dragLayout.setCatSize(AlguiFrameLayout.LayoutParams.MATCH_PARENT, AlguiFrameLayout.LayoutParams.WRAP_CONTENT);
        dragLayout.setCatWeight(1);

        //拖动条
        drag = new SeekBar(aContext);
        drag.setPadding(0, 0, 0, 0);//确保SeekBar没有内边距
        //安卓9以下没有
        // drag.setMinWidth(0);//确保SeekBar不限制最小宽度
        // drag.setMinHeight(0);//确保SeekBar不限制最小高度

        //拖动条进度文本
        dragText = new AlguiViewText(aContext);
        dragText.setCatPadding(0, 2, 0, 2);//拖动条文本内边距

        //先添加拖动条
        dragLayout.addView(drag, new AlguiFrameLayout.LayoutParams(
                               AlguiFrameLayout.LayoutParams.MATCH_PARENT, 
                               AlguiFrameLayout.LayoutParams.WRAP_CONTENT));
        //再往中心叠加文本
        dragLayout.addView(dragText, new AlguiFrameLayout.LayoutParams(
                               AlguiFrameLayout.LayoutParams.WRAP_CONTENT, 
                               AlguiFrameLayout.LayoutParams.WRAP_CONTENT, Gravity.CENTER));

        //递减按钮
        minusB = new AlguiViewButton(aContext, "-");
        minusB.setCatPadding(0, 0, 0, 0);//重置内边距

        //递增按钮
        addB = new AlguiViewButton(aContext, "+");
        addB.setCatPadding(0, 0, 0, 0);//重置内边距

        //构造布局
        addView(dragLayout);
        addView(minusB);
        addView(addB);

        //进度条背景样式
        dragBack = new GradientDrawable();
        dragBack.setShape(GradientDrawable.RECTANGLE);
        //进度条前景样式
        dragFore  = new GradientDrawable();
        dragFore.setShape(GradientDrawable.RECTANGLE);
        //滑块样式
        dragSlider = new GradientDrawable();
        dragSlider.setShape(GradientDrawable.RECTANGLE);

        //默认拖动条颜色
        setCatBackColor(0xFF20324D);//背景颜色
        setCatForeColor(0);//前景颜色
        setCatSliderColor(0xFF3D85E0);//滑块颜色

        setCatTextSize(7);//初始化所有文本大小 (内部初始化大小后才会应用拖动条样式)

        initDrag();//初始化拖动事件

        setCatValueBit(0);//默认小数位0 整数
        setCatValueMin(0);//默认最小进度
        setCatValueMax(100);//默认最大进度
        setCatValueTuneBit(0);//默认递增递减小数位

        setCatText(TAG);//默认进度文本
    }


    //判断是否为渐变类型
    public boolean isGdType(int typeIndex) {
        return typeIndex == GradientDrawable.LINE ||
            typeIndex == GradientDrawable.LINEAR_GRADIENT ||
            typeIndex == GradientDrawable.OVAL ||
            typeIndex == GradientDrawable.RADIAL_GRADIENT ||
            typeIndex == GradientDrawable.RECTANGLE ||
            typeIndex == GradientDrawable.RING ||
            typeIndex == GradientDrawable.SWEEP_GRADIENT;
    }
}
