package com.bytecat.algui.AlguiViews;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/10/25 12:15
 * @Describe Algui折叠菜单
 */
import androidx.annotation.Nullable;
import android.content.Context;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiViews.AlguiViewTriangle;
import java.util.ArrayList;
import com.bytecat.algui.AlguiManager.AlguiAssets;

public class AlguiViewFoldMenu extends AlguiLinearLayout {

    public static final String TAG = "AlguiViewFoldMenu";

    Context aContext;
    AlguiViewItem title;//标题
    AlguiViewTriangle triangleView;//三角形
    AlguiFlowLayout insideLayout;//内部展开布局
    float textSize=-1;//文本大小 (其它视图将参考此大小进行缩放)

    //点击事件回调反馈
    AlguiCallback.Click call;
    boolean isUnfold = false;//展开状态
    boolean isEnableUnfold = true;//是否可以展开
    boolean isInitClick=false;//是否已经初始化点击事件
    //初始化内部点击事件
    private void initClick() {
        //标题点击事件
        title.setCatCallback(new AlguiCallback.Item() {
                @Override
                public void item(int id) {
                    setCatUnfold(!isUnfold);
                }
            });
        isInitClick = true;
    }
    //设置点击事件回调反馈接口
    public AlguiViewFoldMenu setCatCallback(AlguiCallback.Click c) {
        if (c == null) {
            title.setOnClickListener(null);
            isInitClick = false;
        } else {
            call = c;
            if (!isInitClick) {
                initClick();
            }
        }
        return this;
    }

    //获取点击事件回调反馈接口
    public AlguiCallback.Click getByteCallback() {
        return call;
    }
    //设置默认展开状态
    public AlguiViewFoldMenu setCatUnfold(boolean isUnfold) {
        if (isEnableUnfold) {
            this.isUnfold = isUnfold;

            if (isUnfold) {
                //展开则三角形位于上边
                if (triangleView != null) 
                    triangleView.setCatType(AlguiViewTriangle.Type.EQUILATERAL_TOP);
                //内部布局可见
                insideLayout.setVisibility(LinearLayout.VISIBLE);
            } else {
                //未展开则三角形位于左边
                if (triangleView != null) 
                    triangleView.setCatType(AlguiViewTriangle.Type.EQUILATERAL_LEFT);
                //内部布局隐藏
                insideLayout.setVisibility(LinearLayout.GONE);
            }

        }
        if (call != null)
            call.click(isUnfold);//回调
        return this;
    }

    //设置是否可以展开
    public AlguiViewFoldMenu setCatEnableUnfold(boolean isEnableUnfold) {
        this.isEnableUnfold = isEnableUnfold;
        return this;
    }


    /**
     * 获取标题
     * @return 标题
     */
    public AlguiViewItem getByteTitleLayout() {
        return title;
    }


    /**
     * 获取三角形视图
     * @return 三角形视图
     */
    public AlguiViewTriangle getByteTriangleView() {
        return triangleView;
    }



    /**
     * 获取内部展开布局
     * @return 内部展开布局
     */
    public AlguiFlowLayout getByteInsideLayout() {
        return insideLayout;
    }
    //获取所有行
    public ArrayList<AlguiLinearLayout> getByteLineList() {
        return insideLayout.getByteLineList();
    }
    //获取当前行 (最后一行)
    public AlguiLinearLayout getByteLine() {
        return insideLayout.getByteLine();
    }
    //获取指定行
    public AlguiLinearLayout getByteLine(int index) {
        return insideLayout.getByteLine(index);
    }



    public AlguiViewFoldMenu(Context c) {
        super(c);
        aContext = c;
        init();
    }
    public AlguiViewFoldMenu(Context c, CharSequence titleText) {
        this(c);
        setCatText(titleText);
    }

    //设置根大小
    public AlguiViewFoldMenu setCatSize(float w, float h) {
        super.setCatSize(w, h);
        return this;
    }

    //设置根权重
    public AlguiViewFoldMenu setCatWeight(float weight) {
        super.setCatWeight(weight);
        return this;
    }

    //设置内边距
    public AlguiViewFoldMenu setCatPadding(float left, float top, float right, float bottom) {
        insideLayout.setCatPadding(left, top,  right,  bottom);
        return this;
    }
    //设置根外边距
    public AlguiViewFoldMenu setCatMargins(float left, float top, float right, float bottom) {
        super.setCatMargins(left, top, right, bottom);
        return this;
    }
    //设置根背景颜色
    public AlguiViewFoldMenu setCatBackColor(int... backColor) {
        super.setCatBackColor(backColor);
        return this;
    }
    //设置根圆角半径
    public AlguiViewFoldMenu setCatRadiu(float r) {
        super.setCatRadiu(r);//圆角
        return this;
    }
    //设置根描边
    public AlguiViewFoldMenu setCatBorder(float size, int color) {
        super.setCatBorder(size, color);
        return this;
    }
    //设置标题背景颜色
    public AlguiViewFoldMenu setCatTitleBackColor(int... backColor) {
        title.setCatBackColor(backColor);
        return this;
    }
    //设置标题半径
    public AlguiViewFoldMenu setCatTitleRadiu(float r) {
        title.setCatRadiu(r);//圆角
        return this;
    }
    //设置标题描边
    public AlguiViewFoldMenu setCatTitleBorder(float size, int color) {
        title.setCatBorder(size, color);
        return this;
    }

    //设置图标 
    //支持：网络图像链接，base64图像编码，本地图像文件
    //格式：png，jpg，gif…
    //注意：设置后将覆盖默认的三角形 设置null为默认三角视图
    public AlguiViewFoldMenu setCatIcon(@Nullable String url_base64_filePath) {
        if (url_base64_filePath != null) {
            if (triangleView != null)
                if (title.indexOfChild(triangleView) > -1) {
                    title.remView(triangleView);
                }
            title.setCatIcon(url_base64_filePath);
        } else {
            //null则设置为三角视图
            title.setCatIcon(null);
            if (triangleView == null) {
                triangleView = new AlguiViewTriangle(aContext);
                triangleView.setCatColor(0xFFFFFFFF);//三角形颜色
                triangleView.setCatMargins(0,0,7,0);
                if (textSize != -1)
                    triangleView.setCatSize(textSize / 1.2f);//三角形大小
            }
            if (title.indexOfChild(triangleView) < 0)
                title.addView(triangleView, 0);//标题添加三角形
        }

        return this;
    }
    //设置三角形颜色
    /*public AlguiCollapse setCatAngleColor(int color) {
     triangleView.setColor(color);//颜色
     return this;
     }*/

    //设置图标圆角半径
    public AlguiViewFoldMenu setCatIconRadiu(float r) {
        title.setCatIconRadiu(r);
        return this;
    }

    //设置图标颜色(只能设置单色图像的颜色)
    public AlguiViewFoldMenu setCatIconColor(int color) {
        title.setCatIconColor(color);
        if (triangleView != null)
            triangleView.setCatColor(color);//颜色
        return this;
    }

    //设置图标透明度
    public AlguiViewFoldMenu setCatIconTransparent(int t) {
        title.setCatIconTransparent(t);
        return this;
    }

    //设置图标毛玻璃模糊 (不支持GIF动态图片模糊)
    public AlguiViewFoldMenu setCatIconBlur(int radius) {
        title.setCatIconBlur(radius);
        return this;
    }
    //设置标题文本
    public AlguiViewFoldMenu setCatText(CharSequence text, Object... args) {
        title.setCatText(text, args);
        return this;
    }
    //设置标题文本颜色
    public AlguiViewFoldMenu setCatTextColor(int... color) {
        title.setCatTextColor(color);
        return this;
    }
    //设置文本动态渐变效果启动状态
    public AlguiViewFoldMenu setCatTextMoveGrad(boolean b) {
        title.setCatTextMoveGrad(b);
        return this;
    }
    //设置标题文本大小
    public AlguiViewFoldMenu setCatTextSize(float size) {
        title.setCatTextSize(size);//标题大小
        if (triangleView != null)
            triangleView.setCatSize(size / 1.2f);//三角形大小
        textSize = size;
        return this;
    }
    //设置Assets文件夹字体文件作为标题文本字体
    public AlguiViewFoldMenu setCatTextTFAssets(String assetsTfFileName) {
        setCatTextTFAssets(assetsTfFileName, Typeface.NORMAL);
        return this;
    }//重载+样式
    public AlguiViewFoldMenu setCatTextTFAssets(String assetsTfFileName, int style) {
        title.setCatTextTFAssets(assetsTfFileName, style);
        return this;
    }

    //设置内部布局背景颜色
    public AlguiViewFoldMenu setCatInsideBackColor(int... backColor) {
        insideLayout.setCatBackColor(backColor);
        return this;
    }
    //设置内部布局半径
    public AlguiViewFoldMenu setCatInsideRadiu(float r) {
        insideLayout.setCatRadiu(r);//圆角
        return this;
    }
    //设置内部布局描边
    public AlguiViewFoldMenu setCatInsideBorder(float size, int color) {
        insideLayout.setCatBorder(size, color);
        return this;
    }
    //设置父布局
    public AlguiViewFoldMenu setCatParentLayout(ViewGroup vg) {
        if (vg != null)
            vg.addView(this);
        return this;
    }

    //内部流程布局添加视图 中途支持手动换行
    public AlguiViewFoldMenu addView(View... view) {
        insideLayout.addView(view);
        return this;
    }
    //设置所有行最大视图数量 自动换行
    public AlguiViewFoldMenu setCatLineMaxView(int num) {
        insideLayout.setCatLineMaxView(num);
        return this;
    }
    //设置对话框所有行的外边距
    public AlguiViewFoldMenu setCatLineMargins(float left, float top, float right, float bottom) {
        insideLayout.setCatLineMargins(left,top,right,bottom);
        return this;
    }
    //内部布局手动换行
    public AlguiLinearLayout endl() {
        return insideLayout.endl();
    }
    // 设置内容布局方向
    public AlguiViewFoldMenu setCatOrientation(int orientation) {
        if (insideLayout != null) {
            insideLayout.setCatOrientation(orientation);
        }
        return this;
    }

// 删除内容布局所有视图
    public AlguiViewFoldMenu remAllView() {
        if (insideLayout != null) {
            insideLayout.remAllView();
        }
        return this;
    }

// 删除内容布局当前末尾行的子视图
    public AlguiViewFoldMenu remView(View... view) {
        if (insideLayout != null) {
            insideLayout.remView(view);
        }
        return this;
    }

// 删除内容布局指定行的视图
    public AlguiViewFoldMenu remViewToLine(int index, View... views) {
        if (insideLayout != null) {
            insideLayout.remViewToLine(index, views);
        }
        return this;
    }

// 删除内容布局指定行 (按索引)
    public AlguiViewFoldMenu remLine(int... indexs) {
        if (insideLayout != null) {
            insideLayout.remLine(indexs);
        }
        return this;
    }

// 删除内容布局指定行 (按对象)
    public AlguiViewFoldMenu remLine(AlguiLinearLayout... objs) {
        if (insideLayout != null) {
            insideLayout.remLine(objs);
        }
        return this;
    }

// 添加视图到内容布局的指定行
    public AlguiViewFoldMenu addViewToLine(int index, View... views) {
        if (insideLayout != null) {
            insideLayout.addViewToLine(index, views);
        }
        return this;
    }



    //重写添加视图的函数 确保新视图添加到内部布局当中而不是折叠菜单根布局
    @Override
    public void addView(View child) {
        if (child != null)
            insideLayout.addView(child);
    }
    @Override
    public void addView(View child, int index) {
        if (child != null)
            insideLayout.addView(child, index);
    }
    @Override
    public void addView(View child, int width, int height) {
        if (child != null)
            insideLayout.addView(child, width, height);
    }
    //重写后内部往父类添加视图时必须使用此重载 index直接填-1即可
    //原因：其它重载在父类内部会间接最终调用到这个重载
    // 由于重写了所以父类会调用子类重写的重载
    // 那么往父类添加insideLayout时就会无限递归 所以内部必须使用此重装添加
    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        if (child != null)
            insideLayout.addView(child, index, params);
    }
    @Override
    public void addView(View child, ViewGroup.LayoutParams params) {
        if (child != null)
            insideLayout.addView(child, params);
    }



    //初始化
    private void init() {

        //根布局
        super.setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        super.setCatWeight(1);
        setVerticalGravity(16);
        setOrientation(LinearLayout.VERTICAL);//垂直
        super.setCatBackColor(0);

        //标题
        title = new AlguiViewItem(aContext);
        title.setCatText(TAG);
        title.setCatPadding(6, 3, 6, 3);
        setCatIcon(null);//添加三角视图
        
        setCatTextColor(0xFFFFFFFF);
        setCatTextSize(7);
        //内部展开布局
        insideLayout = new AlguiFlowLayout(aContext);
        // insideLayout.setVerticalGravity(16);
        insideLayout.setCatPadding(3, 2, 3, 2);
        //insideLayout.setOrientation(LinearLayout.VERTICAL);//垂直

        initClick();

        //所有addView被重写的原因 所以在内部父布局添加布局必须使用三个参数的重载 否则无限递归
        super.addView(title, -1, new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        super.addView(insideLayout, -1, new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        setCatUnfold(isUnfold);//默认展开状态
    }


}
