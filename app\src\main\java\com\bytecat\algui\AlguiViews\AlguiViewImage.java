package com.bytecat.algui.AlguiViews;
import androidx.annotation.Nullable;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.AnimatedImageDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.os.Message;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Toast;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiTools.AlguiToolImage;
import java.io.File;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/11/21 09:00
 * @Describe Algui图像视图
 */
public class AlguiViewImage extends ImageView {

    public static final String TAG = "AlguiViewImage";
    Context aContext;
    LinearLayout.LayoutParams params;//布局参数
    GradientDrawable gradientDrawable;//背景
    Drawable drawable;//原图
    Drawable baseImage_Blur;//模糊后的图像副本
    int blurR = -1;//毛玻璃模糊半径
    float psize = -1;//百分比大小
    int color=0;//图像颜色


    //点击事件回调反馈
    AlguiCallback.Click call;
    boolean isInitClick=false;//是否已经初始化点击事件
    boolean isChecked=false;
    //初始化内部点击事件
    private void initClick() {
        setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    callClick(!isChecked);
                }
            });
        isInitClick = true;
    }
    //设置点击事件回调反馈接口
    public AlguiViewImage setCatCallback(AlguiCallback.Click c) {
        if (c == null) {
            setOnClickListener(null);
            isInitClick = false;
        } else {
            call = c;
            if (!isInitClick) {
                initClick();
            }
        }
        return this;
    }


    //获取点击事件回调反馈接口
    public AlguiCallback.Click getByteCallback() {
        return call;
    }
    //代码执行点击事件
    public AlguiViewImage callClick(boolean b) {
        isChecked = b;
        if (call != null)
            call.click(isChecked);
        return this;
    }


    // Getter 和 Setter 方法

    // 获取布局参数
    public LinearLayout.LayoutParams getByteParams() {
        return params;
    }

    // 获取背景
    public GradientDrawable getByteBack() {
        return gradientDrawable;
    }

    // 获取图像
    public Drawable getByteDrawable() {
        return drawable;
    }

    // 设置图像
    public AlguiViewImage setCatDrawable(@Nullable Drawable d) {
        this.drawable = d;
        setImageDrawable(drawable);
        baseImage_Blur = null;
        if (drawable != null) {
            //自动继承上次大小
            if (psize != -1)
                setCatSize(psize);
            //自动继承上次的毛玻璃模糊
            if (blurR > 0)
                setCatBlur(blurR);
            //自动继承上次颜色
            if (color!=0)
                setCatColor(color);
            //对于gif则开始动画
            
            /*if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P)
            if (drawable instanceof AnimatedImageDrawable)
             ((AnimatedImageDrawable) drawable).start();*/
        } else {
            __def__();
        }

        return this;
    }


    private void __def__() {setCatImageBase64(AlguiAssets.Icon.image_def);}
    private void __deffail__() {setCatImageBase64(AlguiAssets.Icon.image_fail);}


    public AlguiViewImage(Context c) {
        super(c);
        aContext = c;
        init();
    }

    public AlguiViewImage(Context c, String SetImage_Url_Base64_FilePath) {
        this(c);
        setCatImage(SetImage_Url_Base64_FilePath);
    }

    private void init() {
        gradientDrawable = new GradientDrawable();
        setBackground(gradientDrawable);

        params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                                               LinearLayout.LayoutParams.WRAP_CONTENT);
        setLayoutParams(params);
        setClipToOutline(true);//根据父视图轮廓裁剪
        __def__();

    }



    //设置父布局
    public AlguiViewImage setCatParentLayout(ViewGroup vg) {
        if (vg != null)
            vg.addView(this);
        return this;
    }

    //设置外边距
    public AlguiViewImage setCatMargins(float left, float top, float right, float bottom) {
        params.setMargins(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }

    //设置权重
    public AlguiViewImage setCatWeight(float weight) {
        params.weight = weight;
        requestLayout();//重新计算布局
        return this;
    }
    //设置大小
    public AlguiViewImage setCatSize(float w, float h) {
        if ((int)w != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int) w != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)w != ViewGroup.LayoutParams.FILL_PARENT) {
            params.width = (int)dp2px(w);
        } else {
            params.width = (int)w;
        }

        if ((int)h != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int)h != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)h != ViewGroup.LayoutParams.FILL_PARENT) {
            params.height = (int)dp2px(h);
        } else {
            params.height = (int)h;
        }
        requestLayout();//重新计算布局
        return this;
    }


    //设置图像大小 (按原始图像尺寸设置百分比大小 1为原始尺寸 作为中心加减延伸)
    public AlguiViewImage setCatSize(float percent) {
        psize = percent;
        if (drawable != null) {
            //图像宽高
            int mw = drawable.getIntrinsicWidth();
            int mh = drawable.getIntrinsicHeight();

            // 根据百分比计算目标宽高
            int newWidth = (int) (mw * percent);
            int newHeight = (int) (mh * percent);

            params.width = newWidth;
            params.height = newHeight;
            requestLayout();//重新计算布局
        }
        return this;
    }
    //设置圆角半径
    public AlguiViewImage setCatRadiu(float r) {
        gradientDrawable.setCornerRadius(dp2px(r)); 
        return this;
    }

    
// 设置图像颜色(只能设置单色图像的颜色)
    public AlguiViewImage setCatColor(int color) {
        this.color = color;
        // 不支持动态图
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P)
        if (drawable instanceof AnimatedImageDrawable) {
            return this;
        }

        Bitmap bitmap = AlguiToolImage.drawableToBitmap(drawable);
        if (bitmap != null) {
            int w = bitmap.getWidth();
            int h = bitmap.getHeight();
            // 基准颜色
            int R = -1, G = -1, B = -1, A = -1;
            // 容忍度的平方
            int toleranceSquared = 50 * 50;

            // 获取所有像素数据
            int[] pixels = new int[w * h];
            bitmap.getPixels(pixels, 0, w, 0, 0, w, h);

            for (int i = 0; i < pixels.length; i++) {
                int pixelColor = pixels[i];
                int r = Color.red(pixelColor);
                int g = Color.green(pixelColor);
                int b = Color.blue(pixelColor);
                int a = Color.alpha(pixelColor);

                if (R == -1) {
                    // 获取第一个不透明颜色用于之后其他颜色对比
                    if (a > 0 && (r > 0 || g > 0 || b > 0)) {
                        R = r;
                        G = g;
                        B = b;
                        A = a;
                    }
                } else {
                    if (a > 0) {
                        // 计算颜色差异的平方和
                        int rDiff = R - r;
                        int gDiff = G - g;
                        int bDiff = B - b;
                        int colorDiffSquared = rDiff * rDiff + gDiff * gDiff + bDiff * bDiff;

                        // 如果颜色差异超过容忍度，说明是多彩图像
                        if (colorDiffSquared > toleranceSquared) {
                            setColorFilter(0);  // 多彩图像不做处理
                            return this;
                        }
                    }
                }
            }

            // 如果所有像素颜色相同或差异在容忍度范围内，说明是纯色图像
            setColorFilter(color);
        }

        return this;
    }
    
    
    
   


    //设置图片透明度
    public AlguiViewImage setCatTransparent(int t) {
        setAlpha(t);
        return this;
    }

    //设置毛玻璃模糊 (不支持GIF动态图片模糊)
    public AlguiViewImage setCatBlur(int radius) {
        blurR = radius;//保存模糊半径，下次改变图片时自动继承这个模糊半径
        //对当前图像进行模糊图像处理
        Drawable d  =AlguiToolImage.psImageBlur(drawable, radius, aContext);
        if (d != null) {
            //设置模糊后的图像
            setImageDrawable(d);
            baseImage_Blur = d;
        } else {
            __deffail__();
        }

        return this;
    }

    //设置图像
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiViewImage setCatImage(String Url_Base64_FilePath) {
        if (Url_Base64_FilePath != null) {
            //字符串开头是http或https则设置为网络图片
            if (Url_Base64_FilePath.startsWith("http://") || Url_Base64_FilePath.startsWith("https://")) {
                setCatImageURL(Url_Base64_FilePath);
                //对于base64图像检测长度
            } else if (Url_Base64_FilePath.length() > 32 + 16) {
                setCatImageBase64(Url_Base64_FilePath);
                //其它情况默认识别为本地图像文件
            } else {
                setCatImageFile(Url_Base64_FilePath);
            }
        } else {
            __def__();
        }
        return this;
    }

    //设置网络图像
    public AlguiViewImage setCatImageURL(final String url) {
        if (url != null) {
            AlguiToolImage.getImageURL(url, new AlguiCallback.Web(){
                    @Override
                    public void web(Message msg) {
                        switch (msg.what) {
                            case 200:
                                Object obj = msg.obj;
                                if (obj != null) {
                                    if (obj instanceof Drawable) {
                                        setCatDrawable((Drawable)msg.obj);
                                    }
                                }
                                break;
                            case 404:
                                __deffail__();
                                Toast.makeText(getContext(), "网络图片加载失败：服务器发生错误", Toast.LENGTH_SHORT).show();
                                break;
                            case 651:
                                __deffail__();
                                Toast.makeText(getContext(), "网络图片加载失败：图片异常", Toast.LENGTH_SHORT).show();
                                break;
                        }
                    }
                }
            );
        } else {
            __def__();
        }

        return this;
    }


    //设置本地图像
    //1.可以传入 本地文件路径 例如：/data/user/0/com.bytecat.algui/cache/image.png
    //注意：本地文件路径别人手机上没有这个图片，所以这只针对于从网络下载图像到本地，然后引用此下载好的图像
    //2.可以传入 图像文件名 自动在Assets文件夹下搜索此图像文件
    //3.可以传入 项目根目录Assets路径 例如：/assets/image.png
    public AlguiViewImage setCatImageFile(String filePath) {
        if (filePath != null) {
            Drawable d=AlguiToolImage.getImageFile(filePath, aContext);
            if (d != null) {
                setCatDrawable(d);
            } else {
                __deffail__();
            }
        } else {
            __def__();
        }
        return this;
    }


    //设置Base64图像
    //对Base64长度有限制，太大可能出现异常
    public AlguiViewImage setCatImageBase64(String base64String) {
        if (base64String != null) {
            Drawable d=AlguiToolImage.getImageBase64(base64String);
            if (d != null) {
                setCatDrawable(d);
            } else {
                __deffail__();
            }
        } else {
            __def__();
        }
        return this;
    }




    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }

 

    //下载图像到本地 传入路径 例/storage/emulated/0/myapp/images/image.png
    //如果单传文件名或无法访问的目录将默认下载到相册
    public File downloadImage(String path) {
        if (drawable == null || path == null) {
            return null;
        }
        //如果已经模糊了则保存模糊后的图像副本 否则原图
        return AlguiToolImage.saveDrawableToFile(aContext, baseImage_Blur != null ?baseImage_Blur: drawable, path);
    }














}
