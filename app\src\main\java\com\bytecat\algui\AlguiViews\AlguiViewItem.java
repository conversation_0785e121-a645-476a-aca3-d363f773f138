package com.bytecat.algui.AlguiViews;
import androidx.annotation.Nullable;
import android.content.Context;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import java.util.ArrayList;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/12 15:21
 * @Describe Algui 选项按钮
 */
public class AlguiViewItem extends AlguiLinearLayout {

    public static final String TAG = "AlguiViewItem";
    Context aContext;
    int id;//标识符ID
    public AlguiViewItem setCatId(int id) {this.id = id;return this;}//设置标识符ID
    public int getByteId() {return id;}//获取标识符ID

    AlguiViewImage icon;//图标
    AlguiViewText text;//文本
    AlguiLinearLayout itemLayout;//选项布局
    float iconSize=-1;//图标大小
    int[] backColors,textColors;//背景和文本颜色
    float strokeSize=-1;//描边大小
    int strokeColor=-1;//描边颜色
    boolean isInside;//开始内部更新

    //全局
    private static final ArrayList<AlguiViewItem> ItemList = new ArrayList<>();//所有项列表
    private static AlguiViewItem selectItem;//当前选中的项
    private static int[] clickBackColors={0xFF397CCD};//项点击后的背景颜色
    private static int[] clickTextColors={0xFFFFFFFF};//项点击后的文本颜色
    private static float clickStrokeSize;//项点击后的描边大小
    private static int clickStrokeColor;//项点击后的描边颜色
    //设置项选中后的背景颜色
    public static void setCatSelectBackColor(int... colors) {
        clickBackColors = colors;
    }
    //设置项选中后的文本颜色
    public static void setCatSelectTextColor(int... colors) {
        clickTextColors = colors;
    }
    //设置项选中后的描边
    public static void setCatSelectBorderColor(float size, int color) {
        clickStrokeSize = size;
        clickStrokeColor = color;
    }
    //设置选中项
    public static void setCatSelectItem(AlguiViewItem item) {
        if (item != null)
            item.select();
    }
    public static int[] getByteClickBackColors() { return clickBackColors; }
    public static int[] getByteClickTextColors() { return clickTextColors; }
    public static float getByteClickStrokeSize() { return clickStrokeSize; }
    public static int getByteClickStrokeColor() { return clickStrokeColor; }

    // 获取全局所有项对象的列表
    public static ArrayList<AlguiViewItem> getByteStaticItemList() { return ItemList; }
    // 获取当前选中的项
    public static AlguiViewItem getByteSelectItem() { return selectItem; }

    //选中
    public AlguiViewItem select() {
        selectItem = this;//当前选中项为自身
        //存在点击样式则还原其它项的样式
        if ((clickBackColors != null&&clickBackColors.length>0) ||
            (clickTextColors != null&&clickTextColors.length>0) ||
            (clickStrokeSize > 0 && clickStrokeColor > 0)
            ) {
            for (final AlguiViewItem u:ItemList) {
                if (u != null) {
                    int[] backColors = u.getByteBackColors();
                    int[] textColors=u.getByteTextColors();
                    float strokeSize = u.getByteBorderSize();
                    int strokeColor = u.getByteBorderColor();
                    if (backColors != null)
                        u.setCatBackColor(backColors);
                    if (textColors != null&&textColors.length>0) {
                        
                        //恢复文本选项的颜色
                        for (Item i:u.getByteItems()) {
                            if (i != null) {
                                View v = i.view;
                                if (v != null) {
                                    if (v instanceof AlguiViewText) {
                                        AlguiViewText aText=(AlguiViewText)v;
                                        aText.setCatTextColor(aText.getByteStyleTextColos());
                                    } 
                                    if(v instanceof AlguiViewImage)    
                                    {
                                        AlguiViewImage img=(AlguiViewImage)v;
                                        img.setCatColor(textColors[0]);
                                    }
                                }
                            }
                        }
                        u.setCatTextColor(textColors);
                        u.setCatIconColor(textColors[0]);
                    }

                    if (strokeColor != -1 || strokeSize != -1)
                        u.setCatBorder(strokeSize, strokeColor);
                }
            }
        }
        isInside = true;//开始内部更新
        //设置当前项的点击样式 (这里不要使用给外部的方法)
        if (clickBackColors != null&&clickTextColors.length>0)
            setCatBackColor(clickBackColors);
        if (clickTextColors != null&&clickTextColors.length>0) {
            //设置选项的颜色
            for (Item i:items) {
                if (i != null) {
                    View v = i.view;
                    if (v != null) {
                        if (v instanceof AlguiViewText) {
                            AlguiViewText aText=(AlguiViewText)v;
                            aText.setTemStyle(true);//开始临时修改样式
                            aText.setCatTextColor(clickTextColors);
                            aText.setTemStyle(false);//结束临时修改样式
                        }
                        if(v instanceof AlguiViewImage&&clickTextColors.length>0)    
                        {
                            AlguiViewImage img=(AlguiViewImage)v;
                            img.setCatColor(clickTextColors[0]);
                        }
                    }
                }
            }
            setCatTextColor(clickTextColors);
            setCatIconColor(clickTextColors[0]);
        }
        if (clickStrokeSize > 0 && clickStrokeColor > 0)
            setCatBorder(clickStrokeSize, clickStrokeColor);
        isInside = false;//结束内部更新
        return this;
    }


    // 对于自定义拓展
    // 获取背景颜色
    public int[] getByteBackColors() { return backColors; }
    // 获取文本颜色
    public int[] getByteTextColors() { return textColors; }
    // 获取描边大小
    public float getByteBorderSize() { return strokeSize; }
    // 获取描边颜色
    public int getByteBorderColor() { return strokeColor; }
    // 获取图标
    public AlguiViewImage getByteIcon() {return icon;}
    // 获取文本
    public AlguiViewText getByteText() {return text;}
    // 获取选项布局
    public AlguiLinearLayout getByteItemLayout() { return itemLayout; }
    // 获取全局选项列表
    public ArrayList<Item>  getByteItems() { return items; }


    //选项结构
    public class Item {
        int id;
        View view;
        Item(int id, View view) {
            this.id = id;
            this.view = view;
        }
    }
    //添加选项
    public AlguiViewItem addItem(int id, View view) {
        if (view != null) {
            if (view instanceof AlguiViewText) {
                //如果是文本默认跟随标题
                AlguiViewText o=(AlguiViewText) view;
                o.setCatTextSize(text.getByteStyleTextSize());
                o.setCatTextColor(text.getByteStyleTextColos());
            }
            items.add(new Item(id, view));
            //第一次添加选项默认选中第一个选项
            if (selectItemID == -335348460) {
                selectItemID = id;
                setCatSwitchItem(selectItemID);
            }
        }
        return this;
    }
    //添加一些选项 id根据添加顺序自动赋值 0开始
    public AlguiViewItem addItem(View... view) {
        int i=0;
        for(View v:view){
            if(v!=null){
                addItem(i,v);
                i++;
            }
        }
        return this;
    }
    //切换选项监听
    ArrayList<Item> items = new ArrayList<>();//选项列表
    int selectItemID=-335348460;//当前选中选项ID
    boolean isEnableSwitch=true;// 是否启用切换选项功能
    AlguiCallback.Item call;
    boolean isInitClick=false;//是否已经初始化点击事件
    //初始化内部点击事件
    private void initClick() {
        setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    select();//更新选中样式
                    if (isEnableSwitch) {
                        //查找下一个选项
                        for (int i = 0; i < items.size(); i++) {
                            Item item = items.get(i); 
                            //找到当前选项
                            if (item.id == selectItemID) {
                                //将当前选项改为下一个选项
                                int nextIndex = i + 1;
                                //如果是最后一个选项则为第一个选项
                                if (nextIndex >= items.size()) {
                                    nextIndex = 0;
                                }
                                selectItemID = items.get(nextIndex).id;
                                break;
                            }

                        }
                        setCatSwitchItem(selectItemID);//设置切换到下一个选项

                    }

                }
            });
        isInitClick = true;
    }
    //设置选项事件回调反馈接口
    public AlguiViewItem setCatCallback(AlguiCallback.Item c) {
        if (c == null) {
            setOnClickListener(null);
            isInitClick = false;
        } else {
            call = c;
            if (!isInitClick) {
                initClick();
            }
        }
        return this;
    }
    //获取选项事件回调反馈接口
    public AlguiCallback.Item getByteCallback() {
        return call;
    }
    //设置切换选项不回调
    public AlguiViewItem setCatSwitchItemNoCall(int selectItemID) {
        View v=null;
        for (Item i:items) {
            if (i != null) {
                if (i.id == selectItemID) {
                    v = i.view;
                    break;
                }
            }
        }
        if (v != null) {
            this.selectItemID = selectItemID;
            itemLayout.remAllView();
            itemLayout.addView(v);
        }
        return this;
    }
    //设置切换选项
    public AlguiViewItem setCatSwitchItem(int selectItemID) {
        setCatSwitchItemNoCall(selectItemID);
        if (call != null)
            call.item(selectItemID);//回调切换选项
        return this;
    }
    //设置是否启用切换
    public AlguiViewItem setCatEnableSwitch(boolean isEnableSwitch) {
        this.isEnableSwitch = isEnableSwitch;
        return this;
    }




    //&继承父类方法链
    // 设置大小
    public AlguiViewItem setCatSize(float w, float h) {
        super.setCatSize(w, h); // 调用父类的方法
        return this;
    }

    // 设置权重
    public AlguiViewItem setCatWeight(float weight) {
        super.setCatWeight(weight); // 调用父类的方法
        return this;
    }

    // 设置内边距
    public AlguiViewItem setCatPadding(float left, float top, float right, float bottom) {
        super.setCatPadding(left, top, right, bottom); // 调用父类的方法
        return this;
    }

    // 设置外边距
    public AlguiViewItem setCatMargins(float left, float top, float right, float bottom) {
        super.setCatMargins(left, top, right, bottom); // 调用父类的方法
        return this;
    }

    // 设置背景颜色
    public AlguiViewItem setCatBackColor(int... backColor) {
        if (!isInside)
            backColors = backColor;
        super.setCatBackColor(backColor); // 调用父类的方法
        return this;
    }

    // 设置圆角半径
    public AlguiViewItem setCatRadiu(float radiu) {
        super.setCatRadiu(radiu); // 调用父类的方法
        return this;
    }

    // 设置描边
    public AlguiViewItem setCatBorder(float borderSize, int borderColor) {
        if (!isInside) {
            strokeSize = borderSize;
            strokeColor = borderColor;
        }
        super.setCatBorder(borderSize, borderColor); // 调用父类的方法
        return this;
    }

    // 设置父布局
    public AlguiViewItem setCatParentLayout(ViewGroup vg) {
        super.setCatParentLayout(vg); // 调用父类的方法
        return this;
    }

    //设置文本
    public AlguiViewItem setCatText(CharSequence textstr, Object... args) {
        this.text.setCatText(textstr, args);
        return this;
    }

    //设置文本颜色
    public AlguiViewItem setCatTextColor(int... color) {
        if (!isInside)
            textColors = color;
        text.setCatTextColor(color);
        for(Item i:items){
            if(i==null)
                continue;
            View iv=i.view;
            if(iv==null)
                continue;
            if (iv instanceof AlguiViewText) {
                //如果是文本跟随标题
                AlguiViewText o=(AlguiViewText) iv;
               
                o.setCatTextColor(color);
            }
        }
        
        return this;
    }
    //设置文本动态渐变效果启动状态
    public AlguiViewItem setCatTextMoveGrad(boolean b) {
        text.setCatTextMoveGrad(b);
        return this;
    }
    //设置文本发光
    public AlguiViewItem setCatTextGlow(float radius, int color) {
        text.setCatTextGlow(radius, color);
        return this;
    }

    //设置文本字体 (Assets文件夹下的字体文件名)
    public AlguiViewItem setCatTextTFAssets(String assetsTfFileName) {
        text.setCatTextTFAssets(assetsTfFileName, Typeface.NORMAL);
        return this;
    }//重载+样式
    public AlguiViewItem setCatTextTFAssets(String assetsTfFileName, int style) {
        text.setCatTextTFAssets(assetsTfFileName, style);
        return this;
    }
    //设置文本大小
    public AlguiViewItem setCatTextSize(float size) {
        text.setCatTextSize(size);//设置文本大小
        iconSize = size * 1.3f;//图标是文本的0.3倍
        if (icon != null)
            icon.setCatSize(iconSize, iconSize);
        for(Item i:items){
            if(i==null)
                continue;
            View iv=i.view;
            if(iv==null)
                continue;
            if (iv instanceof AlguiViewText) {
                //如果是文本跟随标题
                AlguiViewText o=(AlguiViewText) iv;
                o.setCatTextSize(size);
            }
        }
        return this;
    }
    //设置图标
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiViewItem setCatIcon(@Nullable String Url_Base64_FilePath) {
        if (Url_Base64_FilePath != null) {
            if (icon == null) {
                icon = new AlguiViewImage(aContext);
                icon.setCatMargins(0, 0, 5, 0);
                if (iconSize != -1)
                    icon.setCatSize(iconSize, iconSize);
            }
            icon.setCatImage(Url_Base64_FilePath);

            if (indexOfChild(icon) < 0)
                addView(icon, 0);
        } else {
            //删除图标
            if (icon != null)
                if (indexOfChild(icon) > -1) {
                    remView(icon);
                }
        }
        return this;
    }
    //设置图标颜色 (只支持单色图标)
    public AlguiViewItem setCatIconColor(int color) {
        if (icon != null)
            icon.setCatColor(color);
        return this;
    }

    //设置图标圆角半径
    public AlguiViewItem setCatIconRadiu(float r) {
        if (icon != null)
            icon.setCatRadiu(r); 
        return this;
    }


    //设置图标透明度
    public AlguiViewItem setCatIconTransparent(int t) {
        if (icon != null)
            icon.setCatTransparent(t);
        return this;
    }

    //设置图标毛玻璃模糊 (不支持GIF动态图片模糊)
    public AlguiViewItem setCatIconBlur(int radius) {
        if (icon != null)
            icon.setCatBlur(radius);
        return this;
    }


    public AlguiViewItem(Context context) {
        super(context);
        aContext = context;
        init();
        ItemList.add(this);//将当前项添加到全局列表
    }
    public AlguiViewItem(Context context, CharSequence text) {
        this(context);
        setCatText(text);
    }

    private void init() {
        //布局
        setCatSize(AlguiFrameLayout.LayoutParams.MATCH_PARENT, AlguiFrameLayout.LayoutParams.WRAP_CONTENT);
        setCatWeight(1);
        setCatBackColor(0xFF233E5D);
        setOrientation(LinearLayout.HORIZONTAL);//横向
        setGravity(Gravity.CENTER_VERTICAL);//垂直居中
        setCatPadding(5, 3, 5, 3);

        //文本
        text = new AlguiViewText(aContext)
            //.setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT)
            //.setCatWeight(1)
            .setCatText(TAG)
            ;

        setCatTextColor(0xFFFFFFFF);
        setCatTextSize(7);

        //选项布局
        itemLayout = new AlguiLinearLayout(aContext)
            .setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT)
            .setCatWeight(1);
        itemLayout.setGravity(Gravity.CENTER_VERTICAL | Gravity.END);


        addView(text);
        addView(itemLayout);
        initClick();//初始化点击事件



    }
}
