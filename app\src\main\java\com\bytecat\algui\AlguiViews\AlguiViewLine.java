package com.bytecat.algui.AlguiViews;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.DashPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.Shader;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/11/09 19:50
 * @Describe Algui线条视图
 */
public class AlguiViewLine extends View {

    public static final String TAG = "AlguiViewLine";

    //线条样式
    int lineStyle = Style.STYLE_SOLID;   // 默认实线
    public static class Style{
        public static final int STYLE_SOLID = 0;    //实线
        public static final int STYLE_DASHED = 1;   //虚线
        public static final int STYLE_DOTTED = 2;   //点线
        public static final int STYLE_WAVE= 3;   //波浪线
    }
    
    Context aContext;
    LinearLayout.LayoutParams params;//布局参数

    Paint mPaint; //画笔
    Path path;//路径
    int colors[]=null;//线条渐变颜色
    float size;//线条大小

    /**
     * 获取布局参数。
     * 通过该方法可以获取控件的布局参数，进而对控件的布局属性进行设置。
     *
     * @return 当前的布局参数 (LinearLayout.LayoutParams)
     */
    public LinearLayout.LayoutParams getByteParams() {
        return params;
    }

   

    /**
     * 获取画笔对象。
     * 该方法返回一个 Paint 对象，允许你获取和修改绘图样式、颜色等。
     *
     * @return 当前的 Paint 对象
     */
    public Paint getBytePaint() {
        return mPaint;
    }

  
    /**
     * 获取路径对象。
     * 该方法返回一个 Path 对象，用于绘制复杂的路径或线条。
     *
     * @return 当前的 Path 对象
     */
    public Path getBytePath() {
        return path;
    }

    
    

    public AlguiViewLine(Context context) {
        super(context);
        aContext = context;
        init();
    }


    //设置视图权重
    public AlguiViewLine setCatWeight(float weight) {
        params.weight = weight;
        requestLayout();//重新计算布局
        return this;
    }

    //设置视图外边距
    public AlguiViewLine setCatMargins(float left, float top, float right, float bottom) {
        params.setMargins(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }

    //设置线条样式
    public AlguiViewLine setCatStyle(int style) {
        lineStyle = style;
        PathEffect effect = null;
        switch (lineStyle) {
                //实线
            default:
            case Style.STYLE_SOLID:
                effect = null;
                break;
                //虚线
            case Style.STYLE_DASHED:
                effect = new DashPathEffect(new float[]{10, 5}, 0);
                break;
                //点线
            case Style.STYLE_DOTTED:
                effect = new DashPathEffect(new float[]{2, 10}, 0);
                break;
                //波浪线
            case Style.STYLE_WAVE:
                update();
                return this;
        }
        mPaint.setPathEffect(effect);
        update();
        return this;
    }

    //设置线条颜色
    public AlguiViewLine setCatColor(int... color) {
        if (color.length == 1) {
            //单个颜色
            mPaint.setColor(color[0]);
        } else if (color.length > 1) {
            //多颜色使用渐变
            colors = color;
            invalidate();
        }

        return this;
    }

    //设置线条大小
    public AlguiViewLine setCatSize(float width) {
        size = (int)dp2px(width);
        mPaint.setStrokeWidth(size);
        update();
        return this;
    }

    //设置父布局
    public AlguiViewLine setCatParentLayout(ViewGroup vg) {
        if(vg!=null)
        vg.addView(this);
        return this;
    }

    //更新
    public AlguiViewLine update() {
        //宽度拉满，高度为线条大小
        params.width = LinearLayout.LayoutParams.MATCH_PARENT;
        params.height = lineStyle == Style.STYLE_WAVE ?(int)size * 10: (int)size - 1;
        requestLayout();//重新计算布局
        invalidate();//重新绘制
        return this;
    }

    private void init() {
        params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                                               LinearLayout.LayoutParams.MATCH_PARENT, 1);
        setLayoutParams(params);

        setClipToOutline(true);//根据父视图轮廓裁剪

        path = new Path();

        mPaint = new Paint();
        mPaint.setAntiAlias(true);  //启用抗锯齿
        mPaint.setStyle(Paint.Style.STROKE);  //绘制模式

        setCatSize(1);//默认线大小
        setCatStyle(Style.STYLE_SOLID);//默认实线
        setCatColor(0xFF42424C);//默认颜色
        setCatWeight(1);//权重
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int width=getWidth();
        int height=getHeight();
        //存在渐变色时初始化渐变色
        if (colors != null) {
            if (mPaint.getShader() == null) {
                Shader shader = new LinearGradient(0, 0, width, 0, colors, null, Shader.TileMode.MIRROR);
                mPaint.setShader(shader);
            }
        }

        path.reset();//重置路径
        path.moveTo(0, height / 2);  //起点
        if (lineStyle == Style.STYLE_WAVE) {

            //正弦波函数
            for (int i = 0; i < width; i++) {
                float y = (float) (height / 2 + Math.sin(i * 
                                                         0.1//波浪频率
                                                         ) * 
                    height / 3 //波浪振幅
                    ); 
                path.lineTo(i, y);
            }
        } else {
            path.lineTo(width, height / 2);//结束

        }


        //绘制路径
        canvas.drawPath(path, mPaint);

    }


    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }




}
