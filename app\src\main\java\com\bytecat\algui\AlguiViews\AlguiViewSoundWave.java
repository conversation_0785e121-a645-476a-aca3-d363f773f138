package com.bytecat.algui.AlguiViews;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/10/28 09:33
 * @Describe Algui可视化声波监测视图
 */
import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Shader;
import android.graphics.drawable.GradientDrawable;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolPermission;
import java.util.Arrays;

public class AlguiViewSoundWave extends View {

    public static final String TAG = "AlguiViewSoundWave";

    //声波颜色样式
    int colors[]=null;//渐变颜色
    int colorStyle=StyleColor.STYLE_COLOR_CUSTOMIZE;//默认颜色样式
    public static class StyleColor {
        public static final int STYLE_COLOR_CUSTOMIZE=0;//自定义
        public static final int STYLE_COLOR_AMP=1;//每个线条根据振幅小绿中黄大红动态变换
        public static final int STYLE_COLOR_AMP_ALL=2;//所有线条根据振幅小绿中黄大红动态变换
    }

    //声波样式
    int style = Style.STYLE_LINE_DENSE;//默认声波样式
    public static class Style {
        public static final int STYLE_LINE_DENSE=0;//密集线 (完整的声纹 拥有丰富的视觉效果)
        public static final int STYLE_LINE_INTERVAL=1;//间隔线 (半完整的声纹 会丢失一些声波数据)
        public static final int STYLE_LINE_PARTICE=2;//粒子线  (半完整的声纹 会丢失一些声波数据)
        public static final int STYLE_LINE_WAVE=3;//波浪线  (半完整的声纹 会丢失一些声波数据)
    }


    Context aContext;
    Activity aActivity;

    LinearLayout.LayoutParams params;//布局参数
    GradientDrawable gradientDrawable;//背景
    Paint paint; // 用于绘制波形
    double[] waveform; // 存储音频波形数据的数组
    double loudness;//声音响度
    AudioRecord audioRecord; // 用于录音
    Thread recordingThread; // 录音线程
    int bufferSize; // 缓冲区大小
    boolean isRecording; // 当前是否正在录音
    float sensitivity=5;//声波灵敏度

    int tBorderSize;//描边大小

    //Getter Setter

    /** 
     * 获取布局参数
     * @return LinearLayout.LayoutParams 当前的布局参数
     */
    public LinearLayout.LayoutParams getByteParams() {
        return params;
    }



    /** 
     * 获取背景绘制对象
     * @return GradientDrawable 当前的背景绘制对象
     */
    public GradientDrawable getByteBack() {
        return gradientDrawable;
    }


    /** 
     * 获取用于绘制波形的画笔
     * @return Paint 当前的绘制画笔
     */
    public Paint getBytePaint() {
        return paint;
    }



    /** 
     * 获取音频波形数据
     * @return double[] 当前的音频波形数据数组
     */
    public double[] getByteWaveform() {
        return waveform;
    }



    /** 
     * 获取当前响度值
     * @return double 当前的响度值
     */
    public double getByteLoudness() {
        return loudness;
    }


    /** 
     * 获取 AudioRecord 对象
     * @return AudioRecord 当前的录音对象
     */
    public AudioRecord getByteAudioRecord() {
        return audioRecord;
    }


    /** 
     * 获取录音线程
     * @return Thread 当前的录音线程
     */
    public Thread getByteRecordingThread() {
        return recordingThread;
    }



    /** 
     * 获取缓冲区大小
     * @return int 当前的缓冲区大小
     */
    public int getByteBufferSize() {
        return bufferSize;
    }



    /** 
     * 获取当前录音状态
     * @return boolean 当前是否正在录音
     */
    public boolean isRecording() {
        return isRecording;
    }



    /** 
     * 获取声波灵敏度
     * @return float 当前的声波灵敏度
     */
    public float getByteSensitivity() {
        return sensitivity;
    }





    public AlguiViewSoundWave(Context context, Activity activity) {
        super(context);
        aContext = context;
        aActivity = activity;
        init();
    }

    private void init() {
        params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                                               LinearLayout.LayoutParams.MATCH_PARENT, 1);
        setLayoutParams(params);
        gradientDrawable = new GradientDrawable();
        gradientDrawable.setColor(0xFF20324D);

        setBackground(gradientDrawable);
        paint = new Paint();
        paint.setColor(0xC200FF00);//声波颜色
        waveform = new double[0]; //初始波形数组
        isRecording = false; //未录音
        setClipToOutline(true);//根据父视图轮廓裁剪
        setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, 30);
        setCatWeight(1);
       
    }


    //设置大小
    public AlguiViewSoundWave setCatSize(float w, float h) {
        if ((int)w != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int) w != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)w != ViewGroup.LayoutParams.FILL_PARENT) {
            params.width = (int)dp2px(w);
        } else {
            params.width = (int)w;
        }

        if ((int)h != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int)h != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)h != ViewGroup.LayoutParams.FILL_PARENT) {
            params.height = (int)dp2px(h);
        } else {
            params.height = (int)h;
        }
        requestLayout();//重新计算布局
        return this;
    }

    //设置权重
    public AlguiViewSoundWave setCatWeight(float weight) {
        params.weight = weight;
        requestLayout();//重新计算布局
        return this;
    }

    //设置内边距
    public AlguiViewSoundWave setCatPadding(float left, float top, float right, float bottom) {
        setPadding(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }
    //设置外边距
    public AlguiViewSoundWave setCatMargins(float left, float top, float right, float bottom) {
        params.setMargins(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }
    //设置背景颜色
    public AlguiViewSoundWave setCatBackColor(int... backColor) {
        if (backColor.length == 1) {
            //单个颜色
            gradientDrawable.setColor(backColor[0]);
        } else if (backColor.length > 1) {
            //多个颜色，使用渐变
            gradientDrawable.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT); //渐变方向
            //最后一个元素如果是渐变类型则应用否则默认线性渐变
            if (isGdType(backColor[backColor.length - 1])) {
                gradientDrawable.setGradientType(backColor[backColor.length - 1]);
                int[] newArray = Arrays.copyOf(backColor, backColor.length - 1);//删除最后一个元素
                gradientDrawable.setColors(newArray);//设置颜色
            } else {
                gradientDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                gradientDrawable.setColors(backColor);//设置颜色
            }
        }
        return this;
    }
    //设置圆角半径
    public AlguiViewSoundWave setCatRadiu(float r) {
        gradientDrawable.setCornerRadius(dp2px(r));//圆角
        return this;
    }
    //设置描边
    public AlguiViewSoundWave setCatBorder(float size, int color) {
        int bs = (int)dp2px(size);//新的描边
        gradientDrawable.setStroke(bs, color);
        int w=bs - tBorderSize;//计算内边距差值，新描边-旧描边=差值
        this.setPadding(getPaddingLeft() + w, getPaddingTop() + w, getPaddingRight() + w, getPaddingBottom() + w);//设置内边距为描边宽度防止描边覆盖子视图
        tBorderSize = bs;
        return this;

    }

    //设置声波灵敏度
    public AlguiViewSoundWave setCatSensitivity(float s) {
        sensitivity = s;
        invalidate();
        return this;
    }
    //设置声波样式
    public AlguiViewSoundWave setCatStyle(int type) {
        style = type;
        invalidate();
        return this;
    }
    //设置声波颜色样式
    public AlguiViewSoundWave setCatColorStyle(int type) {
        colorStyle = type;
        invalidate();
        return this;
    }

    //设置声波颜色
    public AlguiViewSoundWave setCatColor(int... color) {
        //只有颜色样式为自定义时才设置
        if (colorStyle == StyleColor.STYLE_COLOR_CUSTOMIZE) {
            if (color.length == 1) {
                //单个颜色
                paint.setColor(color[0]);
            } else if (color.length > 1) {
                //多个颜色，使用渐变
                colors = color;
                invalidate();
            }

        }
        return this;
    }

    //设置声波发光效果 (这可能会掉帧)
    public AlguiViewSoundWave setCatGlow(float radius, int color) {
        paint.setShadowLayer(radius, 0, 0, color);
        return this;
    }
    //设置父布局
    public AlguiViewSoundWave setCatParentLayout(ViewGroup vg) {
        if (vg != null)
            vg.addView(this);
        return this;
    }

    //开始录音
    public AlguiViewSoundWave startRecording() {
        if (!isRecording) {
            //申请录音权限
            AlguiToolPermission.getPermission(aActivity, Manifest.permission.RECORD_AUDIO, new AlguiToolPermission.PermissionCallback() {
                    @Override
                    public void run(boolean consequence) {
                        if (consequence) {
                            AlguiLog.d(TAG,"授予了录音权限");
                            int source = MediaRecorder.AudioSource.MIC; //录音源
                            int samplingRate = 44100; //采样率
                            int channel = AudioFormat.CHANNEL_IN_MONO; //通道
                            int format = AudioFormat.ENCODING_PCM_16BIT; //编码格式

                            bufferSize = AudioRecord.getMinBufferSize(samplingRate, channel, format);

                            audioRecord = new AudioRecord(source, samplingRate, channel, format, bufferSize);
                            audioRecord.startRecording(); //开始录音
                            isRecording = true; //正在录音
                            recordingThread = new Thread(new Runnable() {
                                    @Override
                                    public void run() {
                                        short[] buffer = new short[bufferSize]; //短整型缓冲区
                                        while (isRecording) {
                                            int readSize = audioRecord.read(buffer, 0, buffer.length);
                                            if (readSize > 0) {
                                                double[] normalizedData = new double[readSize]; //归一化数据数组

                                                double sum = 0.0;
                                                for (int i = 0; i < readSize; i++) {
                                                    normalizedData[i] = buffer[i] / 32768.0; //归一化
                                                    sum += normalizedData[i] * normalizedData[i]; //计算平方和
                                                }

                                                //计算均方根（RMS）
                                                double rms = Math.sqrt(sum / readSize);

                                                waveform = normalizedData; //更新波形数据
                                                loudness = rms; //更新响度
                                                postInvalidate(); //重绘
                                            }
                                        }
                                    }
                                });

                            recordingThread.start();
                        } else {
                            AlguiLog.d(TAG,"未授予录音权限");
                        }
                    }
                });
            
        }
        return this;
    }

    //停止录音
    public AlguiViewSoundWave stopRecording() {
        if (isRecording) {
            isRecording = false;
            audioRecord.stop();
            audioRecord.release();
            audioRecord = null;
            recordingThread = null;
            invalidate(); // 请求重绘
        }
        return this;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        float width = getWidth();
        float height = getHeight();
        float halfHeight=height / 2;//高度中间
        if (colors != null) {
            if (paint.getShader() == null) {
                Shader shader = new LinearGradient(0, 0, 0, height, colors, null, Shader.TileMode.CLAMP); 
                paint.setShader(shader);
            }
        }
        if (!isRecording) {
            canvas.drawLine(0, halfHeight, width, halfHeight, paint);//起始xy 结束xy
        }
        //绘制声音波纹
        if (waveform.length > 0) {
            //调试rms
            //paint.setColor(Color.WHITE);
            //canvas.drawText("rms "+loudness, 2, 15, paint);

            if (colorStyle == StyleColor.STYLE_COLOR_AMP_ALL) {
                if (loudness < 0.02) {
                    paint.setColor(Color.GREEN); //小
                } else if (loudness < 0.04) {
                    paint.setColor(Color.YELLOW); //中
                } else {
                    paint.setColor(Color.RED); //大
                }
            }


            for (int i = 0; i < waveform.length; i++) {
                float x = ((float) i / waveform.length * width);//根据每个波点索引依次增加x 最后+乘数代表每条线x间隔
                float y = halfHeight - (float) (waveform[i] * halfHeight) * sensitivity;//高度从中间开始减去波纹大小为结束点

                if (colorStyle == StyleColor.STYLE_COLOR_AMP) {
                    if (y > halfHeight / 2) {
                        paint.setColor(Color.GREEN); //小
                    } else if (y > halfHeight / 3) {
                        paint.setColor(Color.YELLOW); //中
                    } else {
                        paint.setColor(Color.RED); //大
                    }
                }
                switch (style) {
                    case Style.STYLE_LINE_DENSE://密集
                        canvas.drawLine(x, height / 2, x, y, paint);//起始xy 结束xy
                        break;
                    case Style.STYLE_LINE_INTERVAL://间隔
                        x *= 50;
                        canvas.drawLine(x, height / 2, x, y, paint);//起始xy 结束xy
                        break;
                    case Style.STYLE_LINE_PARTICE://粒子
                        x *= 50;
                        canvas.drawCircle(x, y, 1, paint); //圆
                        break;
                    case Style.STYLE_LINE_WAVE://波浪
                        x *= 50;
                        canvas.drawCircle(x, y, 5, paint); //圆
                        break;
                    default://默认密集
                        canvas.drawLine(x, height / 2, x, y, paint);//起始xy 结束xy
                        break;

                }
            }
        }
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }

    //判断是否为渐变类型
    public boolean isGdType(int typeIndex) {
        return typeIndex == GradientDrawable.LINE ||
            typeIndex == GradientDrawable.LINEAR_GRADIENT ||
            typeIndex == GradientDrawable.OVAL ||
            typeIndex == GradientDrawable.RADIAL_GRADIENT ||
            typeIndex == GradientDrawable.RECTANGLE ||
            typeIndex == GradientDrawable.RING ||
            typeIndex == GradientDrawable.SWEEP_GRADIENT;
    }
}
