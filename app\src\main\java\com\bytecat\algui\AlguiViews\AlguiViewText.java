package com.bytecat.algui.AlguiViews;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Shader;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.text.Html;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.util.Linkify;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiObjectManager;
import java.util.Arrays;
/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/10/26 11:00
 * @Describe Algui文本视图
 */
public class AlguiViewText extends TextView {

    public static final String TAG = "AlguiViewText";

    Context aContext;
    LinearLayout.LayoutParams params;//文本布局参数
    GradientDrawable gradientDrawable;//文本背景
    int tBorderSize;//描边大小

    boolean isLink;//是否自动识别并处理超链接
    //滚动文本效果相关
    boolean roll_isRoll=false;//开关
    public float roll_mOffsetX = 0;//偏移量
    public int roll_mTextWidth;//文本宽度
    public float roll_mSpeed = 2;//速度

    //渐变文本效果相关
    boolean grad_isGrad=false;//开关
    public Matrix grad_mMatrix;//矩阵
    public float grad_mTranslate;//平移量
    public float grad_colorSpeed = 1f;//速度
    public LinearGradient grad_mLinearGradient;//线性渐变
    public int[] grad_colors = null;//渐变颜色


    //点击事件回调反馈
    AlguiCallback.Click call;
    boolean isInitClick=false;//是否已经初始化点击事件
    boolean isChecked=false;
    //初始化内部点击事件
    private void initClick() {
        setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    callClick(!isChecked);
                }
            });
        isInitClick = true;
    }
    //设置点击事件回调反馈接口
    public AlguiViewText setCatCallback(AlguiCallback.Click  c) {
        if (c == null) {
            setOnClickListener(null);
            isInitClick = false;
        } else {
            call = c;
            if (!isInitClick) {
                initClick();
            }
        }
        return this;
    }


    //获取点击事件回调反馈接口
    public AlguiCallback.Click  getByteCallback() {
        return call;
    }
    //代码执行点击事件
    public AlguiViewText callClick(boolean b) {
        isChecked = b;
        if (call != null)
            call.click(isChecked);
        return this;
    }


    /**
     * 获取文本布局参数
     *
     * @return 当前的文本布局参数
     */
    public LinearLayout.LayoutParams getByteParams() {
        return params;
    }


    /**
     * 获取文本背景
     *
     * @return 当前的文本背景
     */
    public GradientDrawable getByteBack() {
        return gradientDrawable;
    }

    CharSequence sText;//文本
    int[] sBackColors,sTextColors;//背景和文本颜色
    float sStrokeSize=-1;//描边大小
    int sStrokeColor=-1;//描边颜色
    float sFilletRadiu=-1;//圆角半径
    float sTextSize=-1;//文本大小
    float sGlowRadius=-1;//发光半径
    int sGlowColor=-1;//发光颜色

    //获取样式
    public CharSequence getByteStyleText() { return sText; }
    public int[] getByteStyleBackColors() { return sBackColors; }
    public int[] getByteStyleTextColos() { return sTextColors; }
    public float getByteStyleBorderSize() { return sStrokeSize; }
    public int getByteStyleBorderColor() { return sStrokeColor; }
    public float getByteStyleRadius() { return sFilletRadiu; }
    public float getByteStyleTextSize() { return sTextSize; }
    public float getByteStyleGlowRadius() { return sGlowRadius; }
    public int getByteStyleGlowColor() { return sGlowColor; }

    boolean isTemStyle=false;
    //设置是否临时改变样式而不保存到变量
    public AlguiViewText setTemStyle(boolean isTem) {
        isTemStyle = isTem;
        return this;
    }


    //设置发光效果
    public AlguiViewText setCatTextGlow(float radius, int color) {
        if (!isTemStyle) {
            sGlowRadius = radius;
            sGlowColor = color;
        }

        setShadowLayer(radius, 0, 0, color);
        updateCatTextWidth();
        return this;
    }
    //设置动态渐变效果启动状态
    public AlguiViewText setCatTextMoveGrad(boolean b) {
        grad_isGrad = b;
        //没有渐变颜色使用默认的
        if (b) {
            if (grad_colors == null) {
                grad_colors = new int[]{ 0xFFff00cc, 0xFFffcc00, 0xFF00ffcc, 0xFFff0066};
            }
        } else {
            grad_colors = null;
        }

        invalidate();
        return this;
    }
    //设置渐变速度
    public AlguiViewText setCatTextGradSpeed(float s) {
        grad_colorSpeed = s;
        invalidate();
        return this;
    }
    //设置滚动效果启动状态
    public AlguiViewText setCatTextRoll(boolean b) {
        if (b == roll_isRoll) {
            return this;
        }
        roll_isRoll = b;
        if(b){
            setMaxLines(1);//最大显示一行
        }else{
            setMaxLines(0);//最大显示一行
        }
        //有抢夺
        /* if (roll_isRoll) {
         setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
         setCatWeight(1);
         //setMaxLines(1);//最大显示一行
         }*/
        invalidate();
        return this;
    }
    //设置滚动速度
    public AlguiViewText setCatTextRollSpeed(float s) {
        roll_mSpeed = s;
        invalidate();
        return this;
    }
    //更新文本宽度
    public AlguiViewText updateCatTextWidth() {
        roll_mTextWidth = (int)getPaint().measureText(getText().toString());
        invalidate();
        return this;
    }
    //设置文本
    public AlguiViewText setCatText(CharSequence text, Object... args) {
        if (!isTemStyle)
            sText = text;
        if (text != null) {
            String str = text.toString();
            //检查HTML文本
            if (str.indexOf("<") != -1 && str.indexOf(">") != -1) {
                setCatTextHtml(str);//设置html文本
            } else if (args != null && args.length > 0) {
                //对于需要格式化文本
                String s = String.format(str, args);
                setText(s);
                setCatTextIsLink(isLink);
            } else {
                //普通
                setText(text);
                setCatTextIsLink(isLink);
            }

        } else {
            setText("");
        }
        
        updateCatTextWidth();//更新文本宽度
        return this;
    }
    //设置Html文本
    public AlguiViewText setCatTextHtml(String text, Object... args) {
        if (!isTemStyle)
            sText = text;
        if (text != null) {
            if (args != null && args.length > 0) {
                text = String.format(text, args);
            }
            setText(Html.fromHtml(text));
        } else
            setText("");

        setCatTextIsLink(isLink);
        updateCatTextWidth();//更新文本宽度
        return this;
    }



    //设置文本颜色
    public AlguiViewText setCatTextColor(int... color) {
        if (!isTemStyle)
            sTextColors = color;
        if (color.length == 1) {
            //单个颜色关闭渐变效果
            setTextColor(color[0]);
            setCatTextMoveGrad(false);
        } else if (color.length > 1) {
            //多个颜色则启用渐变效果
            grad_colors = color;
            invalidate();
        }
        return this;
    }
    //设置文本大小
    public AlguiViewText setCatTextSize(float size) {
        if (!isTemStyle)
            sTextSize = size;
        setTextSize(TypedValue.COMPLEX_UNIT_PX, dp2px(size));
        updateCatTextWidth();//更新文本宽度
        return this;
    }
    //设置Assets文件夹字体文件作为文本字体
    public AlguiViewText setCatTextTFAssets(String assetsTfFileName) {
        setCatTextTFAssets(assetsTfFileName, Typeface.NORMAL);
        return this;
    }//重载+样式
    public AlguiViewText setCatTextTFAssets(String assetsTfFileName, int style) {
        setTypeface(
            assetsTfFileName != null ?
            Typeface.createFromAsset(aContext.getAssets(), assetsTfFileName)
            : null, style);
        updateCatTextWidth();//更新文本宽度
        return this;
    }

    //设置布局大小
    public AlguiViewText setCatSize(float w, float h) {
        if ((int)w != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int) w != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)w != ViewGroup.LayoutParams.FILL_PARENT) {
            params.width = (int)dp2px(w);
        } else {
            params.width = (int)w;
        }

        if ((int)h != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int)h != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)h != ViewGroup.LayoutParams.FILL_PARENT) {
            params.height = (int)dp2px(h);
        } else {
            params.height = (int)h;
        }
        requestLayout();//重新计算布局
        return this;
    }
    //设置权重
    public AlguiViewText setCatWeight(float weight) {
        params.weight = weight;
        requestLayout();//重新计算布局
        return this;
    }

    //设置内边距
    public AlguiViewText setCatPadding(float left, float top, float right, float bottom) {
        setPadding(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }

    //设置外边距
    public AlguiViewText setCatMargins(float left, float top, float right, float bottom) {
        params.setMargins(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }
    //设置重力
    public AlguiViewText setCatTextGravity(int g) {
        //有抢夺
        /*if (g == Gravity.CENTER_HORIZONTAL ||//水平居中
         g == Gravity.RIGHT ||//右对齐
         g == Gravity.CENTER ||//水平和垂直都居中
         g == Gravity.END//内容结束
         ) {
         setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
         setCatWeight(1);
         }*/
        setGravity(g);
        return this;
    }
    //设置是否自动识别转换文本中的超链接到可点击跳转
    public AlguiViewText setCatTextIsLink(boolean b) {
        isLink=b;
        if (b) {
            Linkify.addLinks(this, Linkify.WEB_URLS);//自动识别链接
            setMovementMethod(LinkMovementMethod.getInstance());
        } else {
            setMovementMethod(null);
        }
        return this;
    }
    //设置背景颜色
    public AlguiViewText setCatBackColor(int... backColor) {
        if (!isTemStyle)
            sBackColors = backColor;
        if (backColor.length == 1) {
            //单个颜色
            gradientDrawable.setColor(backColor[0]);
        } else if (backColor.length > 1) {
            //多个颜色，使用渐变
            gradientDrawable.setOrientation(GradientDrawable.Orientation.LEFT_RIGHT); //渐变方向
            //最后一个元素如果是渐变类型则应用否则默认线性渐变
            if (isGdType(backColor[backColor.length - 1])) {
                gradientDrawable.setGradientType(backColor[backColor.length - 1]);
                int[] newArray = Arrays.copyOf(backColor, backColor.length - 1);//删除最后一个元素
                gradientDrawable.setColors(newArray);//设置颜色
            } else {
                gradientDrawable.setGradientType(GradientDrawable.LINEAR_GRADIENT);
                gradientDrawable.setColors(backColor);//设置颜色
            }
        }
        return this;
    }
    //设置圆角半径
    public AlguiViewText setCatRadiu(float r) {
        if (!isTemStyle)
            sFilletRadiu = r;
        gradientDrawable.setCornerRadius(dp2px(r));//圆角
        return this;
    }
    //设置描边
    public AlguiViewText setCatBorder(float size, int color) {
        if (!isTemStyle)
            sStrokeSize = size;
        sStrokeColor = color;
        int bs = (int)dp2px(size);//新的描边
        gradientDrawable.setStroke(bs, color);
        int w=bs - tBorderSize;//计算内边距差值，新描边-旧描边=差值
        this.setPadding(getPaddingLeft() + w, getPaddingTop() + w, getPaddingRight() + w, getPaddingBottom() + w);//设置内边距为描边宽度防止描边覆盖子视图
        tBorderSize = bs;
        return this;
    }


    //设置父布局
    public AlguiViewText setCatParentLayout(ViewGroup vg) {
        if (vg != null)
            vg.addView(this);
        return this;
    }




    public AlguiViewText(Context c) {
        super(c);
        aContext = c;
        init();
    }

    public AlguiViewText(Context c, CharSequence text) {
        this(c);
        setCatText(text);
    }
    private void init() {
        gradientDrawable = new GradientDrawable();
        setBackground(gradientDrawable);

        params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT,
                                               LinearLayout.LayoutParams.WRAP_CONTENT);
        setLayoutParams(params);
        setClipToOutline(true);//根据父视图轮廓裁剪
        updateCatTextWidth();//更新文本宽度
        setCatText(TAG);
        setCatTextSize(7);
        setCatTextColor(0xFFFFFFFF);
        setTag(TAG);//设置标识符
        setCatTextTFAssets("lz.ttf");
        AlguiObjectManager.addView(this);
        
    }

    @Override
    protected void onDraw(Canvas canvas) {
        //滚动文本
        if (roll_isRoll) {
            //判断当前偏移量是否小于文本宽度的负值
            if (roll_mOffsetX < -roll_mTextWidth) {
                //如果文本完全滚出视图，重置偏移量为视图宽度
                roll_mOffsetX = getWidth();
            }
            //减少偏移量，实现滚动效果
            roll_mOffsetX -= roll_mSpeed;

            TextPaint textPaint = getPaint();
            textPaint.setColor(getCurrentTextColor());

            StaticLayout staticLayout = new StaticLayout(getText(), textPaint, roll_mTextWidth,
                                                         Layout.Alignment.ALIGN_NORMAL, 1.0f, 0.0f, true);
            canvas.save();
            canvas.translate(roll_mOffsetX, 0);//绘制坐标
            staticLayout.draw(canvas); //绘制文本
            canvas.restore();

        }

        //需要渐变时
        if (grad_colors != null) {
            //初始化线性渐变
            if (grad_mLinearGradient == null) {
                //线性渐变 左到右
                grad_mLinearGradient = new LinearGradient(
                    0, getHeight() / 2, //渐变起始坐标
                    getWidth(), 0,//渐变结束坐标
                    grad_colors, //渐变颜色
                    null, //渐变比例 null=均匀分布
                    Shader.TileMode.MIRROR//超出渐变范围之外的处理方式，MIRROR镜像重复显示
                );
                getPaint().setShader(grad_mLinearGradient);//设置文本的画笔为这个线性渐变效果
            }

        }

        //如果启用了文本动态渐变
        if (grad_isGrad) {
            //初始化矩阵
            if (grad_mMatrix == null) {
                grad_mMatrix = new Matrix();
            }
            //更新平移量 控制渐变移动
            grad_mTranslate += grad_colorSpeed;//平移
            grad_mMatrix.setTranslate(grad_mTranslate, 0);//设置矩阵的平移距离xy
            grad_mLinearGradient.setLocalMatrix(grad_mMatrix);//线性渐变更新局部矩阵
        }

        //如果滚动效果开启时将不使用父类的绘制
        if (!roll_isRoll) {
            super.onDraw(canvas);
        }

        if (roll_isRoll || grad_isGrad) {
            //对于动态效果则重新绘制，进行递归
            postInvalidateOnAnimation();

        }


    }


    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }

    //判断是否为渐变类型
    public boolean isGdType(int typeIndex) {
        return typeIndex == GradientDrawable.LINE ||
            typeIndex == GradientDrawable.LINEAR_GRADIENT ||
            typeIndex == GradientDrawable.OVAL ||
            typeIndex == GradientDrawable.RADIAL_GRADIENT ||
            typeIndex == GradientDrawable.RECTANGLE ||
            typeIndex == GradientDrawable.RING ||
            typeIndex == GradientDrawable.SWEEP_GRADIENT;
    }

}
