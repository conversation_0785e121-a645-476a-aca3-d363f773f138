package com.bytecat.algui.AlguiViews;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/23 09:21
 * @Describe Algui三角形视图
 */
public class AlguiViewTriangle extends View {

    public static final String TAG = "AlguiViewTriangle";
    private Context aContext;
    private Paint trianglePaint; // 画笔
    private Path trianglePath; // 路径
    private LinearLayout.LayoutParams params;//布局参数

    //三角形绘制类型
    public static class Type{
        public static final int RIGHT_ANGLE_LEFT_BOTTOM = 0; // 直角三角形_位置左下角
        public static final int RIGHT_ANGLE_LEFT_TOP = 1; // 直角三角形_位置左上角
        public static final int RIGHT_ANGLE_RIGHT_BOTTOM = 2; // 直角三角形_位置右下角
        public static final int RIGHT_ANGLE_RIGHT_TOP = 3; // 直角三角形_位置右上角

        public static final int EQUILATERAL_RIGHT = 4; // 等边三角形_位置右边
        public static final int EQUILATERAL_LEFT = 5; // 等边三角形_位置左边
        public static final int EQUILATERAL_TOP = 6; // 等边三角形_位置上边
        public static final int EQUILATERAL_BOTTOM = 7; // 等边三角形_位置下边
    }
    
    private int drawType =Type.EQUILATERAL_BOTTOM; //绘制类型
    
    public int size;

    /**
     * 获取画笔
     *
     * @return 当前的画笔对象
     */
    public Paint getBytePaint() {
        return trianglePaint;
    }

    
    /**
     * 获取路径
     *
     * @return 当前的路径对象
     */
    public Path getBytePath() {
        return trianglePath;
    }


    /**
     * 获取布局参数
     *
     * @return 当前的布局参数对象
     */
    public LinearLayout.LayoutParams getByteParams() {
        return params;
    }

    
    
    // 设置大小
    public AlguiViewTriangle setCatSize(float size) {
        this.size = (int)dp2px(size);
        params.width = this.size ;
        params.height = this.size;
        requestLayout();//重新计算布局
        invalidate();//重绘
        return this;
    }

    //设置外边距
    public AlguiViewTriangle setCatMargins(float left, float top, float right, float bottom) {
        params.setMargins(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }
    // 设置颜色
    public AlguiViewTriangle setCatColor(int color) {
        trianglePaint.setColor(color); // 设置颜色
        return this;
    }

    //设置三角形类型
    public AlguiViewTriangle setCatType(int type) {
        drawType = type;
        invalidate();//重绘
        return this;
    }

    //设置父布局
    public AlguiViewTriangle setCatParentLayout(ViewGroup vg) {
        if(vg!=null)
            vg.addView(this);
        return this;
    }


    public AlguiViewTriangle(Context context) {
        super(context);
        aContext = context;
        init();
    }


    private void init() {
        trianglePaint = new Paint(); // 初始化画笔
        trianglePaint.setStyle(Paint.Style.FILL); // 设置样式为填充
        trianglePaint.setColor(0xff1E3045); // 设置颜色
        trianglePaint.setAntiAlias(true); // 启用抗锯齿

        setBackgroundColor(Color.TRANSPARENT);//背景透明

        
        params = new LinearLayout.LayoutParams(0, 0);
        setLayoutParams(params);
        setClipToOutline(true);//根据父视图轮廓裁剪
        setCatSize(15);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //根据三角形类型处理绘制路径
        switch (drawType) {
            case Type.RIGHT_ANGLE_LEFT_BOTTOM:
                // 处理直角三角形_位置左下角
                trianglePath = new Path(); // 初始化路径
                trianglePath.moveTo(0, 0); // 从视图左上角开始
                trianglePath.lineTo(0, getHeight()); // 移动到视图左下角
                trianglePath.lineTo(getWidth(), getHeight()); // 移动到视图右下角
                break;
            case Type.RIGHT_ANGLE_LEFT_TOP:
                // 处理直角三角形_位置左上角
                trianglePath = new Path(); // 初始化路径
                trianglePath.moveTo(0, getHeight()); // 从视图左下角开始
                trianglePath.lineTo(0, 0); // 移动到视图左上角
                trianglePath.lineTo(getWidth(), 0); // 移动到视图右上角
                break;
            case Type.RIGHT_ANGLE_RIGHT_BOTTOM:
                // 处理直角三角形_位置右下角
                trianglePath = new Path(); // 初始化路径
                trianglePath.moveTo(0, getHeight()); // 从视图左下角开始
                trianglePath.lineTo(getWidth(), getHeight()); // 移动到视图右下角
                trianglePath.lineTo(getWidth(), 0); // 移动到视图右上角
                break;
            case Type.RIGHT_ANGLE_RIGHT_TOP:
                // 处理直角三角形_位置右上角
                trianglePath = new Path(); // 初始化路径
                trianglePath.moveTo(0, 0); // 从视图左上角开始
                trianglePath.lineTo(getWidth(), 0); // 移动到视图右上角
                trianglePath.lineTo(getWidth(), getHeight()); // 移动到视图右下角
                break;
            case Type.EQUILATERAL_RIGHT:
                // 处理等边三角形_位置右边
                trianglePath = new Path(); // 初始化路径
                trianglePath.moveTo(0, getHeight() / 2); // 从视图左边中心开始
                trianglePath.lineTo(getWidth(), getHeight()); // 移动到视图右下角
                trianglePath.lineTo(getWidth(), 0); // 移动到视图右上角
                break;
            case Type.EQUILATERAL_LEFT:
                // 处理等边三角形_位置左边
                trianglePath = new Path(); // 初始化路径
                trianglePath.moveTo(getWidth(), getHeight() / 2); // 从视图右边中心开始
                trianglePath.lineTo(0, getHeight()); // 移动到视图左下角
                trianglePath.lineTo(0, 0); // 移动到视图左上角
                break;
            case Type.EQUILATERAL_TOP:
                // 处理等边三角形_位置上边
                trianglePath = new Path(); // 初始化路径
                trianglePath.moveTo(getWidth() / 2, getHeight()); // 从视图下边中心开始
                trianglePath.lineTo(0, 0); // 移动到视图左上角
                trianglePath.lineTo(getWidth(), 0); // 移动到视图右上角
                break;
            case Type.EQUILATERAL_BOTTOM:
                // 处理等边三角形_位置下边
                trianglePath = new Path(); // 初始化路径
                trianglePath.moveTo(getWidth() / 2, 0); // 从视图上边中心开始
                trianglePath.lineTo(0, getHeight()); // 移动到视图左下角
                trianglePath.lineTo(getWidth(), getHeight()); // 移动到视图右下角
                break;
            default:
                // 未知直接结束
                return;
        }
        trianglePath.close(); // 闭合此路径

        // 裁剪画布此路径内部的区域作为绘制区域
        canvas.clipPath(trianglePath);
        // 绘制此路径内部的区域
        canvas.drawPath(trianglePath, trianglePaint);
    }

   

  
    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }
 



}
