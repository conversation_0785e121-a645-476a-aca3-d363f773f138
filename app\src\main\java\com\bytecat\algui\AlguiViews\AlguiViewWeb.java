package com.bytecat.algui.AlguiViews;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/11/12 16:29
 * @Describe Algui网络视图
 */
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.LinearLayout;

public class AlguiViewWeb  extends WebView {

    public static final String TAG = "AlguiViewWeb";

    Context aContext;
    Activity aActivity;
    LinearLayout.LayoutParams params;//布局参数
    GradientDrawable gradientDrawable;//背景

    int tBorderSize;//描边大小

    /**
     * 获取布局参数
     *
     * @return 当前的布局参数
     */
    public LinearLayout.LayoutParams getByteParams() {
        return params;
    }



    /**
     * 获取背景
     *
     * @return 当前的背景
     */
    public GradientDrawable getByteBack() {
        return gradientDrawable;
    }



    //设置布局大小
    public AlguiViewWeb setCatSize(float w, float h) {
        if ((int)w != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int) w != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)w != ViewGroup.LayoutParams.FILL_PARENT) {
            params.width = (int)dp2px(w);
        } else {
            params.width = (int)w;
        }

        if ((int)h != ViewGroup.LayoutParams.WRAP_CONTENT
            && (int)h != ViewGroup.LayoutParams.MATCH_PARENT
            && (int)h != ViewGroup.LayoutParams.FILL_PARENT) {
            params.height = (int)dp2px(h);
        } else {
            params.height = (int)h;
        }
        requestLayout();//重新计算布局
        return this;
    }
    //设置权重
    public AlguiViewWeb setCatWeight(float weight) {
        params.weight = weight;
        requestLayout();//重新计算布局
        return this;
    }

    //设置外边距
    public AlguiViewWeb setCatMargins(float left, float top, float right, float bottom) {
        params.setMargins(
            (int)dp2px(left), 
            (int)dp2px(top), 
            (int)dp2px(right),
            (int)dp2px(bottom));
        return this;
    }

    //设置背景颜色
    public AlguiViewWeb setCatBackColor(int backColor) {
        setBackgroundColor(backColor);
        return this;
    }
    //设置圆角半径
    public AlguiViewWeb setCatRadiu(float r) {
        gradientDrawable.setCornerRadius(dp2px(r));//圆角
        return this;
    }

    //设置缩放
    public AlguiViewWeb setCatZoom(int scaleInPercent) {
        setInitialScale(scaleInPercent);
        return this;
    }

    //通用简单加载WEB视图
    //支持：加载网站，加载HTML代码，加载HTML文件
    public AlguiViewWeb setCatWeb(String website_htmlcode_htmlfile) {
        if (website_htmlcode_htmlfile != null) {
            //字符串开头是http或https则加载网站
            if (website_htmlcode_htmlfile.startsWith("http://") || website_htmlcode_htmlfile.startsWith("https://")) {
                setCatWebsite(website_htmlcode_htmlfile);
                //对于html代码检查必有点标签特征
            } else if (website_htmlcode_htmlfile.contains("<") && website_htmlcode_htmlfile.contains(">")) {
                setCatHtml(website_htmlcode_htmlfile);
                //其它情况默认识别为本地代码文件
            } else {
                setCatHtmlFile(website_htmlcode_htmlfile);
            }
        }
        return this;
    }
    //加载网站 网站链接
    public AlguiViewWeb setCatWebsite(String url) {
        loadUrl(url);
        return this;
    }
    //加载网站 网站链接，缩放百分比
    public AlguiViewWeb setCatWebsite(String url, int scaleInPercent) {
        loadUrl(url);
        setInitialScale(scaleInPercent);
        return this;
    }

    //加载本地html代码文件
    public AlguiViewWeb setCatHtmlFile(String filePath) {
        if (filePath == null) {
            loadUrl(filePath);
            return this;
        }
        //如果路径开头是assets文件夹 不区分大小写
        if (filePath.toLowerCase().startsWith("/assets/") || filePath.toLowerCase().startsWith("assets/")) {
            //修复assets字符串
            int lastSlashIndex = filePath.lastIndexOf('/');

            if (lastSlashIndex != -1) {
                filePath="file:///android_asset/"+filePath.substring(lastSlashIndex + 1);
            }else{
                filePath="file:///android_asset/"+filePath;
            }
            
        }else if(!filePath.contains("/")){
            //对于纯文件名 识别为assets文件夹下的文件
            filePath="file:///android_asset/"+filePath;
        }else{
            loadUrl(filePath);
        }
        

        return this;
    }

    //加载html代码  资源根目录路径(本地&服务器)，网络代码，MIME类型，编码格式，上一个网络代码(历史记录)
    public AlguiViewWeb setCatHtml(@Nullable String baseUrl, @NonNull String data, @Nullable String mimeType, @Nullable String encoding, @Nullable String historyUrl) {
        loadDataWithBaseURL(baseUrl, data, mimeType, encoding, historyUrl);
        return this;
    }
    //加载html代码  资源根目录路径(本地&服务器)，网络代码，MIME类型，编码格式
    public AlguiViewWeb setCatHtml(@Nullable String baseUrl, @NonNull String data, @Nullable String mimeType, @Nullable String encoding) {
        loadDataWithBaseURL(baseUrl, data, mimeType, encoding, null);
        return this;
    }
    //加载html代码  资源根目录路径(本地&服务器)，网络代码，MIME类型
    public AlguiViewWeb setCatHtml(@Nullable String baseUrl, @NonNull String data, @Nullable String mimeType) {
        loadDataWithBaseURL(baseUrl, data, mimeType, "UTF-8", null);
        return this;
    }
    //加载html代码  资源根目录路径(本地&服务器)，网络代码
    public AlguiViewWeb setCatHtml(@Nullable String baseUrl, @NonNull String data) {
        loadDataWithBaseURL(baseUrl, data, "text/html", "UTF-8", null);
        return this;
    }
    //加载html代码  网络代码
    public AlguiViewWeb setCatHtml(@NonNull String data) {
        loadDataWithBaseURL(null, data, "text/html", "UTF-8", null);
        return this;
    }

    //设置父布局
    public AlguiViewWeb setCatParentLayout(ViewGroup vg) {
        if (vg != null)
            vg.addView(this);
        return this;
    }
    public AlguiViewWeb(Context context, Activity activity) {
        super(context);
        aContext = context;
        aActivity = activity;
        init();
    }
    public AlguiViewWeb(Context context, Activity activity, String website_htmlcode_htmlfile) {
        this(context, activity);
        setCatWeb(website_htmlcode_htmlfile);
    }

    private void init() {
        params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT, 1);
        gradientDrawable = new GradientDrawable();

        setLayoutParams(params);
        setBackground(gradientDrawable); //设置背景
        setLayerType(View.LAYER_TYPE_HARDWARE, null);//硬件加速

        //Web属性
        setClipChildren(true);//子视图超过边界时自动裁剪
        setClipToOutline(true);//根据父视图轮廓裁剪
        getSettings().setJavaScriptEnabled(true);// 允许在 WebView 中执行 JavaScript 代码，
        getSettings().setLoadsImagesAutomatically(true);//自动加载图片。
        getSettings().setBlockNetworkImage(false);//是否阻塞网络图片加载。
        getSettings().setDomStorageEnabled(true);//启用或禁用 DOM Storage API。
        getSettings().setAllowFileAccess(true);//是否允许 WebView 访问文件。
        getSettings().setGeolocationEnabled(true);//启用或禁用地理位置定位。
        getSettings().setDatabaseEnabled(true);//启用或禁用数据库。
        getSettings().setJavaScriptCanOpenWindowsAutomatically(true);//设置 JavaScript 是否可以自动打开窗口。
        getSettings().setDefaultTextEncodingName("utf-8");//设置默认的文本编码

        //Web自适应
        getSettings().setUseWideViewPort(true);//页面自适应屏幕宽度
        getSettings().setLoadWithOverviewMode(true);//图片自适应屏幕大小
        getSettings().setSupportZoom(true);  //是否支持缩放
        getSettings().setBuiltInZoomControls(true);  //是否显示缩放控制按钮
        getSettings().setDisplayZoomControls(false);  //是否隐藏默认的缩放控件
        //getSettings().setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN);//单列布局
        //setInitialScale(50); //设置缩放比例，避免页面加载过大

        setWebViewClient(new WebViewClient() {
                @Override
                public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                    //页面加载开始
                    //super.onPageStarted(view, url, favicon);//显示加载动画
                }
                @Override
                public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                    //页面加载出错
                    //跳转到系统默认的浏览器
                    //AlguiLog.d(TAG + " 页面加载失败：跳转默认游览器");
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(failingUrl));
                    if (!(aActivity instanceof Context))
                        aActivity.startActivity(intent);
                }
                @Override
                public void onPageFinished(WebView view, String url) {
                    //页面加载完成
                    //super.onPageFinished(view, url);//隐藏加载动画
                }
                @Override
                public boolean shouldOverrideUrlLoading(WebView view, String url) {
                    //使其网站中的所有超链接直接跳转而不是打开游览器跳转
                    super.shouldOverrideUrlLoading(view, url);
                    view.loadUrl(url);
                    return true;
                }

            });

        setCatBackColor(0xff20324D);//背景颜色

        //loadWebsite("https://www.w3school.com.cn/index.html");//加载网站
        //loadCodeFile("/assets/AlguiWebExample.html");//加载代码文件

        //初始演示HTML内容
        setCatHtml
        (
            //资源根目录 (也可以是服务器根目录)
            //HTML可直接引用该根目录中的资源，这里默认Assets文件夹
            "file:///android_asset/",
            //当前HTML页面 其中引用的资源都是上方根目录中的
            //----------------
            "<html><body>" +
            //CSS
            "<style>" +
            "a{color: #81C784;}" +
            "p{color: #ADB1B7; font-size: 20px;}" +//段落样式
            "h1 {color: #ADB1B7; font-size:30px}" +//标题样式
            "img{width:200px; height:auto;}" +//图片样式
            "button {" +//按钮样式
            "background-color: #4CAF50;" +
            "border: none;" +
            "color: white;" +
            "padding: 15px 32px;" +
            "text-align: center;" +
            "text-decoration: none;" +
            "display: inline-block;" +
            "font-size: 16px;" +
            "}" +
            "</style>" +
            //js
            "<script>" +
            "function setText(id,newS) { " +
            "var element = document.querySelector(id);" +
            "if (element) {" +
            " element.textContent = newS;" +
            "} " +
            "}" +
            "</script>" +
            //HTML
            "<h1>这是AlguiWeb示例页面<a href='https://www.w3school.com.cn/index.html'> HTML，CSS，JS学习</a></h1>" +
            "<p id='p1'>This is the AlguiWeb page</p>" +
            "<button onclick=\"setText('#p1','Hello World！')\">修改上方文本</button><br>" +
            "<img src='AlguiWebImage1.png'/>" +
            "</body></html>",
            //----------------
            "text/html",//MIME类型
            "UTF-8", //编码格式
            null//上一个页面 (历史记录可以在当前跳转到之前 不需要就传null)
        );



    }



    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }



}
