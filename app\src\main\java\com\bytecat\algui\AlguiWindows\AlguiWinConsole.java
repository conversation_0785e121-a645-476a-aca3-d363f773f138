package com.bytecat.algui.AlguiWindows;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/31 10:58
 * @Describe Algui控制台窗口
 */
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.text.Selection;
import android.text.Spannable;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiViews.AlguiFlowLayout;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.bytecat.algui.AlguiViews.AlguiViewText;

public class AlguiWinConsole {

    public static final String TAG = "AlguiWinConsole";
    Context aContext;
    AlguiWinMenu w;
    AlguiViewText text;
    String str="";

    public AlguiWinMenu getByteWindow(){return w;}
    public AlguiViewText getByteText(){return text;}
    public String getByteString(){return str;}
    
    
    public AlguiWinConsole(Context context) {
        aContext = context;
        init();
    }
    
    //设置标题
    public AlguiWinConsole setCatTitle(CharSequence textstr, Object... args) {
        w.setCatMenuTitle(textstr,args);
        return this;
    }
    //设置窗口颜色
    public AlguiWinConsole setCatColor(int... colors) {
        w.setCatMenuTopBackColor(colors);
        if (colors.length > 0)
            w.setCatMenuTriangleColor(colors[0]);
        return this;
    }
    //设置控制台悬浮球图标
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiWinConsole setCatBallImage(String Url_Base64_FilePath) {
        w.setCatBallImage(Url_Base64_FilePath);
        return this;
    }
    private void init() {
        w = new AlguiWinMenu(aContext, TAG)
            .setCatMenuTitleColor(0xFFFFFFFF)
            .setCatMenuEndIconColor(0xFFFFFFFF)
            .setCatBallColor(0xFFFF5722)
            .setCatBallImage(AlguiAssets.Icon.owl)
            .setCatMenuTopBackColor(0xCEFF5722)
            .setCatMenuTriangleColor(0xCED84315)
            ;
            w.setCatEnableMenuOutsideEnd(false);//禁用点击窗口外部关闭
            w.setCatMenuSize(200,100);
        //设置点击监听
        w.setCatMenuOpenCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    //窗口添加无法获取输入焦点的flag 防止显示悬浮球时窗口之外无法弹出输入法
                    w.addWinFlag(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
                }
            }
        );
        //这里只使用一个文本提升性能 后面往str拼接内容，避免每次输出都创建新文本
        text = new AlguiViewText(aContext)
            .setCatParentLayout(w)
            .setCatTextColor(0xC200FF00);
        text.setLayerType(View.LAYER_TYPE_HARDWARE, null);
        text.setHighlightColor(0xFFBF360C);

        text.setCatTextIsLink(true);
        text.setTextIsSelectable(true); 
        text.setLongClickable(true);    
        //text.setSingleLine(true);
text.setHorizontallyScrolling(true); //可水平滚动

        text.setOnLongClickListener(new View.OnLongClickListener() {

                AlguiFlowLayout layout;
                AlguiViewButton selectAll;
                AlguiViewButton copy;
                PopupWindow popupWindow;
                boolean isInit=false;

                @Override
                public boolean onLongClick(View v) {
                    if (!isInit) {
                        layout = new AlguiFlowLayout(aContext);
                        layout.setCatBackColor(0xff505050);//设置布局背景色 让按钮缩放动画后显示此颜色营造按钮按下后描边效果
                        selectAll =  new AlguiViewButton(aContext, "全选")
                            .setCatBackColor(0xff2C2C2E)
                            .setCatParentLayout(layout)
                            .setCatCallback(new AlguiCallback.Click(){
                                public void click(boolean b) {
                                    if (text.getText() instanceof Spannable) {
                                        Spannable spannable = (Spannable) text.getText();
                                        Selection.setSelection(spannable, 0, spannable.length());
                                    }
                                }
                            }
                        );
                        copy = new AlguiViewButton(aContext, "复制")
                            .setCatBackColor(0xff2C2C2E)
                            .setCatParentLayout(layout)
                            .setCatCallback(new AlguiCallback.Click(){
                                public void click(boolean b) {
                                    if (text.getText() instanceof Spannable) {
                                        Spannable spannable = (Spannable) text.getText();
                                        int start = Selection.getSelectionStart(spannable);
                                        int end = Selection.getSelectionEnd(spannable);
                                        if (start != end) {
                                            String selectedText = spannable.subSequence(start, end).toString();
                                            ClipboardManager clipboardManager = (ClipboardManager) aContext.getSystemService(Context.CLIPBOARD_SERVICE);
                                            ClipData clipData = ClipData.newPlainText("selectedText", selectedText);
                                            clipboardManager.setPrimaryClip(clipData);
                                        }
                                    }
                                }
                            }
                        );

                        popupWindow = new PopupWindow(layout, LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                        popupWindow.setOutsideTouchable(true);
                        isInit = true;
                    }
                    popupWindow.showAsDropDown(v, 0, 0);

                    return false;
                }
            });
    }
    
    //输出文本(覆盖 这将覆盖之前的所有内容)
    public AlguiWinConsole WriteCover(String string, Object... args) {
        str = string;
        text.setCatText(str, args);
        w.updateWin();
        return this;
    }

    //输出文本(输出后不换行)
    public AlguiWinConsole Write(String string, Object... args) {
        str += string;
        text.setCatText(str, args);
        w.updateWin();
        return this;
    }

    //输出文本(输出后换行)
    public AlguiWinConsole WriteLine(String string, Object... args) {
        if (str.length() > 1)
            str += "\n";
        str += string;
        text.setCatText(str, args);
        w.updateWin();
        return this;
    }
    


    //显示控制台
    public AlguiWinConsole show() {
        w.showMenu();
        return this;
    }
    //退出控制台
    public AlguiWinConsole exit() {
        w.hideWin();
        return this;
    }
    //清除控制台内容
    public AlguiWinConsole clear() {
        str = "";
        text.setCatText(str);
        w.updateWin();
        return this;
    }
    //控制台行结束 开始换行
    public AlguiWinConsole endl() {
        str += "\n";
        text.setCatText(str);
        w.updateWin();
        return this;
    }



}
