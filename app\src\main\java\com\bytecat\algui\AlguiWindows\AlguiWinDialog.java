package com.bytecat.algui.AlguiWindows;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/19 13:25
 * @Describe Algui对话框窗口
 */
import androidx.annotation.Nullable;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiTools.AlguiToolPermission;
import com.bytecat.algui.AlguiViews.AlguiFlowLayout;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.bytecat.algui.AlguiViews.AlguiViewImage;
import com.bytecat.algui.AlguiViews.AlguiViewText;
import java.util.ArrayList;

public class AlguiWinDialog extends AlguiFlowLayout {

    public static final String TAG = "AlguiWinDialog";
    //对话框样式
    public static class Style {
        //背景色和前景色
        public static final int BLACK[] = {0xFF303030, 0xFFADB1B7};//黑色
        public static final int WHITE[] = {0xFFEBF2EB, 0xFF303030};//白色
        public static final int BLUE[] = {0xFF294A7A, 0xFFADB1B7};//蓝色
    }

    Context aContext;

    AlertDialog.Builder builder;//对话框构建器
    AlertDialog dialog;//对话框

    AlguiFlowLayout rootLayout;//对话框主布局
    AlguiViewImage icon;//对话框图标
    AlguiViewText title;//对话框标题
    AlguiViewImage endIcon;//对话框关闭图标

    AlguiViewButton noButton;//消极按钮
    AlguiViewButton yesButton;//积极按钮

    //Getter拓展方法
    public AlertDialog.Builder getByteDialogBuilder() {
        return builder;
    }
    public AlertDialog getByteDialog() {
        return dialog;
    }
    public AlguiFlowLayout getByteRootLayout() {
        return rootLayout;
    }
    public AlguiViewImage getByteIcon() {
        return icon;
    }
    public AlguiViewText getByteTitle() {
        return title;
    }
    public AlguiViewImage getByteEndIcon() {
        return endIcon;
    }

    public AlguiViewButton getByteNoButton() {
        return noButton;
    }
    public AlguiViewButton getByteYesButton() {
        return yesButton;
    }
    //获取所有行
    public ArrayList<AlguiLinearLayout> getByteLineList() {
        return super.getByteLineList();
    }
    //获取当前行 (最后一行)
    public AlguiLinearLayout getByteLine() {
        return super.getByteLine();
    }
    //获取指定行
    public AlguiLinearLayout getByteLine(int index) {
        return super.getByteLine(index);
    }

    public AlguiWinDialog(Context context) {
        super(context);
        aContext = context;
        init();

    }

    private void init() {
        //对话框主布局 这里使用流程布局更方便
        rootLayout = new AlguiFlowLayout(aContext)
            .setCatPadding(10, 10, 10, 5);

        //对话框图标
        icon = new AlguiViewImage(aContext, AlguiAssets.Icon.inform_info)
            .setCatSize(15, 15)
            .setCatMargins(0, 0, 5, 0)
            ;


        //对话框标题
        title = new AlguiViewText(aContext, TAG)
            .setCatSize(AlguiLinearLayout.LayoutParams.MATCH_PARENT, AlguiLinearLayout.LayoutParams.WRAP_CONTENT)
            .setCatWeight(1)
            .setCatTextSize(11)
            .setCatTextTFAssets("lz.ttf", Typeface.BOLD);

        //对话框关闭图标
        endIcon = new AlguiViewImage(aContext, AlguiAssets.Icon.fork)
            .setCatSize(9, 9)
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    end();
                }
            }
        );

        //内容布局

        super.setCatMargins(0, 5, 0, 5);
        super.setCatPadding(3, 0, 3, 0);


        //消极按钮
        noButton = new AlguiViewButton(aContext, "取消")
            .setCatTextSize(9)
            .setCatPadding(7, 2, 7, 2)
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    end();
                }
            }
        );

        //积极按钮
        yesButton = new AlguiViewButton(aContext, "确定")
            .setCatTextSize(9)
            .setCatPadding(7, 2, 7, 2)
            .setCatMargins(5, 0, 0, 0)
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    end();
                }
            }
        );



        //构造整体界面
        rootLayout.addView(icon);//加图标
        rootLayout.addView(title);//加标题
        rootLayout.addView(endIcon);//加关闭图标
        rootLayout.endl();//换行
        //rootLayout.getLine().setCatMargins(0, 5, 0, 5);//设置这一行的外边距
        rootLayout.addView(this);//加内容布局
        rootLayout.endl();//换行
        rootLayout.getByteLine().setGravity(Gravity.END);//设置这一行视图的位置
        rootLayout.addView(noButton);//加消极按钮
        rootLayout.addView(yesButton);//加积极按钮



        //对话框初始化
        builder = new AlertDialog.Builder(aContext);//构建器
        builder.setView(rootLayout);//为对话框设置自定义布局
        dialog = builder.create();//创建对话框
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));//设置对话框背景透明 (以便应用圆角)
        //设置窗口类型
        if (aContext instanceof Activity) {
            //对于上下文为活动时 窗口类型设置为应用级窗口 (无需悬浮窗权限)
            dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION);
        } else {
            //对于其它 则使用系统级后台全局窗口 (需要悬浮窗权限)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                //对于安卓8.0以上
                dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
            } else {
                //对于安卓8.0以下
                dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }
            //申请悬浮窗权限
            AlguiToolPermission.getWindow(aContext);
        }
        setCatStyle(Style.BLACK);//默认黑色样式
        setCatIsCanEnd(true);//默认可以关闭对话框
    }


    //设置对话框主题样式
    public AlguiWinDialog setCatStyle(int style[]) {
        if (style != null && style.length >= 2)
            setCatColor(style[0], style[1]);
        return this;
    }
    //设置对话框颜色 背景色和前景色
    public AlguiWinDialog setCatColor(int backColor, int ForeColor) {
        //主布局样式
        getByteRootLayout()
            .setCatBackColor(backColor)//背景颜色
            .setCatRadiu(0)//圆角
            .setCatBorder(0, 0)//描边
            ;

        //图标样式
        getByteIcon()
            .setCatSize(15, 15)//大小
            .setCatColor(ForeColor)//颜色
            .setCatRadiu(0)//圆角
            ;

        //标题样式
        getByteTitle()
            .setCatTextSize(11)//大小
            .setCatTextColor(ForeColor)//颜色
            ;

        //关闭图标样式
        getByteEndIcon()
            .setCatSize(9, 9)//大小
            .setCatColor(ForeColor)//颜色
            .setCatRadiu(0)//圆角
            ;

        //内容布局样式
        super
            .setCatBackColor(0)//背景颜色
            .setCatRadiu(0)//圆角
            .setCatBorder(0f, 0)//描边
            ;

        //消极按钮样式
        getByteNoButton()
            .setCatTextSize(9)//大小
            .setCatBackColor(0)//背景颜色
            .setCatBorder(0.38f, ForeColor)//描边
            .setCatTextColor(ForeColor)//文本颜色
            .setCatRadiu(0)//圆角
            ;

        //积极按钮样式
        getByteYesButton()
            .setCatTextSize(9)//大小
            .setCatBackColor(ForeColor)//背景颜色
            .setCatBorder(0.38f, ForeColor)//描边
            .setCatTextColor(backColor)//文本颜色
            .setCatRadiu(0)//圆角
            ;
        return this;
    }


    //设置对话框图标
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiWinDialog setCatIcon(@Nullable String Url_Base64_FilePath) {
        if (icon != null) {
            if (Url_Base64_FilePath != null) {
                icon.setVisibility(View.VISIBLE);//可见
                icon.setCatImage(Url_Base64_FilePath);
            } else {
                icon.setVisibility(View.GONE);//不可见
            }
        }
        return this;
    }

    //设置对话框标题
    public AlguiWinDialog setCatTitle(CharSequence titleText, Object... args) {
        if (title != null)
            title.setCatText(titleText, args);
        return this;
    }
    //设置对话框是否可以关闭
    public AlguiWinDialog setCatIsCanEnd(boolean isCanEnd) {
        if (isCanEnd) {
            endIcon.setVisibility(View.VISIBLE);//关闭图标可见
            //设置关闭图标监听
            endIcon.setCatCallback(new AlguiCallback.Click(){
                    public void click(boolean b) {
                        end();
                    }
                }
            );
            dialog.setCanceledOnTouchOutside(true); //可以点击对话框外部关闭对话框
        } else {
            endIcon.setCatCallback(null);//关闭图标无监听
            endIcon.setVisibility(View.GONE);//关闭图标不可见
            dialog.setCanceledOnTouchOutside(false); //禁止点击对话框外部关闭对话框
        }
        return this;
    }
    //设置对话框内容布局一行最大视图数量 超出则自动换行
    public AlguiWinDialog setCatLineMaxView(int num) {
        super.setCatLineMaxView(num);
        return this;
    }
    //设置对话框所有行的外边距
    public AlguiFlowLayout setCatLineMargins(float left, float top, float right, float bottom) {
        super.setCatLineMargins(left,top,right,bottom);
        return this;
    }
    //对话框内容布局当前行添加视图 null换行
    public AlguiWinDialog addView(View... views) {

        super.addView(views);
        return this;
    }
    //内容布局手动换行
    public AlguiLinearLayout endl() {
        return super.endl();
    }
    //设置内容布局方向
    public AlguiWinDialog setCatOrientation(int orientation) {

        super.setCatOrientation(orientation);

        return this;
    }
    //删除内容布局所有视图
    public AlguiWinDialog remAllView() {

        super.remAllView();

        return this;
    }
    //删除内容布局子视图
    public AlguiWinDialog remView(View... view) {

        super.remView(view);

        return this;
    }
    //删除内容布局指定行的视图
    public AlguiWinDialog remViewToLine(int index, View... views) {

        super.remViewToLine(index, views);

        return this;
    }
    //删除内容布局指定行 (按索引)
    public AlguiWinDialog remLine(int... indexs) {

        super.remLine(indexs);

        return this;
    }
    //删除内容布局指定行 (按对象)
    public AlguiWinDialog remLine(AlguiLinearLayout... objs) {

        super.remLine(objs);

        return this;
    }
    //添加视图到内容布局的指定行
    public AlguiWinDialog addViewToLine(int index, View... views) {

        super.addViewToLine(index, views);

        return this;
    }


    //设置消极按钮的文本
    public AlguiWinDialog setCatNoButtonText(@Nullable CharSequence text, Object... args) {
        if (noButton != null) {
            if (text != null) {
                noButton.setVisibility(View.VISIBLE);//可见
                noButton.setCatText(text, args);
            } else {
                noButton.setVisibility(View.GONE);//不可见
            }
        }

        return this;
    }
    //设置消极按钮点击事件
    public AlguiWinDialog setCatNoButtonClick(AlguiCallback.Click callback) {
        if (noButton != null) 
            noButton.setCatCallback(callback);
        return this;
    }
    //设置积极按钮的文本
    public AlguiWinDialog setCatYesButtonText(@Nullable CharSequence text, Object... args) {
        if (yesButton != null) {
            if (text != null) {
                yesButton.setVisibility(View.VISIBLE);//可见
                yesButton.setCatText(text, args);
            } else {
                yesButton.setVisibility(View.GONE);//不可见
            }
        }

        return this;
    }
    //设置积极按钮点击事件
    public AlguiWinDialog setCatYesButtonClick(AlguiCallback.Click callback) {
        if (yesButton != null) 
            yesButton.setCatCallback(callback);
        return this;
    }
    //显示对话框
    public AlguiWinDialog show() {
        if (dialog != null)
            dialog.show();
        return this;
    }
    //结束对话框
    public AlguiWinDialog end() {
        if (dialog != null)
            dialog.dismiss();
        return this;
    }


}
