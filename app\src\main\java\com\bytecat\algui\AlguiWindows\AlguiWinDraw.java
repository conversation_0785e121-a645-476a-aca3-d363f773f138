package com.bytecat.algui.AlguiWindows;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.WindowManager;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import android.graphics.Paint;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/26 16:38
 * @Describe Algui每帧在屏幕上动态绘制
 */
public class AlguiWinDraw extends SurfaceView implements SurfaceHolder.Callback {

    public static final String TAG = "AlguiWinDraw";

    Context aContext;
    AlguiWindow window;//窗口
    SurfaceHolder surfaceHolder;//Surface画布管理器
    Thread renderThread;//渲染线程
    AlguiCallback.Draw call;//回调接口
    boolean isRunning;//是否运行渲染线程
    boolean isStartDraw;//是否开始绘制
    boolean isOne = true;//第一帧
    public AlguiWinDraw setCatCallback(AlguiCallback.Draw c) { call = c; return this; } 
    public AlguiCallback.Draw getByteCallback() { return call; } 
    public AlguiWindow getByteWindow() { return window; } 
    public SurfaceHolder getByteSurfaceHolder() { return surfaceHolder; } 
    public Thread getByteRenderThread() { return renderThread; } 
    public boolean isRunning() { return isRunning; } 
    public boolean isStartDraw() { return isStartDraw; } 
    public boolean isFirstFrame() { return isOne; } 


    //设置在哪个活动进行绘制
    public AlguiWinDraw setCatDrawActivity(Context context) {
        window.setCatActivity(context);
        return this;
    }

    //开始绘制
    public AlguiWinDraw startDraw() {
        window.show();
        isOne = true;
        isStartDraw = true;
        return this;
    }
    //结束绘制
    public AlguiWinDraw endDraw() {
        window.hide();
        isStartDraw = false;
        return this;
    }
    //结束渲染线程
    public AlguiWinDraw endRendering() {
        window.hide();
        isRunning = false;
        return this;
    }

    public AlguiWinDraw(Context context) {
        super(context);
        aContext = context;
        init();
    }

    private void init() {
        //初始化管理器
        surfaceHolder = getHolder();
        surfaceHolder.addCallback(this);
        surfaceHolder.setFormat(PixelFormat.RGBA_8888);

        //初始化窗口
        window = new AlguiWindow(aContext);
        //window.addFlag(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);//可超出屏幕
        //window.addFlag(WindowManager.LayoutParams.FLAG_FULLSCREEN);//全屏显示
        //window.addFlag(WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN);//占满整个屏幕
        window.addFlag(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);//禁止触摸
        window.setCatSize(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
        
        //window.getByteWindowParams().systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN;

        window.setCatView(this);
    }

    //每帧更新
    private void update(Canvas canvas) {
        if (call != null) {
            if (isOne) {
                //直到初始化完成
                while (!call.Start(canvas)) {
                    if(!isStartDraw){
                        return;
                    }
                }
                isOne = false;
            }
            if (!call.Update(canvas)) {
                //更新失败开始初始化
                isOne = true;
                //完全清除上一帧所有像素
                canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
            }
        }
    }

    //Surface创建监听
    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        isRunning = true;//启动渲染
        //渲染线程
        renderThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    while (isRunning) {
                        if (isStartDraw) {
                            Canvas canvas = null; 
                            try {
                                //这里锁定并获取Surface的画布
                                //确保每一帧的绘制过程是原子性的且不会受到其他线程的干扰提升性能
                                canvas = surfaceHolder.lockCanvas();

                                if (canvas != null) {
                                    synchronized (surfaceHolder) {  
                                        //完全清除上一帧所有像素
                                        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
                                        //开始下一帧更新
                                        update(canvas);
                                        if (!isStartDraw)
                                            canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);

                                    }
                                }
                            } finally {
                                if (canvas != null) {
                                    surfaceHolder.unlockCanvasAndPost(canvas);//提交更新内容到屏幕上并解锁画布
                                }
                            }

                        }
                    }
                }
            });
        renderThread.start();
    }

    //Surface画布尺寸发生变化时
    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        if (call != null) 
            call.UpdateCanvasSize(holder, format, width, height);
    }

    //Surface销毁时
    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        isRunning = false;
        try {
            renderThread.join(); // 等待线程结束
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        if (call != null)
            call.End(holder);
    }
    
   

}
