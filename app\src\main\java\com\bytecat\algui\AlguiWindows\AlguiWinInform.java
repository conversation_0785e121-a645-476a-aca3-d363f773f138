package com.bytecat.algui.AlguiWindows;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/18 18:06
 * @Describe Algui消息通知窗口 (单例)
 */
import androidx.annotation.Nullable;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiViews.AlguiViewImage;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiViewText;
import com.bytecat.algui.AlguiActivity;
import com.bytecat.algui.AlguiManager.AlguiLog;

public class AlguiWinInform {

    public static final String TAG = "AlguiWinInform";

    Context aContext;
    static private AlguiWinInform obj;//唯一实例
    AlguiWindow window;//窗口
    AlguiLinearLayout rootlayout;//根布局

    //Getter拓展方法
    public AlguiWindow getByteWindow() {
        return window;
    }
    public AlguiLinearLayout getByteRootLayout() {
        return rootlayout;
    }

    //单例访问器
    public static AlguiWinInform Get(Context context) {
        if (obj == null) {
            obj = new AlguiWinInform(context);
        }
        return obj;
    }

    private AlguiWinInform(Context context) {
        aContext = context;
        init();
    }

    private void init() {
        //窗口
        window = new AlguiWindow(aContext);
        window.setCatPosGravity(Gravity.BOTTOM | Gravity.END);//原点位于右下角
        window.setCatPos(10, 10);//右下角xy偏移量
        window.setCatAnimations(AlguiWindow.Animations.Animation_InputMethod);//动画
        window.addFlag(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);//不可触摸

        //根布局
        rootlayout = new AlguiLinearLayout(aContext);
        rootlayout.setCatBackColor(Color.TRANSPARENT);//背景透明
        rootlayout.setOrientation(LinearLayout.VERTICAL);//垂直
        rootlayout.setGravity(Gravity.END);

        window.setCatView(rootlayout);//窗口设置主布局
        window.show();//显示

    }

    //是否启动调试模式
    public AlguiWinInform setByteDebug(boolean b) {
        if (b) {
            rootlayout.setCatBorder(1, 0xFF9C27B0);
            rootlayout.setCatBackColor(0xCECE93D8);
        } else {
            rootlayout.setCatBorder(0, 0);
            rootlayout.setCatBackColor(0);
        }
        return this;
    }

    //设置通知显示的位置 例如右上角：Gravity.TOP | Gravity.END
    public AlguiWinInform setCatPos(int gravity) {
        window.setCatPosGravity(gravity);
        return this;
    }

    //设置窗口显示在哪个活动中
    //如果传入Activity则仅悬浮显示在这一个活动中(无需悬浮窗权限)
    //传入后台Context则全局悬浮显示(需悬浮窗权限)
    public AlguiWinInform setCatActivity(Context context) {
        if (context != null)
            aContext = context;
        window.setCatActivity(context);
        return this;
    }
    //清除所有通知
    public AlguiWinInform clear() {
        rootlayout.remAllView();
        return this;
    }
    //显示自定义消息通知视图 (显示时间单位秒)
    public AlguiWinInform showInfo(int showTime, final View... views) {
        if (views == null || showTime <= 0)
            return this;

        for (final View aView : views) {
            if (aView != null) {
                if (rootlayout.indexOfChild(aView) == -1) {
                    rootlayout.addView(aView);
                    //定时n毫秒后清除当前通知
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                            @Override
                            public void run() {

                                //设置渐隐效果
                                AlphaAnimation fadeOut = new AlphaAnimation(1, 0);
                                fadeOut.setDuration(2000); //动画持续时间（单位：毫秒）
                                //动画监听器
                                fadeOut.setAnimationListener(new Animation.AnimationListener() {
                                        @Override
                                        public void onAnimationStart(Animation animation) {
                                            //动画开始
                                        }

                                        @Override
                                        public void onAnimationEnd(Animation animation) {
                                            // 动画结束
                                            if (rootlayout.indexOfChild(aView) != -1) {
                                                aView.setVisibility(View.GONE);//不再可见
                                                //如果是布局清除所有子视图
                                                if (aView instanceof ViewGroup) {
                                                    ((ViewGroup)aView).removeAllViews();
                                                }
                                                rootlayout.removeView(aView);
                                            }

                                        }

                                        @Override
                                        public void onAnimationRepeat(Animation animation) {
                                            // 动画重复
                                        }
                                    });

                                aView.startAnimation(fadeOut);





                            }
                        }, showTime * 1000);//转换为毫秒

                    //设置渐显效果
                    AlphaAnimation fadeIn = new AlphaAnimation(0, 1);
                    fadeIn.setDuration(1000); //动画持续时间（单位：毫秒）

                    aView.startAnimation(fadeIn);
                }

            }
        }



        return this;
    }

    //显示一个消息通知 (预设通知：自定义外观)
    public AlguiWinInform showInfo(
        //通知主布局外观
        int layoutBackColor, float layoutRadiu, float layoutBorderSize, int layoutBorderColor,
        //图标外观
        @Nullable String Image_Url_Base64_FilePath, int imageColor, float imageRadiu,
        //标题外观
        @Nullable CharSequence titleText, int titleTextColor, String titleTextAssetsTfFileName,
        //信息外观
        @Nullable CharSequence infoText, int infoTextColor, String infoTextAssetsTfFileName,
        //显示时间(秒)
        int showTime
    ) {
        if (showTime <= 0)
            return this;
        //通知主布局
        AlguiLinearLayout infoLayout = new AlguiLinearLayout(aContext);
        infoLayout.setCatSize(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        infoLayout.setCatMargins(0, 0, 0, 0);//外边距
        infoLayout.setOrientation(LinearLayout.HORIZONTAL);//横向
        infoLayout.setCatPadding(3, 5, 3, 5);//内边距
        infoLayout.setGravity(Gravity.CENTER);//子布局位置
        infoLayout.setCatBackColor(layoutBackColor);//背景颜色
        infoLayout.setCatRadiu(layoutRadiu);//圆角半径
        infoLayout.setCatBorder(layoutBorderSize, layoutBorderColor);//描边
        infoLayout.setAlpha(1f);//透明度

        if (Image_Url_Base64_FilePath != null) {
            //通知图标
            AlguiViewImage infoImg = new AlguiViewImage(aContext, Image_Url_Base64_FilePath);
            infoImg.setCatSize(20, 20);//大小
            infoImg.setCatColor(imageColor);//颜色
            infoImg.setCatRadiu(imageRadiu);//圆角

            infoLayout.addView(infoImg);//主布局添加图标
        }

        //文本布局
        AlguiLinearLayout textLayout = new AlguiLinearLayout(aContext);
        textLayout.setCatBackColor(Color.TRANSPARENT);
        textLayout.setOrientation(LinearLayout.VERTICAL);//垂直
        textLayout.setGravity(Gravity.LEFT);
        //图标存在才给文本布局设置外边距
        if (Image_Url_Base64_FilePath != null)
            textLayout.setCatMargins(10, 0, 0, 0);
        String log="";
        if (titleText != null) {
            //标题
            log += titleText;
            AlguiViewText infoTitle = new AlguiViewText(aContext, titleText);
            infoTitle.setCatTextTFAssets(titleTextAssetsTfFileName, Typeface.BOLD);//粗体
            infoTitle.setCatTextColor(titleTextColor);
            infoTitle.setCatTextSize(9);
            infoTitle.setSingleLine(false);//允许自动换行
            infoTitle.setMaxEms(11);//一行最大文本宽度(这里用来限制一行数量) 超过将自动换行
            textLayout.addView(infoTitle);//文本布局添加标题
        }
        if (infoText != null) {
            //信息
            if (titleText != null)
                log += "-" + infoText;
            else
                log += infoText;
            AlguiViewText info = new AlguiViewText(aContext, infoText);
            info.setCatTextTFAssets(infoTextAssetsTfFileName);
            info.setCatTextColor(infoTextColor);
            info.setCatTextSize(7);
            info.setSingleLine(false);//允许自动换行
            info.setMaxEms(11);//一行最大文本宽度(这里用来限制一行数量) 超过将自动换行
            textLayout.addView(info);//文本布局添加信息
        }

        if (titleText != null || infoText != null){
            AlguiLog.i("通知",log);
            infoLayout.addView(textLayout);//主布局添加文本布局
        }
        showInfo(showTime, infoLayout);//显示通知主布局
        window.addFlag(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);//不可触摸
        return this;
    }

    //显示一个消息通知(预设通知：黑色主题 快速使用)
    public AlguiWinInform showInfo_Black(
        @Nullable String Image_Url_Base64_FilePath,//图标 null=无图标 {图片/动态图} 支持：网络图像链接，base64图像编码，本地图像文件路径，图片文件名(项目assets文件夹)
        @Nullable CharSequence titleText,//标题文本 null=无标题
        @Nullable CharSequence infoText,//信息文本 null=无信息
        int showTime//显示时间

    ) {
        showInfo(
            //通知主布局外观
            0xFF303030, //背景颜色
            0, //圆角半径
            0.5f, 0xD0FFFFFF,//描边大小颜色

            //图标外观
            Image_Url_Base64_FilePath,//图标
            0xFFFFFFFF, //颜色(只支持单色图标)
            0,//圆角半径

            //标题外观
            titleText, //文本
            0xFFFFFFFF, //文本颜色
            "lz.ttf",//文本字体

            //信息外观
            infoText, //文本
            0x60FFFFFF, //文本颜色
            "lz.ttf",//文本字体

            //显示时间(秒)
            showTime
        );
        return this;
    }

    //显示一个消息通知(预设通知：白色主题 快速使用)
    public AlguiWinInform showInfo_White(
        @Nullable String Image_Url_Base64_FilePath,//图标 null=无图标 {图片/动态图} 支持：网络图像链接，base64图像编码，本地图像文件路径，图片文件名(项目assets文件夹)
        @Nullable CharSequence titleText,//标题文本 null=无标题
        @Nullable CharSequence infoText,//信息文本 null=无信息
        int showTime//显示时间

    ) {
        showInfo(
            //通知主布局外观
            0xFFEBF2EB, //背景颜色
            0, //圆角半径
            0.5f, 0xFF000000,//描边大小颜色

            //图标外观
            Image_Url_Base64_FilePath,//图标
            0xFF000000, //颜色(只支持单色图标)
            0,//圆角半径

            //标题外观
            titleText, //文本
            0xFF000000, //文本颜色
            "lz.ttf",//文本字体

            //信息外观
            infoText, //文本
            0xFF424242, //文本颜色
            "lz.ttf",//文本字体

            //显示时间(秒)
            showTime
        );
        return this;
    }

    //显示一个消息通知(预设通知：蓝色主题 快速使用)
    public AlguiWinInform showInfo_Blue(
        @Nullable String Image_Url_Base64_FilePath,//图标 null=无图标 {图片/动态图} 支持：网络图像链接，base64图像编码，本地图像文件路径，图片文件名(项目assets文件夹)
        @Nullable CharSequence titleText,//标题文本 null=无标题
        @Nullable CharSequence infoText,//信息文本 null=无信息
        int showTime//显示时间

    ) {
        showInfo(
            //通知主布局外观
            0xFF000000, //背景颜色
            0, //圆角半径
            0.5f, 0xFFADB1B7,//描边大小颜色

            //图标外观
            Image_Url_Base64_FilePath,//图标
            0xFFADB1B7, //颜色(只支持单色图标)
            0,//圆角半径

            //标题外观
            titleText, //文本
            0xFFADB1B7, //文本颜色
            "lz.ttf",//文本字体

            //信息外观
            infoText, //文本
            0xFFADB1B7, //文本颜色
            "lz.ttf",//文本字体

            //显示时间(秒)
            showTime
        );
        return this;
    }


}
