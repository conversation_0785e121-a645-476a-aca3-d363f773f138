package com.bytecat.algui.AlguiWindows;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/20 18:12
 * @Describe Algui菜单窗口
 */
import androidx.annotation.Nullable;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.AnimatedImageDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Message;
import android.view.Gravity;
import android.view.HapticFeedbackConstants;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.Toast;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolImage;
import com.bytecat.algui.AlguiViews.AlguiFlowLayout;
import com.bytecat.algui.AlguiViews.AlguiFrameLayout;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiScrollView;
import com.bytecat.algui.AlguiViews.AlguiViewImage;
import com.bytecat.algui.AlguiViews.AlguiViewText;
import com.bytecat.algui.AlguiViews.AlguiViewTriangle;

public class AlguiWinMenu extends AlguiFlowLayout {

    public static final String TAG = "AlguiWinMenu";

    Context aContext;//活动
    AlguiWindow window;//窗口
    AlguiFrameLayout rootLayout;//窗口根布局
    AlguiViewImage ball;//悬浮球
    AlguiLinearLayout menu;//菜单
    AlguiLinearLayout menuTop;//菜单顶部
    AlguiFlowLayout menuNav;//菜单顶部导航栏布局(默认没有，仅占位，只有外部需要往此布局添加视图时才显示)
    AlguiViewImage menuIcon;//菜单图标
    AlguiViewText menuTitle;//菜单标题
    AlguiViewImage menuEndIcon;//菜单关闭图标
    AlguiScrollView menuList;//菜单滚动列表
    AlguiLinearLayout menuBottom;//菜单底部
    AlguiViewTriangle menuTriangle;//菜单三角视图 (菜单大小调节器)
    public AlguiWindow getByteWindow() { return window; } 
    public AlguiFrameLayout getByteRootLayout() { return rootLayout; } 
    public AlguiViewImage getByteBallLayout() { return ball; } 
    public AlguiLinearLayout getByteMenuLayout() { return menu; } 
    public AlguiLinearLayout getByteMenuTopLayout() { return menuTop; } 
    public AlguiFlowLayout getByteMenuNavLayout() { return menuNav; } 
    public AlguiViewImage getByteMenuIcon() { return menuIcon; } 
    public AlguiViewText getByteMenuTitle() { return menuTitle; } 
    public AlguiViewImage getByteMenuEndIcon() { return menuEndIcon; } 
    public AlguiScrollView getByteMenuList() { return menuList; } 
    public AlguiLinearLayout getByteMenuBottomLayout() { return menuBottom; } 
    public AlguiViewTriangle getByteMenuTriangle() { return menuTriangle; }


    float winTransparent=1;//窗口透明度
    boolean isEnableWinMove=true;//是否启用窗口移动
    AlguiCallback.Click callback_MenuOpen;//回调菜单打开关闭监听
    boolean isEnableMenuSize=true;//是否启用菜单大小调节器
    boolean isEnableMenuOutsideEnd=true;//是否启用点击菜单外部可关闭菜单
    boolean isEnableTitle=true;//是否启用菜单标题栏
    boolean isShowMenu;//是否正在显示菜单
    float menuMinWidth,menuMinHeight;//菜单最小宽高
    float menuWidth,menuHeight;//菜单当前宽高
    public float getByteWinTransparent() { return winTransparent; }
    public boolean isEnableWinMove() { return isEnableWinMove; }
    public AlguiCallback.Click getByteMenuOpenCallback() { return callback_MenuOpen;}
    public boolean isEnableMenuSize() { return isEnableMenuSize; }
    public boolean isEnableMenuOutsideEnd() { return isEnableMenuOutsideEnd; }
    public boolean isShowMenu() { return isShowMenu; }
    public float getByteMenuMinWidth() { return menuMinWidth; }
    public float getByteMenuMinHeight() { return menuMinHeight; }
    public float getByteMenuWidth() { return menuWidth; }
    public float getByteMenuHeight() { return menuHeight; }



    //------------------------------悬浮球----------------------------------------------------
    //设置悬浮球图像
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiWinMenu setCatBallImage(String Url_Base64_FilePath) {
        ball.setCatImage(Url_Base64_FilePath);
        return this;
    }
    //设置悬浮球大小
    public AlguiWinMenu setCatBallSize(int width, int height) {
        ball.setCatSize(width, height);
        return this;
    }
    //设置悬浮球透明度0-255
    public AlguiWinMenu setCatBallTransparency(int t0_255) {
        ball.setCatTransparent(t0_255);
        return this;
    }
    //设置悬浮球颜色(对于单色图像)
    public AlguiWinMenu setCatBallColor(int color) {
        ball.setCatColor(color);
        return this;
    }
    //设置悬浮球毛玻璃模糊
    public AlguiWinMenu setCatBallBlur(int radius) {
        ball.setCatBlur(radius);
        return this;
    }
    //设置悬浮球圆角
    public AlguiWinMenu setCatBallRadiu(float r) {
        ball.setCatRadiu(r); 
        return this;
    }



    //------------------------------悬浮菜单----------------------------------------------------
    //设置菜单打开关闭监听
    public AlguiWinMenu setCatMenuOpenCallback(AlguiCallback.Click c) {
        callback_MenuOpen = c;
        return this;
    }

    //设置菜单最小大小
    public AlguiWinMenu setCatMenuMinSize(float w, float h) {
        float wPX = dp2px(w);
        float hPX = dp2px(h);
        menuMinWidth = wPX;menuMinHeight = hPX;
        if (menuWidth < menuMinWidth) {
            menuWidth = menuMinWidth;
            window.getByteWindowParams().width = (int)menuWidth;
            updateWin();
        }
        if (menuHeight < menuMinHeight) {
            menuHeight = menuMinHeight;
            window.getByteWindowParams().height = (int)menuHeight;
            updateWin();
        }
        return this;
    }

    //设置菜单大小
    public AlguiWinMenu setCatMenuSize(float w, float h) {
        float wPX = dp2px(w);
        float hPX = dp2px(h);
        if (wPX < menuMinWidth)
            wPX = menuMinWidth;
        if (hPX < menuMinHeight)
            hPX = menuMinHeight;
        menuWidth = wPX ;menuHeight = hPX;
        //在显示悬浮菜单时才应用
        if (isShowMenu)
            window.setSizePX((int)menuWidth, (int)menuHeight);//应用宽高
        return this;
    }
    //设置菜单是否启用标题栏
    public AlguiWinMenu setCatEnableMenuTitle(boolean isEnableTitle) {
        this.isEnableTitle = isEnableTitle;
        if (isEnableTitle) {
            menuTop.setVisibility(View.VISIBLE);
        } else {
            menuTop.setVisibility(View.GONE);
        }
        return this;
    }
    //设置是否启用菜单大小调节器
    public AlguiWinMenu setCatEnableMenuSize(boolean isEnableMenuSize) {
        this.isEnableMenuSize = isEnableMenuSize;
        if (isEnableMenuSize) {
            menuTriangle.setVisibility(View.VISIBLE);
        } else {
            menuTriangle.setVisibility(View.GONE);
        }
        return this;
    }
    //设置是否启用点击菜单外部可关闭菜单
    public AlguiWinMenu setCatEnableMenuOutsideEnd(boolean isEnableMenuOutsideEnd) {
        this.isEnableMenuOutsideEnd = isEnableMenuOutsideEnd;
        if (isEnableMenuOutsideEnd) {
            addWinFlag(WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH);//监听外部触摸
        } else {
            remWinFlag(WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH);//移除外部触摸监听
        }
        return this;
    }

    //设置菜单背景图片
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiWinMenu setCatMenuBackImage(@Nullable final String Url_Base64_FilePath, final int blurRadius, final int Transparent0_255) {
        if (Url_Base64_FilePath != null) {
            Drawable image = AlguiToolImage.getImage(aContext, Url_Base64_FilePath, new AlguiCallback.Web(){
                    //对于网络图像
                    @Override
                    public void web(Message msg) {
                        switch (msg.what) {
                            case 200:
                                Object obj = msg.obj;
                                if (obj != null) {
                                    if (obj instanceof Drawable) {
                                        Drawable image = (Drawable)msg.obj;
                                        //对于需要毛玻璃模糊
                                        if (blurRadius > 0)
                                            image = AlguiToolImage.psImageBlur(image, blurRadius, aContext);
                                        //对于需要透明度
                                        if (Transparent0_255 >= 0)
                                            image.setAlpha(Transparent0_255);
                                        image.setAlpha(Transparent0_255);
                                        menuList.setBackground(image);
                                    }
                                }
                                break;
                            case 404:
                                Toast.makeText(getContext(), "网络图片加载失败：服务器发生错误", Toast.LENGTH_SHORT).show();
                                break;
                            case 651:
                                Toast.makeText(getContext(), "网络图片加载失败：图片异常", Toast.LENGTH_SHORT).show();
                                break;
                        }
                    }
                });
            //对于其它
            if (image != null) {
                //对于需要毛玻璃模糊
                if (blurRadius > 0)
                    image = AlguiToolImage.psImageBlur(image, blurRadius, aContext);
                //对于需要透明度
                if (Transparent0_255 >= 0)
                    image.setAlpha(Transparent0_255);
                menuList.setBackground(image);
            }
        } else {
            menuList.setBackground(null);
        }
        return this;
    }
    //______________________菜单布局______________________
    //设置菜单透明度0-1
    public AlguiWinMenu setCatMenuTransparent(float t0_1) {
        menu.setAlpha(t0_1);
        return this;
    }
    //设置菜单圆角半径
    public AlguiWinMenu setCatMenuRadiu(float radiu) {
        menu.setCatRadiu(radiu);
        return this;
    }
    //设置菜单描边
    public AlguiWinMenu setCatMenuBorder(float borderSize, int borderColor) {
        menu.setCatBorder(borderSize, borderColor);
        return this;
    }
    //设置菜单背景颜色
    public AlguiWinMenu setCatMenuBackColor(int... color) {
        menu.setCatBackColor(color);
        return this;
    }
    //______________________菜单顶部布局______________________ 700x156
    //设置菜单顶部布局背景图片 (设置后圆角描边背景颜色失效，null恢复)
    //参数：图片资源，毛玻璃模糊半径，透明度@0-255
    //支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    //格式：png，jpg，gif…
    public AlguiWinMenu setCatMenuTopBackImage(@Nullable final String Url_Base64_FilePath, final int blurRadius, final int Transparent0_255) {
        if (Url_Base64_FilePath == null) {
            menuTop.setBackground(menuTop.getByteBack());
            return this;
        }
        //确保在布局计算完成后才设置
        menuTop.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                Drawable menuTopBackIMG;//菜单顶部布局背景图片
                public void update() {
                    int layoutWidth = menuTop.getWidth();
                    int layoutHeight = menuTop.getHeight();
                    AlguiLog.d(TAG,"%d %d",layoutWidth,layoutHeight);
                    if (menuTopBackIMG != null) {
                        //如果是GIF图片直接设置
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P&&menuTopBackIMG instanceof AnimatedImageDrawable) {
                            menuTop.setBackground((AnimatedImageDrawable) menuTopBackIMG);
                        } else {
                            //对于需要毛玻璃模糊
                            if (blurRadius > 0)
                                menuTopBackIMG = AlguiToolImage.psImageBlur(menuTopBackIMG, blurRadius, aContext);
                            //对于非GIF图片进行缩放处理，图片宽超出布局宽则缩放到布局宽然后裁剪掉多余的高度部分
                            Bitmap originalBitmap = AlguiToolImage.drawableToBitmap(menuTopBackIMG);
                            int originalWidth = originalBitmap.getWidth();
                            int originalHeight = originalBitmap.getHeight();

                            // 图片与布局宽高一致直接设置
                            if (layoutWidth == originalWidth && originalHeight == layoutHeight) {
                                menuTop.setBackground(menuTopBackIMG);
                            } else {
                                //否则将图片宽度缩放为与布局宽度一致然后裁剪掉高度多余的部分
                                //计算缩放比例
                                float scale = (float) layoutWidth / originalWidth;
                                int scaledWidth = layoutWidth;
                                int scaledHeight = (int) (originalHeight * scale);

                                //缩放图片
                                Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, scaledWidth, scaledHeight, true);

                                //如果缩放后的高度超出布局高度则裁剪掉超出的高度部分
                                if (scaledHeight > layoutHeight) {
                                    //计算裁剪区域
                                    int cropHeight = scaledHeight - layoutHeight;
                                    int cropStartY = cropHeight / 2;
                                    Bitmap croppedBitmap = Bitmap.createBitmap(scaledBitmap, 0, cropStartY, scaledWidth, layoutHeight);
                                    menuTopBackIMG = new BitmapDrawable(getResources(), croppedBitmap);
                                    menuTop.setBackground(menuTopBackIMG);
                                } else {
                                    //设置缩放后的图片为布局背景
                                    menuTopBackIMG = new BitmapDrawable(getResources(), scaledBitmap);
                                    menuTop.setBackground(menuTopBackIMG);
                                }
                            }
                        }
                        if (Transparent0_255 >= 0)
                            menuTopBackIMG.setAlpha(Transparent0_255);
                    }
                }
                @Override
                public void onGlobalLayout() {
                    //移除监听器以避免重复调用
                    menuTop.getViewTreeObserver().removeOnGlobalLayoutListener(this);               
                    menuTopBackIMG = AlguiToolImage.getImage(aContext, Url_Base64_FilePath, new AlguiCallback.Web(){
                            //对于网络图像
                            @Override
                            public void web(Message msg) {
                                switch (msg.what) {
                                    case 200:
                                        Object obj = msg.obj;
                                        if (obj != null) {
                                            if (obj instanceof Drawable) {
                                                menuTopBackIMG = (Drawable)msg.obj;
                                                update();
                                            }
                                        }
                                        break;
                                    case 404:
                                        Toast.makeText(getContext(), "网络图片加载失败：服务器发生错误", Toast.LENGTH_SHORT).show();
                                        break;
                                    case 651:
                                        Toast.makeText(getContext(), "网络图片加载失败：图片异常", Toast.LENGTH_SHORT).show();
                                        break;
                                }
                            }
                        });
                    update();
                }
            });
        return this;
    }
    //设置菜单顶部布局内边距
    public AlguiWinMenu setCatMenuTopPadding(float left, float top, float right, float bottom) {
        menuTop.setCatPadding(left, top, right, bottom);
        return this;
    }
    //设置菜单顶部布局圆角半径
    public AlguiWinMenu setCatMenuTopRadiu(float radiu) {
        menuTop.setCatRadiu(radiu);
        return this;
    }
    //设置菜单顶部布局描边
    public AlguiWinMenu setCatMenuTopBorder(float borderSize, int borderColor) {
        menuTop.setCatBorder(borderSize, borderColor);
        return this;
    }
    //设置菜单顶部布局背景颜色
    public AlguiWinMenu setCatMenuTopBackColor(int... color) {
        menuTop.setCatBackColor(color);
        return this;
    }

    //______________________菜单图标______________________
    //设置菜单图标图像
    // 支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    // 格式：png，jpg，gif…
    public AlguiWinMenu setCatMenuIconImage(@Nullable String Url_Base64_FilePath) {
        if (Url_Base64_FilePath == null) {
            menuIcon.setVisibility(View.GONE);
        } else {
            menuIcon.setCatImage(Url_Base64_FilePath);
            menuIcon.setVisibility(View.VISIBLE);
        }
        return this;
    }
    //设置菜单图标颜色(对于单色图像)
    public AlguiWinMenu setCatMenuIconColor(int color) {
        menuIcon.setCatColor(color);
        return this;
    }
    //设置菜单图标毛玻璃模糊
    public AlguiWinMenu setCatMenuIconBlur(int radius) {
        menuIcon.setCatBlur(radius);
        return this;
    }
    //设置菜单图标圆角
    public AlguiWinMenu setCatMenuIconRadiu(float r) {
        menuIcon.setCatRadiu(r); 
        return this;
    }

    //______________________菜单标题______________________
    //设置标题
    public AlguiWinMenu setCatMenuTitle(@Nullable CharSequence textstr, Object... args) {
        if (menuTitle != null) {
            menuTitle.setCatText(textstr, args);
            menuTitle.setVisibility(View.VISIBLE);
        } else {
            menuTitle.setVisibility(View.GONE);
        }
        return this;
    }
    //设置标题颜色
    public AlguiWinMenu setCatMenuTitleColor(int... color) {
        menuTitle.setCatTextColor(color);
        return this;
    }
    //设置标题动态渐变效果启动状态
    public AlguiWinMenu setCatMenuTitleMoveGrad(boolean b) {
        menuTitle.setCatTextMoveGrad(b);
        return this;
    }
    //设置标题发光
    public AlguiWinMenu setCatMenuTitleGlow(float radius, int color) {
        menuTitle.setCatTextGlow(radius, color);
        return this;
    }
    //设置标题字体 (Assets文件夹下的字体文件名)
    public AlguiWinMenu setCatMenuTitleTFAssets(String assetsTfFileName) {
        menuTitle.setCatTextTFAssets(assetsTfFileName, Typeface.BOLD);
        return this;
    }//重载+样式
    public AlguiWinMenu setCatMenuTitleTFAssets(String assetsTfFileName, int style) {
        menuTitle.setCatTextTFAssets(assetsTfFileName, style);
        return this;
    }
    //设置标题大小
    public AlguiWinMenu setCatMenuTitleSize(float size) {
        menuTitle.setCatTextSize(size);//设置文本大小
        float iconSize = size * 1.3f;//图标是标题的0.3倍
        menuIcon.setCatSize(iconSize, iconSize);//菜单图标
        //float endIconSize = size*1.1f;//关闭图标是标题的0.1倍
        menuEndIcon.setCatSize(size, size);//关闭图标
        return this;
    }
    //______________________菜单关闭图标______________________

    //设置菜单关闭图标
    // 支持：网络图像链接，base64图像编码，本地图像文件，图片文件名(项目assets文件夹)
    // 格式：png，jpg，gif…
    public AlguiWinMenu setCatMenuEndIconImage(@Nullable String Url_Base64_FilePath) {
        if (Url_Base64_FilePath == null) {
            menuEndIcon.setVisibility(View.GONE);
        } else {
            menuEndIcon.setCatImage(Url_Base64_FilePath);
            menuEndIcon.setVisibility(View.VISIBLE);
        }
        return this;
    }
    //设置菜单关闭图标颜色(对于单色图像)
    public AlguiWinMenu setCatMenuEndIconColor(int color) {
        menuEndIcon.setCatColor(color);
        return this;
    }
    //设置菜单关闭图标毛玻璃模糊
    public AlguiWinMenu setCatMenuEndIconBlur(int radius) {
        menuEndIcon.setCatBlur(radius);
        return this;
    }
    //设置菜单关闭图标圆角
    public AlguiWinMenu setCatMenuEndIconRadiu(float r) {
        menuEndIcon.setCatRadiu(r); 
        return this;
    }

    //------------------------------菜单顶部导航区----------------------------------------------------
    //设置导航区方向
    public AlguiWinMenu setCatMenuNavOrientation(int orientation) {
        if (menuNav != null) {
            menuNav.setCatOrientation(orientation);
        }
        return this;
    }
    //设置菜单导航区内边距
    public AlguiWinMenu setCatMenuNavPadding(float left, float top, float right, float bottom) {
        menuNav.setCatPadding(left, top, right, bottom);
        return this;
    }
    //设置菜单导航区背景颜色
    public AlguiWinMenu setCatMenuNavBackColor(int... backColor) {
        menuNav.setCatBackColor(backColor);
        return this;
    }
    //设置菜单导航区圆角半径
    public AlguiWinMenu setCatMenuNavRadiu(float radiu) {
        menuNav.setCatRadiu(radiu);
        return this;
    }
    //设置菜单导航区描边
    public AlguiWinMenu setCatMenuNavBorder(float borderSize, int borderColor) {
        menuNav.setCatBorder(borderSize, borderColor);
        return this;
    }
    //设置菜单导航区一行最大视图数量 自动换行
    public AlguiWinMenu setCatMenuNavLineMaxView(int num) {
        menuNav.setCatLineMaxView(num);
        return this;
    }
    //设置菜单导航所有行的外边距
    public AlguiWinMenu setCatMenuNavLineMargins(float left, float top, float right, float bottom) {
        menuNav.setCatLineMargins(left, top, right, bottom);
        return this;
    }
    //菜单导航区 手动换行
    public AlguiLinearLayout endl_Nav() {
        return menuNav.endl();
    }
    //在菜单导航区 添加一些视图 中途支持换行
    public AlguiWinMenu addView_Nav(View... view) {
        menuNav.addView(view);
        return this;
    }
    //删除菜单导航区子视图
    public AlguiWinMenu remView_Nav(View... view) {
        menuNav.remView(view);
        return this;
    }
    //删除菜单导航区所有子视图
    public AlguiWinMenu remAllView_Nav() {
        menuNav.removeAllViews();
        return this;
    }
    //删除导航区指定行的视图
    public AlguiWinMenu remViewToLine_Nav(int index, View... views) {
        if (menuNav != null) {
            menuNav.remViewToLine(index, views);
        }
        return this;
    }
    //删除导航区指定行 (按索引)
    public AlguiWinMenu remLine_Nav(int... indexs) {
        if (menuNav != null) {
            menuNav.remLine(indexs);
        }
        return this;
    }
    //删除导航区指定行 (按对象)
    public AlguiWinMenu remLine_Nav(AlguiLinearLayout... objs) {
        if (menuNav != null) {
            menuNav.remLine(objs);
        }
        return this;
    }
    //添加视图到导航区的指定行
    public AlguiWinMenu addViewToLine_Nav(int index, View... views) {
        if (menuNav != null) {
            menuNav.addViewToLine(index, views);
        }
        return this;
    }

    //______________________菜单滚动列表______________________
    //设置滚动列表背景颜色
    public AlguiWinMenu setCatMenuListBackColor(int backColor) {
        menuList.setBackgroundColor(backColor);
        return this;
    }

    //______________________菜单底部布局______________________
    //设置菜单底部布局圆角半径
    public AlguiWinMenu setCatMenuBottomRadiu(float radiu) {
        menuBottom.setCatRadiu(radiu);
        return this;
    }
    //设置菜单底部布局描边
    public AlguiWinMenu setCatMenuBottomBorder(float borderSize, int borderColor) {
        menuBottom.setCatBorder(borderSize, borderColor);
        return this;
    }
    //设置菜单底部布局背景颜色
    public AlguiWinMenu setCatMenuBottomBackColor(int... color) {
        menuBottom.setCatBackColor(color);
        return this;
    }

    //______________________菜单右下角直角三角形______________________
    //设置菜单直角颜色
    public AlguiWinMenu setCatMenuTriangleColor(int color) {
        menuTriangle.setCatColor(color);
        return this;
    }
    //设置菜单直角大小
    public AlguiWinMenu setCatMenuTriangleSize(float size) {
        menuTriangle.setCatSize(size);
        return this;
    }




    //------------------------------菜单行缓冲区----------------------------------------------------
    //设置菜单缓冲区方向
    public AlguiWinMenu setCatMenuBufferOrientation(int orientation) {
        super.setCatOrientation(orientation);
        return this;
    }
    //设置菜单缓冲区内边距
    public AlguiWinMenu setCatMenuBufferPadding(float left, float top, float right, float bottom) {
        super.setCatPadding(left, top,  right,  bottom);
        return this;
    }
    //设置菜单缓冲区背景颜色
    public AlguiWinMenu setCatMenuBufferBackColor(int... backColor) {
        super.setCatBackColor(backColor);
        return this;
    }
    //设置菜单缓冲区圆角半径
    public AlguiWinMenu setCatMenuBufferRadiu(float radiu) {
        super.setCatRadiu(radiu);
        return this;
    }
    //设置菜单缓冲区描边
    public AlguiWinMenu setCatMenuBufferBorder(float borderSize, int borderColor) {
        super.setCatBorder(borderSize, borderColor);
        return this;

    }
    //设置菜单缓冲区一行最大视图数量 自动换行
    public AlguiWinMenu setCatMenuBufferLineMaxView(int num) {
        super.setCatLineMaxView(num);
        return this;
    }
    //设置菜单缓冲区所有行的外边距
    public AlguiWinMenu setCatMenuBufferLineMargins(float left, float top, float right, float bottom) {
        super.setCatLineMargins(left, top, right, bottom);
        return this;
    }

    //缓冲区 手动换行
    public AlguiLinearLayout endl() {
        return super.endl();
    }
    //在缓冲区 添加一些视图 中途null代表换行
    public AlguiWinMenu addView(View... view) {
        super.addView(view);
        return this;
    }
    //删除缓冲区子视图
    public AlguiWinMenu remView(View... view) {
        super.remView(view);
        return this;
    }
    //删除缓冲区所有子视图
    public AlguiWinMenu remAllView() {
        super.remAllView();
        return this;
    }
    //删除缓冲区指定行的视图
    public AlguiWinMenu remViewToLine(int index, View... views) {
        super.remViewToLine(index, views);
        return this;
    }
    //删除缓冲区指定行 (按索引)
    public AlguiWinMenu remLine(int... indexs) {
        super.remLine(indexs);
        return this;
    }
    //删除缓冲区指定行 (按对象)
    public AlguiWinMenu remLine(AlguiLinearLayout... objs) {
        super.remLine(objs);
        return this;
    }
    //添加视图到缓冲区的指定行
    public AlguiWinMenu addViewToLine(int index, View... views) {
        super.addViewToLine(index, views);
        return this;
    }




    //------------------------------主窗口----------------------------------------------------
    //设置窗口显示在哪个活动中
    //如果传入Activity则仅悬浮显示在这一个活动中(无需悬浮窗权限)
    //传入后台Context则全局悬浮显示(需悬浮窗权限)
    public AlguiWinMenu setCatWinActivity(Context context) {
        if (context != null) {
            aContext = context;
            window.setCatActivity(context);
        }
        return this;
    }
    //显示窗口
    public AlguiWinMenu showWin() {
        window.show();
        return this;
    }
    //更新窗口
    public AlguiWinMenu updateWin() {    
        window.update();
        return this;
    }
    //隐藏窗口
    public AlguiWinMenu hideWin() {
        setCatWinView(null);
        window.hide();
        return this;
    }
    //设置窗口布局视图 (只能容纳一个视图)
    public AlguiWinMenu setCatWinView(View view) {
        rootLayout.remAllView();//清除窗口根布局所有视图
        if (view != null) 
            rootLayout.addView(view);
        showWin();
        return this;
    }
    //设置一些窗口特性
    public AlguiWinMenu setCatWinFlags(int flags) {
        window.setCatFlags(flags);
        return this;
    }
    //添加一个窗口特性
    public AlguiWinMenu addWinFlag(int flag) {
        window.addFlag(flag);
        return this;
    }
    //移除一个窗口特性
    public AlguiWinMenu remWinFlag(int flag) {
        window.remFlag(flag);
        return this;
    }
    //设置是否启用窗口动态移动
    public AlguiWinMenu setCatEnableWinMove(boolean isEnableWinMove) {
        this.isEnableWinMove = isEnableWinMove;
        return this;
    }
    //设置窗口在屏幕上的xy位置 (相对左上角原点xy偏移)
    public AlguiWinMenu setCatWinPos(float x, float y) {
        window.setCatPos(x, y);
        return this;
    }
    //设置窗口亮度0-1
    public AlguiWinMenu setCatWinBrightness(float b0_1) {
        window.setCatBrightness(b0_1);
        return this;
    }
    //设置窗口透明度0-1
    public AlguiWinMenu setCatWinTransparent(float t0_1) {
        window.setCatTransparent(t0_1);
        winTransparent = t0_1;
        return this;
    }
    //设置窗口启动退出动画
    public AlguiWinMenu setCatWinAnimations(int animID) {
        window.setCatAnimations(animID);
        return this;
    }
    //显示悬浮球
    public AlguiWinMenu showBall() {
        //窗口添加无法获取输入焦点的flag 防止显示悬浮球时窗口之外无法弹出输入法
        addWinFlag(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        //窗口移除允许显示在屏幕之外的flag
        remWinFlag(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        //只有悬浮球根布局未添加时才添加
        if (rootLayout.indexOfChild(ball) == -1) {
            //窗口宽高跟随悬浮球大小
            window.setSizePX(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
            setCatWinView(ball);
        }
        isShowMenu = false;
        if (callback_MenuOpen != null)
            callback_MenuOpen.click(isShowMenu);
        return this;
    }
    //显示悬浮菜单
    public AlguiWinMenu showMenu() {
        //窗口添加允许显示在屏幕之外的flag
        addWinFlag(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        //窗口移除无法获取输入焦点的flag 防止菜单输入框无法弹出输入法 但这会使窗口之外无法弹出输入法
        remWinFlag(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        //只有菜单根布局未添加时才添加
        if (rootLayout.indexOfChild(menu) == -1) {
            //窗口宽高是上次的菜单宽高
            window.setSizePX((int)menuWidth, (int)menuHeight);
            setCatWinView(menu);
        }
        isShowMenu = true;
        if (callback_MenuOpen != null)
            callback_MenuOpen.click(isShowMenu);
        return this;
    }



    //------------------------------初始化----------------------------------------------------
    public AlguiWinMenu(Context context) {
        super(context);
        aContext = context;

        initWindow();//初始化窗口
        initBallLayout();//初始化悬浮球
        initMenu();//初始化菜单

        setCatWinActivity(aContext);//设置窗口显示
        setCatMenuMinSize(100, 100);//设置最小宽高
        setCatMenuSize(200, 200);//设置宽高
        setCatWinAnimations(AlguiWindow.Animations.Animation_Translucent);//启动退出动画
    }
    public AlguiWinMenu(Context context, CharSequence title) {
        this(context);
        setCatMenuTitle(title);
    }

    //初始化窗口
    private void initWindow() {
        //窗口
        window = new AlguiWindow(aContext);
        //设置特性
        window.setCatFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED//硬件加速
            | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL//不阻止其他窗口接收触摸事件
            | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH//监听窗口外部触摸
            | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE//无法获取输入框焦点
            | WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS//允许显示在屏幕之外
        );

        //窗口根布局
        rootLayout = new AlguiFrameLayout(aContext);
        rootLayout.setCatBackColor(Color.TRANSPARENT);
        rootLayout.setLayerType(View.LAYER_TYPE_HARDWARE, null) ;//开启硬件加速减少延迟
        window.setCatView(rootLayout);//设置窗口根布局

        //设置窗口触摸移动事件
        rootLayout.setOnTouchListener(
            new OnTouchListener() {
                private int signX;
                private int signY;
                private float downX;
                private float downY;
                private float moveX;
                private float moveY;
                boolean isOne=true;//第一次移动
                boolean isMove=false;//当前是否在移动
                int moveThreshold=20;//手指移动的阀值 (灵敏度) 改小更容易触发移动 太小可能导致误判打不开悬浮窗
                @Override
                public boolean onTouch(View view, MotionEvent event) {

                    switch (event.getActionMasked()) {
                            //点击了窗口之外的区域时触发
                        case MotionEvent.ACTION_OUTSIDE:
                            if (isEnableMenuOutsideEnd) {
                                //正在显示菜单则显示悬浮球
                                if (isShowMenu) 
                                    showBall();
                            }
                            return true;
                            //手指按下时触发
                        case MotionEvent.ACTION_DOWN:           
                            isMove = false;

                            if (isEnableWinMove) {
                                isOne = true;
                                signX = window.getByteWindowParams().x;//记录窗口初始位置的横向坐标
                                signY = window.getByteWindowParams().y;//记录窗口初始位置的竖向坐标
                                downX = event.getRawX();//记录手指按下时的绝对横向坐标
                                downY = event.getRawY();//记录手指按下时的绝对竖向坐标
                            }

                            return true;
                            //手指移动时触发
                        case MotionEvent.ACTION_MOVE:
                            if (isEnableWinMove) {
                                float moveDistanceX = Math.abs(event.getRawX() - downX);
                                float moveDistanceY = Math.abs(event.getRawY() - downY);
                                if (moveDistanceX > moveThreshold || moveDistanceY > moveThreshold) {
                                    isMove = true;//当前是移动
                                }
                                if (isMove) {
                                    //第一次移动执行的内容
                                    if (isOne) {
                                        rootLayout.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS);//窗口进行移动触觉振动反馈
                                        window.setCatTransparent(winTransparent / 2);
                                        isOne = false;//不是第一次移动了
                                    }
                                    window.getByteWindowParams().x = signX + (int) (event.getRawX() - downX);//根据手指移动的距离计算窗口新的横向坐标
                                    window.getByteWindowParams().y = signY + (int) (event.getRawY() - downY);//根据手指移动的距离计算窗口新的竖向坐标
                                    window.update();//更新窗口位置
                                }
                            }

                            return true;
                            //手指抬起时触发
                        case MotionEvent.ACTION_UP:
                            if (!isMove) {
                                //不是移动状态 而是点击抬起的
                                //正在显示悬浮球则显示菜单
                                if (!isShowMenu) {
                                    //AlguiAudioBase64.play(AlguiAudioBase64.Window_on);//播放菜单显示音频
                                    showMenu();//显示菜单
                                }
                            } else {
                                window.setCatTransparent(winTransparent);
                            }
                            return true;
                    }
                    return false;
                }
            }
        );
    }
    //初始化悬浮球
    private void initBallLayout() {
        ball = new AlguiViewImage(aContext)
            .setCatColor(0xff294A7A)
            .setCatSize(40, 40)
            ;
        //默认使用当前应用图标
        try {
            String packageName = aContext.getPackageName();//获取当前应用的包名
            PackageManager packageManager = aContext.getPackageManager();//获取包管理器
            Drawable appIcon = packageManager.getApplicationIcon(packageName);//获取应用的图标
            if (appIcon != null)
                ball.setCatDrawable(appIcon);//使用当前应用图标
            else
                ball.setCatImage(AlguiAssets.Icon.round);//使用圆

        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }
    //初始化菜单
    private void initMenu() {
        //菜单根布局
        menu = new AlguiLinearLayout(aContext);
        menu.setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        menu.setOrientation(LinearLayout.VERTICAL);
        menu.setCatBorder(0.4f, 0xFF424242);
        menu.setCatBackColor(0xff151617);
        menu.setAlpha(1f);

        //顶部布局
        menuTop = new AlguiLinearLayout(aContext);
        menuTop.setCatSize(
            LinearLayout.LayoutParams.MATCH_PARENT, 
            LinearLayout.LayoutParams.WRAP_CONTENT);
        menuTop.setOrientation(LinearLayout.HORIZONTAL);
        menuTop.setGravity(Gravity.CENTER_VERTICAL);
        menuTop.setCatBackColor(0xff294A7A);
        menuTop.setCatPadding(2, 2, 2, 2);

        //顶部导航栏布局(默认没有，仅先占位，只有外部需要时往此布局添加视图时才显示)
        menuNav = new AlguiFlowLayout(aContext);
        menuNav.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        menuNav.setCatWeight(0);
        menuNav.setCatBackColor(0xff151617);

        //图标
        menuIcon = new AlguiViewImage(aContext);
        menuIcon.setCatMargins(0, 0, 2, 0);
        menuIcon.setCatSize(10, 10);
        menuIcon.setCatColor(0xFFFFFFFF);
        menuIcon.setVisibility(View.GONE);//默认隐藏
        //默认使用当前应用图标
        /*try {
         String packageName = aContext.getPackageName();//获取当前应用的包名
         PackageManager packageManager = aContext.getPackageManager();//获取包管理器
         Drawable appIcon = packageManager.getApplicationIcon(packageName);//获取应用的图标
         if (appIcon != null)
         menuIcon.setCatDrawable(appIcon);//使用当前应用图标
         else
         menuIcon.setCatImage(AlguiAssets.Icon.round);//使用圆

         } catch (PackageManager.NameNotFoundException e) {
         e.printStackTrace();
         }*/




        //标题
        menuTitle = new AlguiViewText(aContext);
        menuTitle.setCatSize(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        menuTitle.setCatWeight(1);
        menuTitle.setCatMargins(2, 0, 0, 0);
        menuTitle.setCatText(TAG);
        menuTitle.setCatTextTFAssets("lz.ttf", Typeface.BOLD); //粗体
        menuTitle.setCatTextSize(7);
        menuTitle.setCatTextColor(0xFFFFFFFF);

        //关闭图标
        menuEndIcon = new AlguiViewImage(aContext, AlguiAssets.Icon.fork);
        menuEndIcon.setCatMargins(0, 0, 2, 0);
        menuEndIcon.setCatSize(7, 7);
        menuEndIcon.setCatColor(0xFFFFFFFF);
        menuEndIcon.setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    showBall();
                }
            }
        );

        //滚动列表
        menuList = new AlguiScrollView(aContext);
        menuList.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                                                               LinearLayout.LayoutParams.MATCH_PARENT, 1f));

        //底部布局
        menuBottom = new AlguiLinearLayout(aContext);
        menuBottom.setCatSize(
            LinearLayout.LayoutParams.MATCH_PARENT, 
            LinearLayout.LayoutParams.WRAP_CONTENT);
        menuBottom.setOrientation(LinearLayout.HORIZONTAL);//横向
        menuBottom.setGravity(Gravity.CENTER_VERTICAL | Gravity.END);


        //三角形 (窗口大小调节器)
        menuTriangle = new AlguiViewTriangle(aContext);
        menuTriangle.setCatSize(13);//大小
        menuTriangle.setCatColor(0xff1E3045);//颜色
        menuTriangle.setCatType(AlguiViewTriangle.Type.RIGHT_ANGLE_RIGHT_BOTTOM);//类型为右下直角

        //菜单三角形触摸监听 调整窗口大小
        menuTriangle.setOnTouchListener(new View.OnTouchListener(){

                //用于存储手指按下时的坐标
                private float downInitX, downInitY;
                //用于存储手指按下时窗口的宽高
                private float winW,winH;
                private LinearLayout.LayoutParams menuRootLayoutParams;
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                            //手指按下时触发
                        case MotionEvent.ACTION_DOWN:
                            //记录现在手指按下时的初始坐标
                            downInitX = event.getRawX();
                            downInitY = event.getRawY();
                            //记录窗口按下时的宽高
                            winW = window.getByteWindowParams().width;
                            winH = window.getByteWindowParams().height;
                            return true;
                            //手指移动时触发
                        case MotionEvent.ACTION_MOVE:
                            //计算现在移动时坐标相对于手指按下时坐标的偏移量 现在移动时的手指坐标减去手指按下时的坐标=偏移量(移动距离)
                            int offsetX = (int) (event.getRawX() - downInitX);
                            int offsetY = (int) (event.getRawY() - downInitY);

                            //为菜单布局设置新的宽高 参数1为最小宽高 新的宽高：初始宽高 + 手指移动后的偏移量 = 新的宽高 
                            menuWidth = Math.max(menuMinWidth, winW + offsetX);
                            menuHeight = Math.max(menuMinHeight, winH + offsetY);
                            window.setSizePX((int)menuWidth, (int)menuHeight);//更新大小
                            //debug
                            /* debugText.setCatText("initW：" + winW + " | " + "initH：" + winH + "\n"
                             + "width：" + menuWidth + " | " + "height：" + menuHeight
                             );*/
                            return true;
                            //手指抬起时触发
                        case MotionEvent.ACTION_UP:

                            return true;
                    }
                    return false;
                }
            });

        //顶部布局
        menuTop.addView(menuIcon, menuTitle, menuEndIcon);
        //滚动列表
        menuList.addView(this);
        //底部布局
        menuBottom.addView(menuTriangle);

        //构造菜单整体布局
        menu.addView(menuTop);
        menu.addView(menuNav);
        menu.addView(menuList);
        menu.addView(menuBottom);

    }


    //导航
    /*private Map<String, AlguiFlowLayout> titleLayouts = new HashMap<>();

     //添加导航
     public AlguiWinMenu addNav(String... titles) {
     for (String title : titles) {
     if (title != null && !title.isEmpty()) {
     if (!titleLayouts.containsKey(title)) {
     AlguiButton button = new AlguiButton(aContext, title);

     final AlguiFlowLayout layout = new AlguiFlowLayout(aContext);
     button.setCatCallback(new AlguiCallback.Click(){
     public void click(boolean b) {
     remAllView();
     if(layout!=null)
     addView(layout);
     endl();
     }
     }
     );
     addView_Nav(button);
     titleLayouts.put(title, layout);
     }
     } else {
     System.out.println("无效标题");
     }
     }
     return this;
     }

     //向对应导航布局添加视图
     public void addView(String title, View... view) {
     AlguiFlowLayout layout = titleLayouts.get(title);
     if (layout != null) {
     layout.addView(view);
     } 
     }*/

}
