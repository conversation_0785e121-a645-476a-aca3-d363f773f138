package com.bytecat.algui.AlguiWindows;
import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.PixelFormat;
import android.os.Build;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import com.bytecat.algui.AlguiTools.AlguiToolPermission;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/17 23:44
 * @Describe Algui窗口
 */
public class AlguiWindow {

    public static final String TAG = "AlguiWindow";

    //窗口启动退出动画
    public static class Animations {
        public static final int Animation = 0x1030000 /*16973824*/;//默认
        public static final int Animation_Activity = 0x1030001 /*16973825*/;//弹出(活动)
        public static final int Animation_Dialog = 0x1030002 /*16973826*/;//弹出(弹窗)
        public static final int Animation_InputMethod = 0x1030056 /*16973910*/;//下移上(键盘)
        public static final int Animation_Toast = 0x1030004 /*16973828*/;//弹出(弹窗)
        public static final int Animation_Translucent = 0x1030003 /*16973827*/;

		public static int Animation_ScaleFade;//右移左
    }


    Context aContext;
    boolean isWindowShow = false;//窗口是否正在显示
    WindowManager Window;//窗口
    WindowManager.LayoutParams WindowParams;//窗口参数
    View view;//显示的视图

    //get-set拓展方法
    // 获取上下文
    public Context getByteContext() {
        return aContext;
    }



    // 获取窗口管理器
    public WindowManager getByteWindowManager() {
        return Window;
    }

    // 获取窗口参数
    public WindowManager.LayoutParams getByteWindowParams() {
        return WindowParams;
    }

    // 设置窗口参数
    public AlguiWindow setCatWindowParams(WindowManager.LayoutParams windowParams) {
        if (windowParams != null) {
            this.WindowParams = windowParams;
            update();
        } else {
            hide();
        }
        return this;
    }

    // 获取显示的视图
    public View getByteView() {
        return view;
    }

    //设置视图
    public AlguiWindow setCatView(View view) {
        if (view == null) {
            hide();
        }
        this.view = view;
       // show();
        update();
        return this;
    }

    // 获取窗口是否正在显示
    public boolean isWindowShow() {
        return isWindowShow;
    }


    //设置窗口显示在哪个活动中
    //如果传入Activity则仅悬浮显示在这一个活动中(无需悬浮窗权限)
    //传入后台Context则全局悬浮显示(需悬浮窗权限)
    public AlguiWindow setCatActivity(Context context) {
        if (context != null) {
            aContext = context;
            boolean b =isWindowShow;//保存之前是否正在显示状态
            hide();//先清除视图

            //设置窗口类型
            if (aContext instanceof Activity) {
                //对于上下文为活动时 窗口类型设置为应用级窗口 (无需悬浮窗权限)
                WindowParams.type  =  WindowManager.LayoutParams.TYPE_APPLICATION;
            } else {
                //对于其它 则使用系统级后台全局窗口 (需要悬浮窗权限)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    //对于安卓8.0以上
                    WindowParams.type  = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
                } else {
                    //对于安卓8.0以下
                    WindowParams.type  =  WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
                }
                //申请悬浮窗权限
                AlguiToolPermission.getWindow(context);
            }
            //获取窗口实例
            Window = (WindowManager) aContext.getSystemService(Context.WINDOW_SERVICE);

            //之前正在显示则重新显示
            if (b) {
                show();
            }
        } else {
            hide();
        }
        return this;
    }

    //初始化时传递的Context决定窗口显示在哪里，
    //如果传入Activity则仅悬浮显示在这一个活动中(无需悬浮窗权限)
    //传入后台Context则全局悬浮显示(需悬浮窗权限)
    //之后也可以通过setCatActivity方法更改显示
    public AlguiWindow(Context context) {
        aContext = context;
        init();
        setCatActivity(aContext);
    }

    public AlguiWindow(Context context, View view) {
        this(context);
        setCatView(view);
    }



    //显示
    public AlguiWindow show() {
        //未显示才显示
        if (!isWindowShow && view != null) {
            //对于安卓8.0以下无需悬浮窗权限
            Window.addView(view, WindowParams);
            isWindowShow = true;
        }
        return this;
    }
    //更新
    public AlguiWindow update() {    
        //正在显示才更新
        if (isWindowShow && view != null) {
            Window.updateViewLayout(view, WindowParams);
        }
        return this;
    }
    //隐藏
    public AlguiWindow hide() {
        //正在显示才清除
        if (isWindowShow && view != null) {
            Window.removeView(view);
            isWindowShow = false;
        }
        return this;
    }

    //设置一些窗口特性
    public AlguiWindow setCatFlags(int flags) {
        WindowParams.flags = flags;
        update();
        return this;
    }
    //添加一个窗口特性
    public AlguiWindow addFlag(int flag) {
        //只有在没有此flag时才添加
        if ((WindowParams.flags & flag) == 0) {
            WindowParams.flags |= flag;
            update();
        }
        return this;
    }
    //移除一个窗口特性
    public AlguiWindow remFlag(int flag) {
        //只有窗口存在此flag时才移除
        if ((WindowParams.flags & flag) != 0) {
            WindowParams.flags &= ~flag;
            update();
        }
        return this;
    }

    //设置窗口大小(dp单位 大小适应不同设备)
    public AlguiWindow setCatSize(float w, float h) {
        if ((int)w != WindowManager.LayoutParams.WRAP_CONTENT
            && (int) w != WindowManager.LayoutParams.MATCH_PARENT
            && (int)w != WindowManager.LayoutParams.FILL_PARENT) {
            WindowParams.width = (int)dp2px(w);
        } else {
            WindowParams.width = (int)w;
        }

        if ((int)h != WindowManager.LayoutParams.WRAP_CONTENT
            && (int)h != WindowManager.LayoutParams.MATCH_PARENT
            && (int)h != WindowManager.LayoutParams.FILL_PARENT) {
            WindowParams.height = (int)dp2px(h);
        } else {
            WindowParams.height = (int)h;
        }
        update();
        return this;
    }
    //设置窗口大小(px单位)
    public AlguiWindow setSizePX(int w, int h) {
        WindowParams.width = w;
        WindowParams.height = h;
        update();
        return this;
    }
    //设置窗口相对于屏幕哪个位置(设置坐标原点)
    public AlguiWindow setCatPosGravity(int gravity) {
        WindowParams.gravity = gravity ;
        update();
        return this;
    }
    //设置窗口在屏幕上的xy位置 (相对Gravity原点的xy偏移)
    public AlguiWindow setCatPos(float x, float y) {
        WindowParams.x = (int) dp2px(x);
        WindowParams.y = (int) dp2px(y);
        update();
        return this;
    }
     //设置窗口在屏幕上的xy位置(相对Gravity原点的xy偏移)
    public AlguiWindow setPos(int x, int y) {
        WindowParams.x = x;
        WindowParams.y = y;
        update();
        return this;
    }
    //设置窗口亮度0-1
    public AlguiWindow setCatBrightness(float b0_1) {
        WindowParams.screenBrightness = b0_1;
        update();
        return this;
    }
    //设置窗口透明度0-1
    public AlguiWindow setCatTransparent(float t0_1) {
        WindowParams.alpha = t0_1;
        update();
        return this;
    }
    //设置窗口启动退出动画
    public AlguiWindow setCatAnimations(int animID) {
        WindowParams.windowAnimations = animID;
        update();
        return this;
    }
   


    private void init() {
        //窗口默认参数
        WindowParams = new WindowManager.LayoutParams();
        WindowParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        WindowParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        WindowParams.gravity = Gravity.TOP | Gravity.LEFT;//位于父布局(屏幕)的位置 (原点)默认左上角
        WindowParams.format = PixelFormat.RGBA_8888;//像素格式
        WindowParams.flags =
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED//硬件加速
            | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL//不阻止其他窗口接收触摸事件
            //| WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH//监听窗口外部触摸
            | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE//无法获取输入框焦点
            ;

        //WindowManager.LayoutParams-API：
        /*public float alpha;  // 窗口的透明度，范围是0.0到1.0，0.0表示完全透明，1.0表示完全不透明。
         public float buttonBrightness;  // 按钮的亮度调节，控制窗口中按钮的亮度，通常用于系统窗口的按钮显示。
         public float dimAmount;  // 窗口的暗度，范围是0.0到1.0，0.0表示完全亮，1.0表示完全黑暗。常用于设置窗口暗背景。
         public int flags;  // 窗口的标志位，控制窗口的特性和行为。通过位掩码设置不同的窗口行为，如是否可以获得焦点、是否需要显示在最上层等。
         public int format;  // 窗口内容的像素格式，通常指定显示内容的颜色格式。
         public int gravity;  // 窗口的重力布局参数，确定窗口相对于父容器的对齐方式。比如，可以指定窗口对齐方式为左上、居中等。
         public float horizontalMargin;  // 窗口水平方向的外边距，通常用于调整窗口在水平轴上的位置。
         @ViewDebug.ExportedProperty public float horizontalWeight;  // 水平权重，用于多窗口的布局管理，决定窗口在水平方向上的扩展比例。
         public int layoutInDisplayCutoutMode;  // 窗口显示是否适应显示切口区域。对于有刘海屏的设备，可以控制窗口是否允许显示在切口区域内。
         @Deprecated public int memoryType;  // 窗口的内存类型，已被废弃。用于指定窗口使用的内存类型（如屏幕外内存、系统内存等）。
         public String packageName;  // 设置窗口所属的应用程序包名，通常用于多任务系统，标识窗口属于哪个应用。
         public int preferredDisplayModeId;  // 偏好的显示模式ID，指示窗口的显示模式（如高刷新率模式等）。
         @Deprecated public float preferredRefreshRate;  // 偏好的刷新率，已被废弃。指定窗口显示的刷新率。
         public int rotationAnimation;  // 窗口旋转动画的方式。控制窗口在旋转时是否需要动画效果以及动画的类型。
         public float screenBrightness;  // 屏幕亮度，控制窗口所显示的屏幕的亮度，范围通常是0.0到1.0。
         public int screenOrientation;  // 屏幕的方向，可以是 `ActivityInfo.SCREEN_ORIENTATION_*` 常量之一，表示窗口显示时的方向，如竖屏、横屏等。
         public int softInputMode;  // 控制软键盘行为的标志位，设置窗口显示时，软键盘如何弹出和交互。
         public int systemUiVisibility;  // 控制窗口的系统UI显示状态，例如是否显示状态栏、导航栏等。
         public IBinder token;  // 窗口的标识符，通常是一个 `IBinder` 对象，表示窗口所属的上下文或者 Activity，帮助系统识别这个窗口属于哪个任务。
         public int type;  // 窗口类型，指定窗口的种类，比如普通窗口、系统窗口、浮动窗口等，决定窗口的表现和优先级。
         public float verticalMargin;  // 窗口垂直方向的外边距，调整窗口在垂直轴上的位置。
         @ViewDebug.ExportedProperty public float verticalWeight;  // 垂直权重，用于多窗口的布局管理，决定窗口在垂直方向上的扩展比例。
         public int windowAnimations;  // 窗口的动画资源ID，控制窗口显示、隐藏时的动画效果。
         @ViewDebug.ExportedProperty public int x;  // 窗口的水平坐标，指定窗口相对于屏幕或父容器的水平位置。
         @ViewDebug.ExportedProperty public int y;  // 窗口的垂直坐标，指定窗口相对于屏幕或父容器的垂直位置。
         */
    }


    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public float dp2px(float dpValue) {
        //参数：输入值单位，需转换的值，设备显示信息
        return  TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, Resources.getSystem().getDisplayMetrics());
    }

}
