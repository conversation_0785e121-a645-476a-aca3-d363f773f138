package com.bytecat.algui;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.bytecat.algui.AlguiTools.AlguiToolNetwork;

import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 简化的远程公告管理器 - 使用外链获取公告内容
 */
public class AnnouncementHelper {

    // 公告外链地址 - 请替换为你的实际地址
    private static final String ANNOUNCEMENT_URL = "https://pastebin.com/raw/ND3Kgttn";

    private Context context;
    private SharedPreferences preferences;

    public AnnouncementHelper(Context context) {
        this.context = context;
        this.preferences = context.getSharedPreferences("announcement_prefs", Context.MODE_PRIVATE);
    }

    /**
     * 检查并显示公告
     */
    public void checkAndShowAnnouncement(Activity activity) {
        // 检查是否设置了不再显示
        if (preferences.getBoolean("dont_show_again", false)) {
            return;
        }

        // 获取远程公告
        getAnnouncementAsync(activity);
    }

    /**
     * 异步获取公告
     */
    private void getAnnouncementAsync(Activity activity) {
        Handler mainHandler = new Handler(Looper.getMainLooper());

        Thread networkThread = new Thread(() -> {
            String result = null;
            try {
                System.out.println("公告请求 - 开始获取公告，URL: " + ANNOUNCEMENT_URL);

                // 直接发送HTTP GET请求获取公告内容
                String response = AlguiToolNetwork.get(ANNOUNCEMENT_URL);
                System.out.println("公告请求 - 原始响应: " + response);
                System.out.println("公告请求 - 响应长度: " + (response != null ? response.length() : "null"));

                if (response != null && !response.trim().isEmpty()) {
                    result = response.trim();
                }

            } catch (Exception e) {
                System.out.println("公告请求 - 异常: " + e.getMessage());
                e.printStackTrace();
            }

            // 切换回主线程处理结果
            String finalResult = result;
            mainHandler.post(() -> {
                System.out.println("公告请求 - 处理结果: " + finalResult);
                if (finalResult != null && !activity.isFinishing()) {
                    if (!finalResult.isEmpty()) {
                        System.out.println("公告请求 - 显示远程公告内容");
                        showAnnouncementDialog(activity, finalResult);
                    } else {
                        System.out.println("公告请求 - 远程公告内容为空，显示默认公告");
                        showDefaultAnnouncement(activity);
                    }
                } else {
                    System.out.println("公告请求 - 获取失败，显示默认公告");
                    showDefaultAnnouncement(activity);
                }
            });
        });

        networkThread.start();
    }

    /**
     * 显示默认公告（当API调用失败时）
     */
    private void showDefaultAnnouncement(Activity activity) {
        String defaultAnnouncement = "{\n"
                + "\"content\": \"❶本软件部分功能需要付费,不同意付费可以退出并卸载软件。\\n\\n❷当前Tohka辅助版本为8.0Pro,反馈群938552503,进群请看群公告,反馈请详细描述问题和带上录屏发送给群主[2647103221]。\\n\\n❸当前适配游戏[星陨计划][天下布魔][潘吉亚异闻录][贤者同盟][樱景物语]。\\n\\n❹购买卡密后可添加微信\\\"HeQuanFeiAi0v0\\\"投稿你所需要的游戏[如若涉及修改游戏数据本人有权力拒绝]。\\n\\n❺购买永久卡如若有意向可带上卡密1对1教学制作游戏辅助(教学后禁止用于网络游戏,如若触犯法律行为与教学人员\\\"十香\\\"无关)。\",\n"
                + "\"time\": \"" + getCurrentTime() + "\",\n"
                + "\"important_tip\": \"请仔细阅读以上条款，使用软件即表示同意相关条款\",\n"
                + "\"version\": \"Tohka辅助 v8.0Pro\"\n"
                + "}";

        if (!activity.isFinishing()) {
            showAnnouncementDialog(activity, defaultAnnouncement);
        }
    }

    /**
     * 显示公告对话框
     */
    private void showAnnouncementDialog(Activity activity, String content) {
        if (activity.isFinishing()) {
            return;
        }

        try {
            // 尝试解析JSON格式的公告
            JSONObject announcementData;
            try {
                announcementData = new JSONObject(content);
            } catch (Exception e) {
                // 如果不是JSON格式，创建简单的公告对象
                announcementData = new JSONObject();
                announcementData.put("content", content);
                announcementData.put("time", getCurrentTime());
            }

            // 创建对话框
            Dialog dialog = new Dialog(activity);
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);

            // 加载布局
            LayoutInflater inflater = LayoutInflater.from(activity);
            View dialogView = inflater.inflate(R.layout.dialog_announcement, null);
            dialog.setContentView(dialogView);

            // 设置对话框属性
            dialog.setCancelable(false);
            dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);

            // 初始化视图
            setupAnnouncementViews(dialogView, announcementData, dialog);

            // 显示对话框
            dialog.show();

        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(activity, "公告显示失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 设置公告视图内容
     */
    private void setupAnnouncementViews(View dialogView, JSONObject data, Dialog dialog) {
        try {
            // 获取视图组件
            TextView tvContent = dialogView.findViewById(R.id.tvAnnouncementContent);
            TextView tvTime = dialogView.findViewById(R.id.tvAnnouncementTime);
            TextView tvImportantTip = dialogView.findViewById(R.id.tvImportantTip);
            TextView tvVersionInfo = dialogView.findViewById(R.id.tvVersionInfo);
            LinearLayout layoutImportantTip = dialogView.findViewById(R.id.layoutImportantTip);
            LinearLayout layoutVersionInfo = dialogView.findViewById(R.id.layoutVersionInfo);
            Button btnDontShowAgain = dialogView.findViewById(R.id.btnDontShowAgain);
            Button btnGotIt = dialogView.findViewById(R.id.btnGotIt);

            // 设置公告内容
            String content = data.optString("content", "暂无公告内容");
            tvContent.setText(content);

            // 设置时间
            String timeStr = data.optString("time", getCurrentTime());
            tvTime.setText(timeStr);

            // 设置重要提示
            String importantTip = data.optString("important_tip", "");
            if (!importantTip.isEmpty()) {
                tvImportantTip.setText(importantTip);
                layoutImportantTip.setVisibility(View.VISIBLE);
            }

            // 设置版本信息
            String versionInfo = data.optString("version", "");
            if (!versionInfo.isEmpty()) {
                tvVersionInfo.setText(versionInfo);
                layoutVersionInfo.setVisibility(View.VISIBLE);
            }

            // 设置按钮点击事件
            btnDontShowAgain.setOnClickListener(v -> {
                preferences.edit().putBoolean("dont_show_again", true).apply();
                dialog.dismiss();
                Toast.makeText(context, "已设置不再显示公告", Toast.LENGTH_SHORT).show();
            });

            btnGotIt.setOnClickListener(v -> dialog.dismiss());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取当前时间字符串
     */
    private String getCurrentTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
        return sdf.format(new Date());
    }
}
