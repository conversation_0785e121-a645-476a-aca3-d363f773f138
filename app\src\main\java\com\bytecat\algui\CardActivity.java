package com.bytecat.algui;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.Toast;
import androidx.annotation.Nullable;

/**
 * 发卡网界面Activity
 * 独立的界面，用于显示发卡网页面
 */
public class CardActivity extends Activity {
    
    private WebView webViewCard;
    private ProgressBar progressBarCard;
    private Button btnBackCard;
    
    // 发卡网URL
    private static final String CARD_URL = "https://faka.tohka.cn/";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 隐藏系统ActionBar，保持全屏美观
        try {
            if (getActionBar() != null) {
                getActionBar().hide();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        setContentView(R.layout.activity_card);
        
        // 初始化界面元素
        webViewCard = findViewById(R.id.webViewCard);
        progressBarCard = findViewById(R.id.progressBarCard);
        btnBackCard = findViewById(R.id.btnBackCard);
        
        // 返回按钮点击事件
        btnBackCard.setOnClickListener(v -> {
            finish(); // 关闭当前Activity，返回到MainActivity
        });
        
        // 初始化WebView设置
        initWebView();
        
        // 加载发卡网页面
        webViewCard.loadUrl(CARD_URL);
    }
    
    /**
     * 初始化WebView设置
     */
    private void initWebView() {
        // 启用JavaScript
        webViewCard.getSettings().setJavaScriptEnabled(true);
        webViewCard.getSettings().setDomStorageEnabled(true);
        webViewCard.getSettings().setLoadWithOverviewMode(true);
        webViewCard.getSettings().setUseWideViewPort(true);
        webViewCard.getSettings().setBuiltInZoomControls(true);
        webViewCard.getSettings().setDisplayZoomControls(false);
        webViewCard.setBackgroundColor(0xFFFFFFFF);
        
        // 设置WebChromeClient来处理进度条
        webViewCard.setWebChromeClient(new android.webkit.WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                progressBarCard.setProgress(newProgress);
                if (newProgress >= 100) {
                    progressBarCard.setVisibility(View.GONE);
                } else {
                    progressBarCard.setVisibility(View.VISIBLE);
                }
            }
        });
        
        // 添加WebViewClient来处理页面加载状态
        webViewCard.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                progressBarCard.setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                progressBarCard.setVisibility(View.GONE);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                progressBarCard.setVisibility(View.GONE);
                Toast.makeText(CardActivity.this, "加载失败: " + description, Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    @Override
    public void onBackPressed() {
        // 如果WebView可以后退，则后退；否则关闭Activity
        if (webViewCard.canGoBack()) {
            webViewCard.goBack();
        } else {
            super.onBackPressed();
            finish();
        }
    }
}
