package com.bytecat.algui;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;
import androidx.annotation.Nullable;
import com.bytecat.algui.AlguiManager.AlguiLog;

/**
 * 悬浮窗选择界面Activity
 * 独立的界面，用于选择要打开的游戏悬浮窗
 */
public class FloatSelectionActivity extends Activity {
    
    private RadioGroup radioGroupFloat;
    private Button btnOpenFloat;
    private Button btnBackFloat;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 隐藏系统ActionBar，保持全屏美观
        try {
            if (getActionBar() != null) {
                getActionBar().hide();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        setContentView(R.layout.activity_float_selection);
        
        // 初始化界面元素
        radioGroupFloat = findViewById(R.id.radioGroupFloat);
        btnOpenFloat = findViewById(R.id.btnOpenFloat);
        btnBackFloat = findViewById(R.id.btnBackFloat);
        
        // 返回按钮点击事件
        btnBackFloat.setOnClickListener(v -> {
            finish(); // 关闭当前Activity，返回到MainActivity
        });
        
        // 打开悬浮窗按钮点击事件
        btnOpenFloat.setOnClickListener(v -> {
            int checkedId = radioGroupFloat.getCheckedRadioButtonId();
            if (checkedId == -1) {
                Toast.makeText(this, "请先选择一个游戏", Toast.LENGTH_SHORT).show();
                return;
            }
            
            RadioButton selectedRadioButton = findViewById(checkedId);
            String game = selectedRadioButton.getText().toString();
            
            // 使用悬浮窗管理器来控制悬浮窗的启动
            AlguiFloatWindowManager manager = AlguiFloatWindowManager.getInstance();
            
            // 检查是否已有相同游戏的悬浮窗在运行
            if (manager.isFloatWindowRunning(game)) {
                Toast.makeText(this, game + "悬浮窗已在运行", Toast.LENGTH_SHORT).show();
                return;
            }
            
            // 检查是否有其他游戏的悬浮窗在运行
            if (manager.hasAnyFloatWindowRunning()) {
                String currentGame = manager.getCurrentActiveGame();
                if (currentGame != null && !currentGame.equals(game)) {
                    // 显示确认对话框
                    new android.app.AlertDialog.Builder(this)
                        .setTitle("确认切换悬浮窗")
                        .setMessage("当前已有" + currentGame + "悬浮窗在运行，是否要切换到" + game + "？")
                        .setPositiveButton("确定", (dialog, which) -> {
                            // 通过Service启动新的悬浮窗
                            boolean success = manager.startFloatWindowViaService(this, game);
                            if (success) {
                                Toast.makeText(this, "已切换到" + game + "悬浮窗", Toast.LENGTH_SHORT).show();
                                finish(); // 关闭当前Activity
                            } else {
                                Toast.makeText(this, "启动" + game + "悬浮窗失败", Toast.LENGTH_SHORT).show();
                            }
                        })
                        .setNegativeButton("取消", null)
                        .show();
                    return;
                }
            }
            
            // 直接启动悬浮窗
            boolean success = manager.startFloatWindowViaService(this, game);
            if (success) {
                Toast.makeText(this, "已请求打开" + game + "悬浮窗", Toast.LENGTH_SHORT).show();
                finish(); // 关闭当前Activity
            } else {
                Toast.makeText(this, "启动" + game + "悬浮窗失败", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    @Override
    public void onBackPressed() {
        // 处理返回键
        super.onBackPressed();
        finish();
    }
}
