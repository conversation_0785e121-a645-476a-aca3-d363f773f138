package com.bytecat.algui;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiDemo.AlguiDemoModMenu;
import com.bytecat.algui.AlguiHacker.AlguiCpp;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiHacker.AlguiRootClient;
import com.bytecat.algui.AlguiHacker.AlguiRootService;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiDocument;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolApp;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolNative;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.bytecat.algui.AlguiWindows.AlguiWin2FA;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.topjohnwu.superuser.ipc.RootService;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁
 * 版权所有］游戏逆向交流QQ群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:35
 * @Describe
 */
//视频使用教学：【ByteCat404的个人空间-哔哩哔哩】 https://b23.tv/3u2y9YO
//文本教程见AlguiDemo.java文件
public class Game3FloatWindow {

    //网络验证
    private static boolean is2FA = true;//网络验证总开关

    private static void Net2FA() {
        //参数1是验证窗口显示在哪个活动中，参数2必须是主活动
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity).setCatTitleBackImage("Toui.jpg")//背景图片
                //配置对接微验网络验证
                .setCatWYAppID("60214")//应用ID
                .setCatWYAppCode("5.1.0")//应用版本号(检测更新)
                .setCatWYOkCode(801)//成功状态码
                .setCatWYAppKey("Ot2IDakBXPuzjjo")//appkey密钥
                .setCatWYRC4_2("JxJrwoS4astZt")//rc4-2密钥
                .addRemoteFieldName("dljjmsone")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .addRemoteFieldName("dljjmstwo")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
                .startWY(new AlguiCallback.WY2FA() {
                    //登录成功时执行 传递：卡密，到期时间，远程变量(如果有)
                    public void success(String kami, String expireTime, HashMap<String, String> field) {
                        MyMenu(kami, expireTime, field);
                    }
                });
    }

    //你的菜单界面 如果启动了网络验证那么传递这些参数：卡密，到期时间，远程变量列表
    private static void MyMenu(String kami, String expireTime, final HashMap<String, String> field) {
        // 检查网络状态 - 使用传入的context
        if (!AlguiToolNetwork.isNetworkAvailable(aContext)) {
            AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "网络错误", "网络连接不可用，请检查网络设置", 5);
            return;
        }

        // 调试信息：显示网络类型
        String networkType = AlguiToolNetwork.getNetworkType(aContext);
        AlguiLog.d(TAG, "网络类型: " + networkType);

        // 调试信息：显示远程变量
        AlguiLog.d(TAG, "远程变量数量: " + field.size());
        for (Map.Entry<String, String> entry : field.entrySet()) {
            AlguiLog.d(TAG, "远程变量 " + entry.getKey() + ": " + entry.getValue());
        }

        // 加载动态库
        AlguiToolNative.loadLibrary("Algui");
        //如果开发root插件请使用此代码绑定root权限 否则注释
        RootService.bind(new Intent(aContext, AlguiRootClient.class), new AlguiRootService());

        //如果有网络验证那么可以获取到这些东西
        String km = kami;//登录成功后的卡密
        String time = expireTime;//登录成功后的到期时间
        //获取一个远程变量的值 确保配置网络验证时添加这个远程变量的名称 参数：远程变量名称，获取失败时的默认值
        String value1 = field.getOrDefault("dljjmsone", "这是远程变量获取失败时的默认值");
        String value2 = field.getOrDefault("dljjmstwo", "这是远程变量获取失败时的默认值");
        //获取机器码
        final String markcode = android.os.Build.FINGERPRINT;

        //检查并获取全网人数
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                String codeList = AlguiToolNetwork.get(AlguiDocument.getRead("codeList"));
                return codeList;
            }

            @Override
            protected void onPostExecute(String result) {
                // 创建悬浮窗菜单
                AlguiV a = AlguiV.Get(aContext);//获取UI快速构建器
                AlguiWinMenu menu = a.WinMenu("潘吉亚异闻录");
                menu.setCatMenuBackImage("Yote2.png", 1, 50); // 使用本地图片替代网络图片
                menu.setCatMenuTopBackImage("Yote2.png", 1, 200); // 设置菜单顶部背景图片
                menu.setCatMenuSize(480, 320); // 设置窗口大小
                
                // 注册悬浮窗到管理器
                AlguiFloatWindowManager.getInstance().registerFloatWindow("潘吉亚异闻录", menu);
                
                // 设置菜单打开关闭回调，用于注销悬浮窗
                menu.setCatMenuOpenCallback(new AlguiCallback.Click() {
                    @Override
                    public void click(boolean isChecked) {
                        if (!isChecked) {
                            // 菜单关闭时注销悬浮窗
                            AlguiFloatWindowManager.getInstance().unregisterFloatWindow("潘吉亚异闻录");
                        }
                    }
                });

                menu.setCatMenuBackImage("https://tc.z.wiki/autoupload/f/jkCJwRluZ1VocLiermaMRUIrV12CZwHbnTb4IyNW3aWyl5f0KlZfm6UsKj-HyTuv/20250728/tkhF/2400X3600/Image_176269371427890.jpg", 1, 50); // 设置菜单背景图片
                menu.setCatMenuTopBackImage("Yote2.png", 1, 200); // 设置菜单顶部背景图片
                menu.setCatMenuSize(480, 320); // 设置窗口大小
                menu.setCatMenuBufferLineMargins(8, 8, 8, 0); // 设置菜单每行的外边距
                menu.setCatBallImage("Ackot9.png");
                menu.setCatMenuBufferLineMaxView(2); // 设置菜单一行最大可包含视图数量为2
                // 主布局 - 水平
                AlguiLinearLayout mainLayout = new AlguiLinearLayout(aContext);
                mainLayout.setOrientation(LinearLayout.HORIZONTAL);
                mainLayout.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                mainLayout.setCatWeight(1);
                mainLayout.setCatBackColor(android.graphics.Color.TRANSPARENT);
                menu.addView(mainLayout);

                // 侧边栏
                AlguiLinearLayout sidebar = new AlguiLinearLayout(aContext);
                sidebar.setOrientation(LinearLayout.VERTICAL);
                sidebar.setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT); // 关键：宽度0dp
                sidebar.setCatWeight(0.3f); // 占比30%
                sidebar.setCatBackColor(android.graphics.Color.TRANSPARENT);
                sidebar.setCatPadding(5, 10, 5, 10);
                mainLayout.addView(sidebar);

                // 顶部图片
                a.Image(sidebar, "Yote2.png")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatRadiu(25)
                        .setCatMargins(0, 18, 0, 18);

                        // 按钮与内容区
        final String[] buttonTexts = {"首页", "功能"};
        final AlguiViewButton[] navButtons = new AlguiViewButton[buttonTexts.length];
        final AlguiLinearLayout[] contentPages = new AlguiLinearLayout[buttonTexts.length];

                AlguiLinearLayout contentArea = new AlguiLinearLayout(aContext);
                contentArea.setOrientation(LinearLayout.VERTICAL);
                contentArea.setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT); // 关键：宽度0dp
                contentArea.setCatWeight(0.7f); // 占比70%
                contentArea.setCatBackColor(android.graphics.Color.TRANSPARENT);
                contentArea.setCatPadding(10, 10, 10, 10);
                mainLayout.addView(contentArea);

                for (int i = 0; i < buttonTexts.length; i++) {
                    final int pageIndex = i;
                    // 按钮美化
                    navButtons[i] = a.Button(sidebar, buttonTexts[i])
                            .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                            .setCatWeight(0)
                            .setCatBackColor(0xCC5BA0F0)
                            .setCatRadiu(14)
                            .setCatTextColor(0xFFFFFFFF)
                            .setCatTextSize(9)
                            .setCatPadding(10, 5, 10, 5)
                            .setCatMargins(0, 6, 0, 6);

                    contentPages[i] = new AlguiLinearLayout(aContext);
                    contentPages[i].setOrientation(LinearLayout.VERTICAL);
                    contentPages[i].setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                    contentPages[i].setCatWeight(1);
                    contentPages[i].setCatBackColor(android.graphics.Color.TRANSPARENT);
                    contentPages[i].setVisibility(android.view.View.GONE);
                    contentArea.addView(contentPages[i]);

                    navButtons[i].setCatCallback(new AlguiCallback.Click() {
                        public void click(boolean isChecked) {
                            for (int j = 0; j < contentPages.length; j++) {
                                contentPages[j].setVisibility(android.view.View.GONE);
                                navButtons[j].setCatBackColor(0xCC5BA0F0)
                                        .setCatRadiu(14)
                                        .setCatBorder(0, 0x00000000);
                            }
                            contentPages[pageIndex].setVisibility(android.view.View.VISIBLE);
                            navButtons[pageIndex].setCatBackColor(0xFF3A7BC8)
                                    .setCatRadiu(16)
                                    .setCatBorder(2, 0xFF00C3FF);
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                        }
                    });
                }
                // 默认显示主页
                contentPages[0].setVisibility(android.view.View.VISIBLE);
                navButtons[0].setCatBackColor(0xFF3A7BC8).setCatRadiu(16).setCatBorder(2, 0xFF00C3FF);

                // 主页内容
                a.TextTitle(contentPages[0], "🎮 潘吉亚异闻录辅助工具")
                        .setCatTextSize(12)
                        .setCatTextColor(0xFF2C3E50)
                        .setCatPadding(0, 0, 0, 20);
                a.TextRoll(contentPages[0], "✨ 作者: 十日之香 | QQ: 2647103221 ✨")
                        .setCatTextRollSpeed(1.5f)
                        .setCatTextColor(0xFFff00cc, 0xFFffcc00, 0xFF00ffcc, 0xFFff0066)
                        .setCatTextMoveGrad(true)
                        .setCatTextSize(8)
                        .setCatPadding(0, 0, 0, 20);
                a.TextInfo(contentPages[0], "📊 当前状态: 运行中\n🎮 目标游戏: 潘吉亚异闻录\n🔧 版本: v4.0")
                        .setCatBackColor(0xFF1a1a2e)
                        .setCatBorder(1, 0xFF4a4a6a);
                a.TextSon(contentPages[0], "© 2024 ByteCat & 十日之香 版权所有")
                        .setCatTextColor(0xFF888888)
                        .setCatTextSize(7)
                        .setCatMargins(0, 20, 0, 0);
                a.Textlink(contentPages[0], "💬 加入交流群: https://qm.qq.com/q/ot52Gd53Eu")
                        .setCatTextColor(0xFF4CAF50)
                        .setCatTextSize(8);

                // --- 以下为功能卡片美化 ---
                /**
                 * @desc 秒杀功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
                 */
                AlguiLinearLayout cardSecondKill = new AlguiLinearLayout(aContext);
                cardSecondKill.setOrientation(LinearLayout.VERTICAL);
                cardSecondKill.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                cardSecondKill.setCatBackColor(0x803A3A3A); // 半透明深色背景
                cardSecondKill.setCatRadiu(14); // 圆角
                cardSecondKill.setCatBorder(1, 0x30FFFFFF); // 浅色边框
                cardSecondKill.setCatMargins(0, 10, 0, 0); // 上下间隔
                contentPages[1].addView(cardSecondKill);
                final boolean[] secondKillSwitch = {false};
                final AlguiViewButton[] btnSecondKill = new AlguiViewButton[1];
                btnSecondKill[0] = a.Button(cardSecondKill, "⚡ 秒杀(关)")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatTextSize(12)
                        .setCatTextColor(0xEEFFFFFF)
                        .setCatBackColor(android.graphics.Color.TRANSPARENT)
                        .setCatRadiu(8)
                        .setCatCallback(new AlguiCallback.Click() {
                            public void click(boolean isChecked) {
                                secondKillSwitch[0] = !secondKillSwitch[0];
                                if (secondKillSwitch[0]) {
                                    btnSecondKill[0].setCatText("⚡ 秒杀(开)").setCatBackColor(0x4000FF00);
                                } else {
                                    btnSecondKill[0].setCatText("⚡ 秒杀(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                                }

                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        if (secondKillSwitch[0]) {
                                            //功能开启
                                            AlguiMemTool.setPackageName("com.rivergames.pangea");//设置包名
                                            AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
                                            AlguiMemTool.MemorySearch("1118;78;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("78", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            AlguiMemTool.MemorySearch("931;52;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("52", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("999;72;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("72", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1325;72;50;9", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("72", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1015;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("49", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1189;73;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("73", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1269;61;50;8", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("61", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("1034;74;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("74", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1154;70;50;8", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("70", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1145;46;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("46", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1002;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("49", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("933;50;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("50", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1105;80;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("80", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1037;75;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("75", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1060;66;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("66", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1503;67;50;12", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("67", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("873;54;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("54", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("830;55;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("55", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1048;48;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("48", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("988;78;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("78", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1054;76;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("76", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("974;51;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("51", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1020;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("49", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("835;55;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("55", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("1118;78;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("78", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1082;66;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("66", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1154;72;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("72", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1080;78;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("78", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("854;53;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("53", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1106;47;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("47", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("786;56;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("56", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1533;72;50;12", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("72", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1383;59;50;9", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("59", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1049;76;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("76", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("889;53;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("53", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("991453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            return "⚡ 秒杀开启成功！";
                                        } else {
                                            AlguiMemTool.setPackageName("com.rivergames.pangea");//设置包名
                                            AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
                                            AlguiMemTool.MemorySearch("991453", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.MemoryOffsetWrite("50", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            return "🔒 秒杀已关闭";
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "秒杀", result, 3);
                                    }
                                }.execute();
                            }
                        });
                /**
                 * @desc 血量功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
                 */
                AlguiLinearLayout cardHp = new AlguiLinearLayout(aContext);
                cardHp.setOrientation(LinearLayout.VERTICAL);
                cardHp.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                cardHp.setCatBackColor(0x803A3A3A); // 半透明深色背景
                cardHp.setCatRadiu(14); // 圆角
                cardHp.setCatBorder(1, 0x30FFFFFF); // 浅色边框
                cardHp.setCatMargins(0, 10, 0, 0); // 上下间隔
                contentPages[1].addView(cardHp);
                final boolean[] hpSwitch = {false};
                final AlguiViewButton[] btnHp = new AlguiViewButton[1];
                btnHp[0] = a.Button(cardHp, "❤️ 血量(关)")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatTextSize(12)
                        .setCatTextColor(0xEEFFFFFF)
                        .setCatBackColor(android.graphics.Color.TRANSPARENT)
                        .setCatRadiu(8)
                        .setCatCallback(new AlguiCallback.Click() {
                            public void click(boolean isChecked) {
                                hpSwitch[0] = !hpSwitch[0];
                                if (hpSwitch[0]) {
                                    btnHp[0].setCatText("❤️ 血量(开)").setCatBackColor(0x4000FF00);
                                } else {
                                    btnHp[0].setCatText("❤️ 血量(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                                }

                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        if (hpSwitch[0]) {
                                            AlguiMemTool.setPackageName("com.rivergames.pangea");//设置包名
                                            AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
                                            AlguiMemTool.MemorySearch("1118;78;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1118", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            AlguiMemTool.MemorySearch("931;52;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("931", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("999;72;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("999", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1325;72;50;9", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1325", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1015;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1015", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1189;73;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1189", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1269;61;50;8", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1269", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("1034;74;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1034", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1154;70;50;8", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1154", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1145;46;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1145", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1002;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1002", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("933;50;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("933", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1105;80;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1105", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1037;75;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1037", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1060;66;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1060", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1503;67;50;12", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1503", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("873;54;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("873", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("830;55;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("830", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1048;48;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1048", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("988;78;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("988", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1054;76;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1054", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("974;51;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("974", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1020;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1020", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("835;55;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("835", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("1118;78;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1118", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1082;66;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1082", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1154;72;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1154", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1080;78;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1080", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("854;53;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("854", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1106;47;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1106", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("786;56;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("786", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1533;72;50;12", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1533", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1383;59;50;9", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1383", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1049;76;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("1049", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("889;53;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("889", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("981453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            return "❤️ 血量开启成功！";
                                        } else {
                                            AlguiMemTool.setPackageName("com.rivergames.pangea");//设置包名
                                            AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
                                            AlguiMemTool.MemorySearch("981453", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.MemoryOffsetWrite("1000", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            return "🔒 血量已关闭";
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "血量", result, 3);
                                    }
                                }.execute();
                            }
                        });
                /**
                 * @desc 防御功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
                 */
                AlguiLinearLayout cardDefense = new AlguiLinearLayout(aContext);
                cardDefense.setOrientation(LinearLayout.VERTICAL);
                cardDefense.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                cardDefense.setCatBackColor(0x803A3A3A); // 半透明深色背景
                cardDefense.setCatRadiu(14); // 圆角
                cardDefense.setCatBorder(1, 0x30FFFFFF); // 浅色边框
                cardDefense.setCatMargins(0, 10, 0, 0); // 上下间隔
                contentPages[1].addView(cardDefense);
                final boolean[] defenseSwitch = {false};
                final AlguiViewButton[] btnDefense = new AlguiViewButton[1];
                btnDefense[0] = a.Button(cardDefense, "🛡️ 防御(关)")
                        .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                        .setCatTextSize(12)
                        .setCatTextColor(0xEEFFFFFF)
                        .setCatBackColor(android.graphics.Color.TRANSPARENT)
                        .setCatRadiu(8)
                        .setCatCallback(new AlguiCallback.Click() {
                            public void click(boolean isChecked) {
                                defenseSwitch[0] = !defenseSwitch[0];
                                if (defenseSwitch[0]) {
                                    btnDefense[0].setCatText("🛡️ 防御(开)").setCatBackColor(0x4000FF00);
                                } else {
                                    btnDefense[0].setCatText("🛡️ 防御(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                                }

                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        if (defenseSwitch[0]) {
                                            AlguiMemTool.setPackageName("com.rivergames.pangea");//设置包名
                                            AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
                                            AlguiMemTool.MemorySearch("1118;78;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            AlguiMemTool.MemorySearch("931;52;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("4", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("999;72;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1325;72;50;9", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("9", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1015;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1189;73;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("6", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1269;61;50;8", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("8", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("1034;74;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1154;70;50;8", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("8", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1145;46;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("6", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1002;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("933;50;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("4", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1105;80;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("6", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("1037;75;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("6", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1060;66;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1503;67;50;12", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("12", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("873;54;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("3", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

                                            AlguiMemTool.MemorySearch("830;55;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("2", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1048;48;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("988;78;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("3", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1054;76;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("974;51;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("4", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1020;49;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("835;55;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("2", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果



                                            AlguiMemTool.MemorySearch("1118;78;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1082;66;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("6", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1154;72;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("6", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1080;78;50;4", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("4", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("854;53;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("3", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1106;47;50;6", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("6", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("786;56;50;2", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("2", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1533;72;50;12", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("12", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1383;59;50;9", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("9", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("1049;76;50;5", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果


                                            AlguiMemTool.MemorySearch("889;53;50;3", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.ImproveOffset("3", AlguiMemTool.TYPE_DWORD, 0);//改善
                                            AlguiMemTool.MemoryOffsetWrite("971453", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            return "🛡️ 防御开启成功！";
                                        } else {
                                            AlguiMemTool.setPackageName("com.rivergames.pangea");//设置包名
                                            AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
                                            AlguiMemTool.MemorySearch("971453", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
                                            AlguiMemTool.MemoryOffsetWrite("5", AlguiMemTool.TYPE_DWORD, 0, false);//修改 【如果需要冻结将false改为true】
                                            AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                            return "🔒 防御已关闭";
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "防御", result, 3);
                                    }
                                }.execute();
                            }
                        });

                //绘制静态视图到屏幕上
                a.WinDraw
                (
                    a.TextTag(null, "Thoka辅助 联系QQ2647103221购买 [到期时间：%s]", 0xCE000000, expireTime)
                    .setCatTextSize(8)
                    .setCatTextColor(0xFFFFFFFF)
                    ,//绘制的视图
                    Gravity.BOTTOM | Gravity.START,//坐标原点 (这里右上原点)
                    10, 10,//相对原点xy偏移
                    false//视图是否可接收触摸事件
                );

            }
        }.execute();

    }

    /* Algui Game3FloatWindow */
    private Game3FloatWindow() {
        throw new UnsupportedOperationException("cannot be instantiated");
    }
    public static final String TAG = "Game3FloatWindow";
    public static Context aContext;

    public static void start(Context c) {
        aContext = c;
        if (is2FA) {
            Net2FA();
        } else {
            MyMenu("免费", "无限期", new HashMap<String, String>());
        }
        // 初始化网络验证
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity).initNet();
    }

}
