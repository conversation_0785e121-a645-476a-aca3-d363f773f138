package com.bytecat.algui;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewFoldMenu;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiDemo.AlguiDemoModMenu;
import com.bytecat.algui.AlguiHacker.AlguiCpp;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiHacker.AlguiRootClient;
import com.bytecat.algui.AlguiHacker.AlguiRootService;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiDocument;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolApp;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolNative;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiWindows.AlguiWin2FA;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import android.text.Html;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.topjohnwu.superuser.ipc.RootService;
import java.util.HashMap;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import java.util.Map;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁
 * 版权所有］游戏逆向交流QQ群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:35
 * @Describe
 */
//视频使用教学：【ByteCat404的个人空间-哔哩哔哩】 https://b23.tv/3u2y9YO
//文本教程见AlguiDemo.java文件
public class Game4FloatWindow {

    //网络验证
    private static boolean is2FA = true;//网络验证总开关
    private static String value1, value2, value3, value4, value5, value6;

    private static void Net2FA() {
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity)
                .setCatTitleBackImage("Toui.jpg")
                .setCatWYAppID("60214")
                .setCatWYAppCode("5.1.0")
                .setCatWYOkCode(801)
                .setCatWYAppKey("Ot2IDakBXPuzjjo")
                .setCatWYRC4_2("JxJrwoS4astZt")
                .addRemoteFieldName("xztmxlone")
                .addRemoteFieldName("xztmxltwo")
                .addRemoteFieldName("xztmxlthree")
                .addRemoteFieldName("xztmmsone")
                .addRemoteFieldName("xztmmstwo")
                .addRemoteFieldName("xztmmsthree")
                .startWY(new AlguiCallback.WY2FA() {
                    public void success(String kami, String expireTime, HashMap<String, String> field) {
                        MyMenu(kami, expireTime, field);
                    }
                });
    }

    //你的菜单界面 如果启动了网络验证那么传递这些参数：卡密，到期时间，远程变量列表
    private static void MyMenu(String kami, String expireTime, final HashMap<String, String> field) {
        // 检查网络状态 - 使用传入的context
        if (!AlguiToolNetwork.isNetworkAvailable(aContext)) {
            AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "网络错误", "网络连接不可用，请检查网络设置", 5);
            return;
        }

        // 调试信息：显示网络类型
        String networkType = AlguiToolNetwork.getNetworkType(aContext);
        AlguiLog.d(TAG, "网络类型: " + networkType);

        // 调试信息：显示远程变量
        AlguiLog.d(TAG, "远程变量数量: " + field.size());
        for (Map.Entry<String, String> entry : field.entrySet()) {
            AlguiLog.d(TAG, "远程变量 " + entry.getKey() + ": " + entry.getValue());
        }

        // 加载动态库
        AlguiToolNative.loadLibrary("Algui");
        RootService.bind(new Intent(aContext, AlguiRootClient.class), new AlguiRootService());
        // 恢复原有变量赋值
        String km = kami; // 登录成功后的卡密
        String time = expireTime; // 登录成功后的到期时间
        // 远程变量赋值
        value1 = field.getOrDefault("xztmxlone", "这是远程变量获取失败时的默认值");
        value2 = field.getOrDefault("xztmxltwo", "这是远程变量获取失败时的默认值");
        value3 = field.getOrDefault("xztmxlthree", "这是远程变量获取失败时的默认值");
        value4 = field.getOrDefault("xztmmsone", "这是远程变量获取失败时的默认值");
        value5 = field.getOrDefault("xztmmstwo", "这是远程变量获取失败时的默认值");
        value6 = field.getOrDefault("xztmmsthree", "这是远程变量获取失败时的默认值");

        // 你可以在后续功能逻辑中使用这些远程变量
        //获取一个远程变量的值 确保配置网络验证时添加这个远程变量的名称 参数：远程变量名称，获取失败时的默认值
        //获取机器码
        final String markcode = android.os.Build.FINGERPRINT;

        //检查并获取全网人数
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                String codeList = AlguiToolNetwork.get(AlguiDocument.getRead("codeList"));
                //AlguiLog.d("服务器测试，机器码列表",codeList);
                long count = 0;
                for (int i = 0; i < codeList.length(); i++) {
                    if (codeList.charAt(i) == ';') {
                        count++;
                    }
                }
                //机器码不存在时
                if (!codeList.contains(markcode)) {
                    AlguiToolNetwork.get(AlguiDocument.getAdd("codeList", markcode + ";"));
                    //AlguiLog.d("服务器测试，添加新用户",r);
                    count++;
                    return "欢迎新用户！你是第" + count + "个用户";
                }

                return "全网人数：" + count;
            }

            @Override
            protected void onPostExecute(String result) {
                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info_gta5);
                AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "欢迎", result, 6);
            }
        }.execute();

        // 统一现代UI风格：侧边栏+内容区+顶部图片，背景透明，按钮圆角高亮
        AlguiV a = AlguiV.Get(aContext);

        AlguiWinMenu menu = a.WinMenu("贤者同盟");
        menu.setCatMenuBackImage("Yote2.png", 1, 50); // 使用本地图片替代网络图片
        menu.setCatMenuTopBackImage("Yote2.png", 1, 200); // 设置菜单顶部背景图片
        menu.setCatMenuSize(480, 320); // 设置窗口大小
        menu.setCatMenuBufferLineMargins(8, 8, 8, 0); // 设置菜单每行的外边距
        menu.setCatBallImage("Ackot9.png");
        menu.setCatMenuBufferLineMaxView(2); // 设置菜单一行最大可包含视图数量为2
        
        // 注册悬浮窗到管理器
        AlguiFloatWindowManager.getInstance().registerFloatWindow("贤者同盟", menu);
        
        // 设置菜单打开关闭回调，用于注销悬浮窗
        menu.setCatMenuOpenCallback(new AlguiCallback.Click() {
            @Override
            public void click(boolean isChecked) {
                if (!isChecked) {
                    // 菜单关闭时注销悬浮窗
                    AlguiFloatWindowManager.getInstance().unregisterFloatWindow("贤者同盟");
                }
            }
        });

        // 主布局 - 水平
        AlguiLinearLayout mainLayout = new AlguiLinearLayout(aContext);
        mainLayout.setOrientation(LinearLayout.HORIZONTAL);
        mainLayout.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        mainLayout.setCatWeight(1);
        mainLayout.setCatBackColor(android.graphics.Color.TRANSPARENT);
        menu.addView(mainLayout);

        // 侧边栏
        AlguiLinearLayout sidebar = new AlguiLinearLayout(aContext);
        sidebar.setOrientation(LinearLayout.VERTICAL);
        sidebar.setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT); // 关键：宽度0dp
        sidebar.setCatWeight(0.3f); // 占比30%
        sidebar.setCatBackColor(android.graphics.Color.TRANSPARENT);
        sidebar.setCatPadding(5, 10, 5, 10);
        mainLayout.addView(sidebar);

        // 顶部图片
        a.Image(sidebar, "Yote2.png")
                .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                .setCatRadiu(25)
                .setCatMargins(0, 18, 0, 18);

        // 按钮与内容区
        final String[] buttonTexts = {"首页", "关于"};
        final AlguiViewButton[] navButtons = new AlguiViewButton[buttonTexts.length];
        final AlguiLinearLayout[] contentPages = new AlguiLinearLayout[buttonTexts.length];

        AlguiLinearLayout contentArea = new AlguiLinearLayout(aContext);
        contentArea.setOrientation(LinearLayout.VERTICAL);
        contentArea.setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT); // 关键：宽度0dp
        contentArea.setCatWeight(0.7f); // 占比70%
        contentArea.setCatBackColor(android.graphics.Color.TRANSPARENT);
        contentArea.setCatPadding(10, 10, 10, 10);
        mainLayout.addView(contentArea);

        for (int i = 0; i < buttonTexts.length; i++) {
            final int pageIndex = i;
            // 按钮美化
            navButtons[i] = a.Button(sidebar, buttonTexts[i])
                    .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                    .setCatWeight(0)
                    .setCatBackColor(0xCC5BA0F0)
                    .setCatRadiu(14)
                    .setCatTextColor(0xFFFFFFFF)
                    .setCatTextSize(9)
                    .setCatPadding(10, 5, 10, 5)
                    .setCatMargins(0, 6, 0, 6);

            contentPages[i] = new AlguiLinearLayout(aContext);
            contentPages[i].setOrientation(LinearLayout.VERTICAL);
            contentPages[i].setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            contentPages[i].setCatWeight(1);
            contentPages[i].setCatBackColor(android.graphics.Color.TRANSPARENT);
            contentPages[i].setVisibility(android.view.View.GONE);
            contentArea.addView(contentPages[i]);

            navButtons[i].setCatCallback(new AlguiCallback.Click() {
                public void click(boolean isChecked) {
                    for (int j = 0; j < contentPages.length; j++) {
                        contentPages[j].setVisibility(android.view.View.GONE);
                        navButtons[j].setCatBackColor(0xCC5BA0F0)
                                .setCatRadiu(14)
                                .setCatBorder(0, 0x00000000);
                    }
                    contentPages[pageIndex].setVisibility(android.view.View.VISIBLE);
                    navButtons[pageIndex].setCatBackColor(0xFF3A7BC8)
                            .setCatRadiu(16)
                            .setCatBorder(2, 0xFF00C3FF);
                    AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                }
            });
        }
        // 默认显示主页
        contentPages[0].setVisibility(android.view.View.VISIBLE);
        navButtons[0].setCatBackColor(0xFF3A7BC8).setCatRadiu(16).setCatBorder(2, 0xFF00C3FF);

        // 主页内容
        a.TextTitle(contentPages[0], "🎮 贤者同盟辅助工具")
                .setCatTextSize(12)
                .setCatTextColor(0xFF2C3E50)
                .setCatPadding(0, 0, 0, 20);
        a.TextRoll(contentPages[0], "✨ 作者: 十日之香 | QQ: 2647103221 ✨")
                .setCatTextRollSpeed(1.5f)
                .setCatTextColor(0xFFff00cc, 0xFFffcc00, 0xFF00ffcc, 0xFFff0066)
                .setCatTextMoveGrad(true)
                .setCatTextSize(8)
                .setCatPadding(0, 0, 0, 20);
        a.TextInfo(contentPages[0], "📊 当前状态: 运行中\n🎮 目标游戏: 贤者同盟\n🔧 版本: v4.0")
                .setCatBackColor(0xFF1a1a2e)
                .setCatBorder(1, 0xFF4a4a6a);

        a.TextInfo(contentPages[0], Html.fromHtml("\uD83E\uDEAC 已读取到游戏：<font color='#FFFFFF'>『" + String.join(" ", AlguiToolApp.getSameUidAppNames(aContext)) + "』" + "</font>"))
                .setCatBackColor(0xFF1a1a2e)
                .setCatBorder(1, 0xFF4a4a6a);
        a.TextSon(contentPages[0], "© 2025 十日之香 版权所有")
                .setCatTextColor(0xFF888888)
                .setCatTextSize(7)
                .setCatMargins(0, 20, 0, 0);
        a.Textlink(contentPages[0], "💬 加入售后群: https://qm.qq.com/q/SktO02Wk0y")
                .setCatTextColor(0xFF4CAF50)
                .setCatTextSize(8);

        // --- 以下为功能卡片美化 ---
        /**
         * @desc 血量功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
         */
        AlguiLinearLayout cardHp = new AlguiLinearLayout(aContext);
        cardHp.setOrientation(LinearLayout.VERTICAL);
        cardHp.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardHp.setCatBackColor(0x803A3A3A); // 半透明深色背景
        cardHp.setCatRadiu(14); // 圆角
        cardHp.setCatBorder(1, 0x30FFFFFF); // 浅色边框
        cardHp.setCatMargins(0, 10, 0, 0); // 上下间隔
        final boolean[] hpSwitch = {false};
        final AlguiViewButton[] btnHp = new AlguiViewButton[1]; // 用数组包裹，便于匿名内部类访问
        btnHp[0] = a.Button(cardHp, "❤️ 血量(关)")
                .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                .setCatTextSize(12)
                .setCatTextColor(0xEEFFFFFF)
                .setCatBackColor(android.graphics.Color.TRANSPARENT)
                .setCatRadiu(8)
                .setCatCallback(new AlguiCallback.Click() {
                    public void click(boolean isChecked) {
                        hpSwitch[0] = !hpSwitch[0];
                        if (hpSwitch[0]) {
                            btnHp[0].setCatText("❤️ 血量(开)").setCatBackColor(0x4000FF00);
                        } else {
                            btnHp[0].setCatText("❤️ 血量(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                        }
                        // 原有血量功能逻辑
                        new AsyncTask<Void, Void, String>() {
                            @Override
                            protected String doInBackground(Void... voids) {
                                if (value1.contains("默认值") || value2.contains("默认值") || value3.contains("默认值")) {
                                    AlguiWinInform.Get(aContext).showInfo_White(
                                            AlguiAssets.Icon.inform_info,
                                            "数据错误",
                                            "未获取到有效参数，请检查网络或联系作者！\n当前参数：" + value1 + ", " + value2 + ", " + value3,
                                            5
                                    );
                                    return "未获取到有效参数";
                                }
                                try {
                                    AlguiMemTool.setPackageName("com.studiosages.sages.ero");
                                    long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + Long.decode(value1);
                                    AlguiMemTool.setMemoryAddrValue("1384663008", yatte, AlguiMemTool.TYPE_DWORD, false);
                                    long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + Long.decode(value2);
                                    AlguiMemTool.setMemoryAddrValue("1923088864", ttre, AlguiMemTool.TYPE_DWORD, false);
                                    long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + Long.decode(value3);
                                    AlguiMemTool.setMemoryAddrValue("-698416192", bttty, AlguiMemTool.TYPE_DWORD, false);
                                    return "❤️ 血量开启成功！";
                                } catch (Exception e) {
                                    AlguiWinInform.Get(aContext).showInfo_White(
                                            AlguiAssets.Icon.inform_info,
                                            "数据错误",
                                            "功能参数内容不合法，请联系作者或检查网络配置！\n" + e.getMessage() + "\n参数：" + value1 + ", " + value2 + ", " + value3,
                                            5
                                    );
                                    return "参数错误";
                                }
                            }

                            @Override
                            protected void onPostExecute(String result) {
                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "贤者同盟", result, 3);
                            }
                        }.execute();
                    }
                });
        contentPages[1].addView(cardHp);

        /**
         * @desc 秒杀功能卡片，按钮极简风格，点击后切换开关并调用原逻辑
         */
        AlguiLinearLayout cardMs = new AlguiLinearLayout(aContext);
        cardMs.setOrientation(LinearLayout.VERTICAL);
        cardMs.setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        cardMs.setCatBackColor(0x803A3A3A);
        cardMs.setCatRadiu(14);
        cardMs.setCatBorder(1, 0x30FFFFFF);
        cardMs.setCatMargins(0, 10, 0, 0);
        final boolean[] msSwitch = {false};
        final AlguiViewButton[] btnMs = new AlguiViewButton[1];
        btnMs[0] = a.Button(cardMs, "⚡ 秒杀(关)")
                .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                .setCatTextSize(12)
                .setCatTextColor(0xEEFFFFFF)
                .setCatBackColor(android.graphics.Color.TRANSPARENT)
                .setCatRadiu(8)
                .setCatCallback(new AlguiCallback.Click() {
                    public void click(boolean isChecked) {
                        msSwitch[0] = !msSwitch[0];
                        if (msSwitch[0]) {
                            btnMs[0].setCatText("⚡ 秒杀(开)").setCatBackColor(0x4000FF00);
                        } else {
                            btnMs[0].setCatText("⚡ 秒杀(关)").setCatBackColor(android.graphics.Color.TRANSPARENT);
                        }
                        // 原有秒杀功能逻辑
                        new AsyncTask<Void, Void, String>() {
                            @Override
                            protected String doInBackground(Void... voids) {
                                if (value4.contains("默认值") || value5.contains("默认值") || value6.contains("默认值")) {
                                    AlguiWinInform.Get(aContext).showInfo_White(
                                            AlguiAssets.Icon.inform_info,
                                            "数据错误",
                                            "未获取到有效参数，请检查网络或联系作者！\n当前参数：" + value4 + ", " + value5 + ", " + value6,
                                            5
                                    );
                                    return "未获取到有效参数";
                                }
                                try {
                                    AlguiMemTool.setPackageName("com.studiosages.sages.ero");
                                    long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + Long.decode(value4);
                                    AlguiMemTool.setMemoryAddrValue("1384663008", yatte, AlguiMemTool.TYPE_DWORD, false);
                                    long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + Long.decode(value5);
                                    AlguiMemTool.setMemoryAddrValue("1923088864", ttre, AlguiMemTool.TYPE_DWORD, false);
                                    long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + Long.decode(value6);
                                    AlguiMemTool.setMemoryAddrValue("-698416192", bttty, AlguiMemTool.TYPE_DWORD, false);
                                    return "⚡ 秒杀开启成功！";
                                } catch (Exception e) {
                                    AlguiWinInform.Get(aContext).showInfo_White(
                                            AlguiAssets.Icon.inform_info,
                                            "数据错误",
                                            "功能参数内容不合法，请联系作者或检查网络配置！\n" + e.getMessage() + "\n参数：" + value4 + ", " + value5 + ", " + value6,
                                            5
                                    );
                                    return "参数错误";
                                }
                            }

                            @Override
                            protected void onPostExecute(String result) {
                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "贤者同盟", result, 3);
                            }
                        }.execute();
                    }
                });
        contentPages[1].addView(cardMs);

        //绘制静态视图到屏幕上
        a.WinDraw
        (
            a.TextTag(null, "Thoka辅助 联系QQ2647103221购买 [到期时间：%s]", 0xCE000000, expireTime)
            .setCatTextSize(8)
            .setCatTextColor(0xFFFFFFFF)
            ,//绘制的视图
            Gravity.BOTTOM | Gravity.START,//坐标原点 (这里右上原点)
            10, 10,//相对原点xy偏移
            false//视图是否可接收触摸事件
        );

    }

    /* Algui Main */
    private Game4FloatWindow() {
        throw new UnsupportedOperationException("cannot be instantiated");
    }
    public static final String TAG = "Game4FloatWindow";
    public static Context aContext;

    public static void start(Context c) {
        aContext = c;
        if (is2FA) {
            Net2FA();
        } else {
            MyMenu("免费", "无限期", new HashMap<String, String>());
        }
        // 初始化网络验证
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity).initNet();
    }

}
