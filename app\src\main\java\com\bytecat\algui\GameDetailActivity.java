package com.bytecat.algui;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.Nullable;

/**
 * 游戏详情页Activity，展示游戏信息、版本选择和下载功能。
 */
public class GameDetailActivity extends Activity {

    private ImageView imgGameLogo;
    private TextView txtGameName;
    private RadioGroup radioGroupVersion;
    private Button btnDownload, btnBack;

    // 下载链接配置
    private static final String URL_XYJH_E = "https://www.123912.com/s/QaJtVv-kUHP";
    private static final String URL_XYJH_H = "https://www.123865.com/s/QaJtVv-kUHP";
    private static final String URL_TXBM_E = "https://www.123912.com/s/QaJtVv-9UHP";
    private static final String URL_TXBM_H = "https://www.123912.com/s/QaJtVv-QUHP";
    private static final String URL_PJYYWL_E = "https://www.123912.com/s/QaJtVv-AUHP";
    private static final String URL_PJYYWL_H = "https://www.123865.com/s/QaJtVv-AUHP";
    private static final String URL_XZTM_E = "https://www.123912.com/s/QaJtVv-HUHP";
    private static final String URL_XZTM_H = "https://www.123912.com/s/QaJtVv-hUHP";
    private static final String URL_YJWY_E = "https://www.123912.com/s/QaJtVv-3UHP";
    private static final String URL_YJWY_H = "https://www.123865.com/s/QaJtVv-3UHP";

    private String gameKey = "";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_game_detail);

        imgGameLogo = findViewById(R.id.imgGameLogo);
        txtGameName = findViewById(R.id.txtGameName);
        radioGroupVersion = findViewById(R.id.radioGroupVersion);
        btnDownload = findViewById(R.id.btnDownload);
        btnBack = findViewById(R.id.btnBack);

        // 获取传递的游戏名
        Intent intent = getIntent();
        String gameName = intent.getStringExtra("gameName");
        if (gameName == null) {
            gameName = "星陨计划";
        }
        txtGameName.setText(gameName);

        // 根据游戏名设置图片
        switch (gameName) {
            case "星陨计划":
                imgGameLogo.setImageResource(R.drawable.xyjh);
                gameKey = "xyjh";
                break;
            case "天下布魔":
                imgGameLogo.setImageResource(R.drawable.txbm);
                gameKey = "txbm";
                break;
            case "潘吉亚异闻录":
                imgGameLogo.setImageResource(R.drawable.pjyywl);
                gameKey = "pjyywl";
                break;
            case "贤者同盟":
                imgGameLogo.setImageResource(R.drawable.xztm);
                gameKey = "xztm";
                break;
            case "樱井物语":
                imgGameLogo.setImageResource(R.drawable.yjwy);
                gameKey = "yjwy";
                break;
            default:
                imgGameLogo.setImageResource(R.drawable.ic_launcher);
                gameKey = "default";
                break;
        }

        // 返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 添加RadioGroup监听器来调试
        radioGroupVersion.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.radioE) {
                System.out.println("选择了E服版本");
            } else if (checkedId == R.id.radioH) {
                System.out.println("选择了工口服版本");
            }
        });

        // 下载按钮
        btnDownload.setOnClickListener(v -> {
            int checkedId = radioGroupVersion.getCheckedRadioButtonId();
            if (checkedId == -1) {
                Toast.makeText(this, "请选择版本", Toast.LENGTH_SHORT).show();
                return;
            }
            String url = getDownloadUrl(gameKey, checkedId == R.id.radioE ? "E" : "H");
            if (url == null) {
                Toast.makeText(this, "暂无下载链接", Toast.LENGTH_SHORT).show();
                return;
            }
            // 跳转到浏览器下载
            Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            startActivity(browserIntent);
        });
    }

    /**
     * 获取下载链接
     *
     * @param key 游戏key
     * @param version 版本（E/H）
     * @return 下载链接
     */
    private String getDownloadUrl(String key, String version) {
        switch (key) {
            case "xyjh":
                return version.equals("E") ? URL_XYJH_E : URL_XYJH_H;
            case "txbm":
                return version.equals("E") ? URL_TXBM_E : URL_TXBM_H;
            case "pjyywl":
                return version.equals("E") ? URL_PJYYWL_E : URL_PJYYWL_H;
            case "xztm":
                return version.equals("E") ? URL_XZTM_E : URL_XZTM_H;
            case "yjwy":
                return version.equals("E") ? URL_YJWY_E : URL_YJWY_H;
        }
        return null;
    }
}
