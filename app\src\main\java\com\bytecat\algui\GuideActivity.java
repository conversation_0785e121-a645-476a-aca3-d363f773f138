package com.bytecat.algui;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import androidx.annotation.Nullable;

/**
 * 攻略界面Activity
 * 独立的界面，用于显示各游戏攻略选择
 */
public class GuideActivity extends Activity {
    
    private Button btnBackGuideList;
    private Button btnGuideXYJH, btnGuideTXBM, btnGuideXZTM, btnGuidePJYYWL, btnGuideYJWT;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 隐藏系统ActionBar，保持全屏美观
        try {
            if (getActionBar() != null) {
                getActionBar().hide();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        setContentView(R.layout.activity_guide);
        
        // 初始化界面元素
        btnBackGuideList = findViewById(R.id.btnBackGuideList);
        btnGuideXYJH = findViewById(R.id.btnGuideXYJH);
        btnGuideTXBM = findViewById(R.id.btnGuideTXBM);
        btnGuideXZTM = findViewById(R.id.btnGuideXZTM);
        btnGuidePJYYWL = findViewById(R.id.btnGuidePJYYWL);
        btnGuideYJWT = findViewById(R.id.btnGuideYJWT);
        
        // 返回按钮点击事件
        btnBackGuideList.setOnClickListener(v -> {
            finish(); // 关闭当前Activity，返回到MainActivity
        });
        
        // 攻略按钮点击事件
        btnGuideXYJH.setOnClickListener(v -> showGuideWeb("https://www.gamekee.com/xy/"));
        btnGuideTXBM.setOnClickListener(v -> showGuideWeb("https://www.gamekee.com/txbm/"));
        btnGuideXZTM.setOnClickListener(v -> showGuideWeb("https://www.gamekee.com/xz/"));
        btnGuidePJYYWL.setOnClickListener(v -> showGuideWeb("https://www.gamekee.com/pj/"));
        btnGuideYJWT.setOnClickListener(v -> showGuideWeb("https://www.gamekee.com/yjwt/"));
    }
    
    /**
     * 显示攻略网页
     */
    private void showGuideWeb(String url) {
        Intent intent = new Intent(this, GuideWebActivity.class);
        intent.putExtra("url", url);
        startActivity(intent);
    }
    
    @Override
    public void onBackPressed() {
        // 处理返回键
        super.onBackPressed();
        finish();
    }
}
