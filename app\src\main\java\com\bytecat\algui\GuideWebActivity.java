package com.bytecat.algui;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.Toast;
import androidx.annotation.Nullable;

/**
 * 攻略网页显示Activity
 * 用于显示具体的攻略网页内容
 */
public class GuideWebActivity extends Activity {
    
    private WebView webViewGuide;
    private ProgressBar progressBarGuide;
    private Button btnBackGuideWeb;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 隐藏系统ActionBar，保持全屏美观
        try {
            if (getActionBar() != null) {
                getActionBar().hide();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        setContentView(R.layout.activity_guide_web);
        
        // 初始化界面元素
        webViewGuide = findViewById(R.id.webViewGuide);
        progressBarGuide = findViewById(R.id.progressBarGuide);
        btnBackGuideWeb = findViewById(R.id.btnBackGuideWeb);
        
        // 返回按钮点击事件
        btnBackGuideWeb.setOnClickListener(v -> {
            finish(); // 关闭当前Activity，返回到攻略列表
        });
        
        // 初始化WebView设置
        initWebView();
        
        // 获取传递的URL并加载
        String url = getIntent().getStringExtra("url");
        if (url != null && !url.isEmpty()) {
            webViewGuide.loadUrl(url);
        } else {
            Toast.makeText(this, "无效的网址", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    /**
     * 初始化WebView设置
     */
    private void initWebView() {
        // 启用JavaScript
        webViewGuide.getSettings().setJavaScriptEnabled(true);
        webViewGuide.getSettings().setDomStorageEnabled(true);
        webViewGuide.getSettings().setLoadWithOverviewMode(true);
        webViewGuide.getSettings().setUseWideViewPort(true);
        webViewGuide.getSettings().setBuiltInZoomControls(true);
        webViewGuide.getSettings().setDisplayZoomControls(false);
        webViewGuide.setBackgroundColor(0xFFFFFFFF);
        
        // 设置WebChromeClient来处理进度条
        webViewGuide.setWebChromeClient(new android.webkit.WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                progressBarGuide.setProgress(newProgress);
                if (newProgress >= 100) {
                    progressBarGuide.setVisibility(View.GONE);
                } else {
                    progressBarGuide.setVisibility(View.VISIBLE);
                }
            }
        });
        
        // 添加WebViewClient来处理页面加载状态
        webViewGuide.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                progressBarGuide.setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                progressBarGuide.setVisibility(View.GONE);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                progressBarGuide.setVisibility(View.GONE);
                Toast.makeText(GuideWebActivity.this, "加载失败: " + description, Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    @Override
    public void onBackPressed() {
        // 如果WebView可以后退，则后退；否则关闭Activity
        if (webViewGuide.canGoBack()) {
            webViewGuide.goBack();
        } else {
            super.onBackPressed();
            finish();
        }
    }
}
