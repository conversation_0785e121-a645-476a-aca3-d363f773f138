# 使用教程

对于新手，在AlguiDemo文件夹下查看相关示例教程
如果你已经学会了，请直接转到Main.java文件的MyMenu方法体中来创建你的窗口...
售后QQ群：941504979

---

# 致谢

Algui 作者：ByteCat & 艾琳  
作者b站：[https://b23.tv/3u2y9YO](https://b23.tv/3u2y9YO) [搜ByteCat404]  
作者QQ：[https://qm.qq.com/q/ot52Gd53Eu](https://qm.qq.com/q/ot52Gd53Eu) [3353484607]  
QQ群：[https://qm.qq.com/q/beuSE6DvIk](https://qm.qq.com/q/beuSE6DvIk) [931212209]  
TG电报群：[https://t.me/+h03LfN94s4M2NWFl](https://t.me/+h03LfN94s4M2NWFl)  
购买地址：[https://982.gov06.cn/item/zb63rt](https://982.gov06.cn/item/zb63rt)  
Algui界面设计来源imgui，感谢亲爱的imgui

---

# 关于

Algui是一个基于安卓Java的窗口UI开发工具包，旨在为开发者提供快速创建窗口界面、内存修改和HOOK功能的解决方案。该工具包灵感来源于IMGUI（Immediate Mode GUI）库，结合Java和JNI（Java Native Interface）技术，为开发者提供灵活的内存操作和调试能力，尤其适合游戏内存修改和作弊菜单制作。

---

# 核心功能

- **窗口UI框架**：采用Java和IMGUI风格的UI库，支持快速创建和自定义窗口界面，简化开发者的GUI开发流程。
- **内存修改支持**：提供GG修改器类似的功能，支持范围搜索、联合搜索、联合改善等高级特性。开发者可以通过Java直接对内存进行操作，进行数据修改和调试。
- **JNI内存操作**：利用JNI桥接技术，直接在Java层实现底层内存修改，不需要依赖外部工具，提升了修改效率和稳定性。
- **HOOK功能**：支持对指定函数进行HOOK操作，允许在程序运行时动态拦截和修改目标函数的执行，适用于调试、反向工程以及作弊功能的实现。

---

# 适用场景

- **游戏辅助**：可用于快速制作游戏中的内置作弊菜单直装、外部作弊菜单插件、内存修改器等。
- **内存分析与修改**：适合开发内存扫描、修改工具，支持对内存中的数据进行精确搜索和修改。
- **反向工程**：在逆向工程和调试中提供便捷的界面和内存操作功能，辅助开发者进行漏洞分析和功能探索。

---

# 优势

- 支持直接在Java进行Root跨进程内存操作。
- 无需外部插件，Java原生支持内存操作与调试。
- 高度可定制化的UI，极大提升开发效率。
- 提供多种内存修改算法，支持高级搜索功能。
- 通过JNI实现底层内存访问，避免了传统的外挂工具依赖问题。

---

# 免责声明

本工具包仅用于合法的开发和调试目的，开发者应遵守当地法律法规，避免用于非法用途。

---

# 版权声明

版权所有 © 2025 ByteCat & 艾琳。  
本工具包受版权保护，未经授权不得用于非法用途，任何未经授权的复制、分发或修改均属违法行为。

