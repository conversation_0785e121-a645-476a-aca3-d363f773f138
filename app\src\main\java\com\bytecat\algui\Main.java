package com.bytecat.algui;

import android.app.Instrumentation;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Handler;
import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiHacker.AlguiRootClient;
import com.bytecat.algui.AlguiHacker.AlguiRootService;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiDocument;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolNative;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.bytecat.algui.AlguiViews.AlguiViewInputBox;
import com.bytecat.algui.AlguiWindows.AlguiWin2FA;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.bytecat.algui.AlguiWindows.AlguiWindow;
import com.topjohnwu.superuser.ipc.RootService;
import java.util.HashMap;
import com.bytecat.algui.AlguiTools.AlguiToolImage;
import android.os.Message;

class Main {

    private static final String TAG = "Main";
    private static Context aContext;
    private static boolean is2FA = true; // 网络验证总开关

    // 网络验证相关变量
    private static String value1, value2, value3, value4, value5, value6, value7, value8, value9, value10, value11, value12, value13, value14, value15;



    /**
     * 启动主程序
     *
     * @param c 上下文
     */
    public static void start(Context c) {
        aContext = c;
        
        // 测试网络验证和远程变量获取
        testNetworkVerification();
        
        if (is2FA) {
            Net2FA();
        } else {
            MyMenu("免费", "无限期", new HashMap<String, String>());
        }
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity).initNet();
    }

    private static void Net2FA() {
        AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity)
                .setCatTitleBackImage("Toui.jpg")
                .setCatWYAppID("60214")
                .setCatWYAppCode("5.1.0")
                .setCatWYOkCode(801)
                .setCatWYAppKey("Ot2IDakBXPuzjjo")
                .setCatWYRC4_2("JxJrwoS4astZt")
                .addRemoteFieldName("xyjhtgone")
                .addRemoteFieldName("xyjhtgtwo")
                .addRemoteFieldName("xyjhtgthree")
                .addRemoteFieldName("xyjhtgfour")
                .addRemoteFieldName("xyjhsyone")
                .addRemoteFieldName("xyjhsytwo")
                .addRemoteFieldName("xyjhmxone")
                .addRemoteFieldName("xyjhmxtwo")
                .addRemoteFieldName("xyjhjsone")
                .addRemoteFieldName("xyjhjstwo")
                .addRemoteFieldName("xyjhgqone")
                .addRemoteFieldName("xyjhgqtwo")
                .addRemoteFieldName("xyjhqjjsone")
                .addRemoteFieldName("xyjhqjjstwo")
                .addRemoteFieldName("bjtpgifone")
                .startWY(
                        new AlguiCallback.WY2FA() {
                            public void success(String kami, String expireTime, HashMap<String, String> field) {
                                MyMenu(kami, expireTime, field);
                            }
                        });
    }

    private static void MyMenu(String kami, String expireTime, final HashMap<String, String> field) {

        AlguiToolNative.loadLibrary("Algui");

        RootService.bind(new Intent(aContext, AlguiRootClient.class), new AlguiRootService());

        value1 = field.getOrDefault("xyjhtgone", "这是远程变量获取失败时的默认值");
        value2 = field.getOrDefault("xyjhtgtwo", "这是远程变量获取失败时的默认值");
        value3 = field.getOrDefault("xyjhtgthree", "这是远程变量获取失败时的默认值");
        value4 = field.getOrDefault("xyjhtgfour", "这是远程变量获取失败时的默认值");
        value5 = field.getOrDefault("xyjhsyone", "这是远程变量获取失败时的默认值");
        value6 = field.getOrDefault("xyjhsytwo", "这是远程变量获取失败时的默认值");
        value7 = field.getOrDefault("xyjhmxone", "这是远程变量获取失败时的默认值");
        value8 = field.getOrDefault("xyjhmxtwo", "这是远程变量获取失败时的默认值");
        value9 = field.getOrDefault("xyjhjsone", "这是远程变量获取失败时的默认值");
        value10 = field.getOrDefault("xyjhjstwo", "这是远程变量获取失败时的默认值");
        value11 = field.getOrDefault("xyjhgqone", "这是远程变量获取失败时的默认值");
        value12 = field.getOrDefault("xyjhgqtwo", "这是远程变量获取失败时的默认值");
        value13 = field.getOrDefault("xyjhqjjsone", "这是远程变量获取失败时的默认值");
        value14 = field.getOrDefault("xyjhqjjstwo", "这是远程变量获取失败时的默认值");
        value15 = field.getOrDefault("bjtpgifone", "这是远程变量获取失败时的默认值");

        final String markcode = android.os.Build.FINGERPRINT;

        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                String codeList = AlguiToolNetwork.get(AlguiDocument.getRead("codeList"));
                long count = 0;
                for (int i = 0; i < codeList.length(); i++) {
                    if (codeList.charAt(i) == ';') {
                        count++;
                    }
                }

                if (!codeList.contains(markcode)) {
                    AlguiToolNetwork.get(AlguiDocument.getAdd("codeList", markcode + ";"));
                    count++;
                    return "欢迎新用户！你是第" + count + "个用户";
                }

                return "全网人数：" + count;
            }

            @Override
            protected void onPostExecute(String result) {
                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info_gta5);
                AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info2, "欢迎", result, 6);
            }
        }.execute();

        //绘制静态视图到屏幕上
        AlguiV a = AlguiV.Get(aContext);
        a.WinDraw
        (
            a.TextTag(null, "Thoka辅助 联系QQ2647103221购买 [到期时间：%s]", 0xCE000000, expireTime)
            .setCatTextSize(8)
            .setCatTextColor(0xFFFFFFFF)
            ,//绘制的视图
            android.view.Gravity.BOTTOM | android.view.Gravity.START,//坐标原点 (这里右上原点)
            10, 10,//相对原点xy偏移
            false//视图是否可接收触摸事件
        );

        // 创建主菜单界面
        createMainMenu();
    }

    /** 创建主菜单 */
    private static void createMainMenu() {
        AlguiV a = AlguiV.Get(aContext);
        AlguiWinMenu menu =
                a.WinMenu("星陨计划")
                        .setCatWinAnimations(AlguiWindow.Animations.Animation_ScaleFade)
                        .showMenu()
                        .setCatMenuBackColor(0xCC1E1E1E)
                        .setCatMenuRadiu(15)
                        .setCatMenuBackImage(value15, 10, 200)
                        .setCatMenuTitleGlow(15, 15)
                        .setCatBallSize(1, 1)
                        .setCatBallBlur(5)
                        .setCatBallColor(0xFFFF5722)
                        .setCatBallImage("Ackot9.png")
                        .setCatEnableMenuSize(false)
                        .setCatMenuTriangleSize(20)
                        .setCatMenuTitleSize(8)
                        .setCatMenuTitleColor(0xFFFFFFFF)
                        .setCatMenuTitleGlow(10, 0x60FFFFFF)
                        .setCatMenuTitleTFAssets("font_medium.ttf")
                        .setCatMenuEndIconImage(null)
                        .setCatMenuTitleColor(0xFFFFFFFF)
                        .setCatMenuTopPadding(10, 10, 10, 10)
                        .setCatMenuTopBackImage("Yote2.png", 4, 65)
                        .setCatMenuSize(480, 320)
                        .setCatMenuBufferLineMaxView(2)
                        .setCatMenuTopPadding(2, 3, 2, 3)
                        .setCatMenuTopBackImage("Yote2.png", 4, 200)
                        .setCatEnableMenuTitle(true);

        AlguiLinearLayout mainLayout = new AlguiLinearLayout(aContext).setCatOrientation(LinearLayout.HORIZONTAL).setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

        AlguiLinearLayout leftMenu = new AlguiLinearLayout(aContext).setCatOrientation(LinearLayout.VERTICAL).setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT).setCatWeight(0.2f).setCatBackColor(0x8020324D).setCatPadding(0, 10, 0, 0);

        a.Image(leftMenu, "TRU1.png")
            .setCatSize((float)ViewGroup.LayoutParams.MATCH_PARENT, 65f)
            .setCatMargins(10f, 5f, 5f, 20f);

        final AlguiLinearLayout rightContent = new AlguiLinearLayout(aContext).setCatOrientation(LinearLayout.VERTICAL).setCatSize(0, ViewGroup.LayoutParams.MATCH_PARENT).setCatWeight(0.8f).setCatPadding(10, 10, 10, 10);

        // 在左侧菜单中，每个按钮+分割线单独成组
        String[] buttonTexts = {"秒杀功能", "修改角色", "关于菜单"};
        for (final String text : buttonTexts) {
            // 添加按钮
            a.Button(leftMenu, text)
                    .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 50)
                    .setCatWeight(0)
                    .setCatPadding(5, 5, 5, 5)
                    .setCatTextSize(12)
                    .setCatTextColor(0xEEFFFFFF)
                    .setCatBackColor(0x803A3A3A)
                    .setCatRadiu(8)
                    .setCatBorder(1, 0x30FFFFFF)
                    .setCatTextGlow(5, 0x80FFFFFF)
                    .setCatCallback(
                            new AlguiCallback.Click() {
                                @Override
                                public void click(boolean isClick) {
                                    rightContent.removeAllViews();
                                    switch (text) {
                                        case "秒杀功能":
                                            createKillFunction(rightContent);
                                            break;
                                        case "修改角色":
                                            createGodMode(rightContent);
                                            break;

                                        case "关于菜单":
                                            createAboutPage(rightContent);
                                            break;
                                    }
                                }
                            });

            // 添加分割线（最后一个按钮后不加）
            if (!text.equals(buttonTexts[buttonTexts.length - 1])) {
                a.Line(leftMenu, 0x60FFFFFF).setCatSize(1f); // 只传线宽 float
            }
        }

        mainLayout.addView(leftMenu);
        mainLayout.addView(rightContent);
        menu.addView(mainLayout);

        createKillFunction(rightContent);
    }

    /**
     * 创建秒杀功能界面（单卡片内部分为左右两栏）
     *
     * @param parent 父布局
     */
    private static void createKillFunction(AlguiLinearLayout parent) {
        final AlguiV a = AlguiV.Get(aContext);

        // 创建主卡片容器
        AlguiLinearLayout mainCard = new AlguiLinearLayout(aContext).setCatOrientation(LinearLayout.VERTICAL).setCatBackColor(0x60FFFFFF).setCatRadiu(15).setCatPadding(15, 15, 15, 15).setCatBorder(1, 0x30FFFFFF).setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);

        // 提示文本（放在卡片顶部）
        a.TextHelp(mainCard, "点击查看注意事项！", "开启之后进入对局第一次没效果正常，只需要右上角设置点击重开对局，深渊对局结束卡死属于正常现象").setCatPadding(8, 8, 8, 8).setCatTextColor(0xFF64B5F6).setCatTextSize(13).setCatBackColor(0x2064B5F6).setCatRadiu(8).setCatMargins(0, 0, 0, 10);

        // 创建内部左右分栏容器
        AlguiLinearLayout innerContainer = new AlguiLinearLayout(aContext).setCatOrientation(LinearLayout.HORIZONTAL).setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT).setCatPadding(0, 10, 0, 0);

        // 创建左栏（原卡片1的功能）
        AlguiLinearLayout leftColumn = new AlguiLinearLayout(aContext).setCatOrientation(LinearLayout.VERTICAL).setCatSize(0, ViewGroup.LayoutParams.WRAP_CONTENT).setCatWeight(1); // 设置权重为1

        // 创建右栏（原卡片2的功能）
        AlguiLinearLayout rightColumn = new AlguiLinearLayout(aContext).setCatOrientation(LinearLayout.VERTICAL).setCatSize(0, ViewGroup.LayoutParams.WRAP_CONTENT).setCatWeight(1); // 设置权重为1

        // 左栏功能
        createFeatureSwitch(a, leftColumn, "秒杀", "20", "999998");
        createFeatureSwitch(a, leftColumn, "血量", "60", "999988");
        createFeatureSwitch(a, leftColumn, "定怪", "100", "999");
        createFeatureSwitch(a, leftColumn, "速度", "4", "999958");

        // 往菜单添加一个复选框并设置点击监听器
        a.CheckBox(leftColumn, "全局加速")
                .setCatSize(0, ViewGroup.LayoutParams.WRAP_CONTENT)
                .setCatWeight(1)
                .setCatTextSize(9)
                .setCatTextColor(0xFF000000)
                .setCatPadding(2, 2, 2, 2)
                .setCatCallback(
                        new AlguiCallback.Click() {

                            public void click(final boolean isSwitch) {

                                // 我们在子线程运行内存修改 防止在UI线程卡顿或异常
                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        if (isSwitch) {
                                            // 功能开启

                                            // 动态基址内存修改示例
                                            AlguiMemTool.setPackageName("com.nerversoft.ark.recode"); // 设置包名
                                            long sAddr = AlguiMemTool.getModuleBaseAddr("libunity.so:bss", AlguiMemTool.HEAD_CB); // 获取模块基址 【xa模块传HEAD_XA  cb模块传HEAD_CB  cd模块传HEAD_CD】
                                            long daddr = AlguiMemTool.jump64(sAddr + 0x1CBC0) + 0xFC; // 跳转指针 跳到目标地址 【32位使用 jump32  64位使用jump64】
                                            AlguiMemTool.setMemoryAddrValue("15", daddr, AlguiMemTool.TYPE_FLOAT, true); // 修改目标值 【如果需要冻结将false改为true】
                                            return "开启成功";
                                        } else {
                                            // 功能关闭
                                            // 动态基址内存修改示例
                                            AlguiMemTool.setPackageName("com.nerversoft.ark.recode"); // 设置包名
                                            long sAddr = AlguiMemTool.getModuleBaseAddr("libunity.so:bss", AlguiMemTool.HEAD_CB); // 获取模块基址 【xa模块传HEAD_XA  cb模块传HEAD_CB  cd模块传HEAD_CD】
                                            long daddr = AlguiMemTool.jump64(sAddr + 0x1CBC0) + 0xFC; // 跳转指针 跳到目标地址 【32位使用 jump32  64位使用jump64】
                                            AlguiMemTool.setMemoryAddrValue("1", daddr, AlguiMemTool.TYPE_FLOAT, false); // 修改目标值 【如果需要冻结将false改为true】
                                            return "关闭成功";
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "功能", result, 3);
                                    }
                                }.execute();
                            }
                        });

        createSpecialFeatureSwitch(a, leftColumn, "过检", value5, value6, "-721215457", "-698416192", "-1447079938", "-1442839565");

        // 右栏功能
        createFeatureSwitch(a, rightColumn, "丢怪", "0.9", "0.0004");
        createFeatureSwitch(a, rightColumn, "百分百暴击", "0.15", "9.91");
        a.CheckBox(rightColumn, "跳关")
                .setCatSize(0, ViewGroup.LayoutParams.WRAP_CONTENT)
                .setCatWeight(1)
                .setCatTextSize(9)
                .setCatTextColor(0xFF000000)
                .setCatPadding(2, 2, 2, 2)
                .setCatCallback(
                        new AlguiCallback.Click() {

                            public void click(final boolean isSwitch) {

                                // 我们在子线程运行内存修改 防止在UI线程卡顿或异常
                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        if (isSwitch) {
                                            // 功能开启

                                            AlguiMemTool.setPackageName("com.nerversoft.ark.recode"); // 设置包名

                                            // 计算目标地址
                                            // 假设 value1 是一个十六进制字符串，例如 "0x1C3C878"
                                            long offset = Long.decode(value1); // 将字符串解析为 long 类型的数值
                                            long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset;

                                            // 修改内存值
                                            AlguiMemTool.setMemoryAddrValue("1384120352", yatte, AlguiMemTool.TYPE_DWORD, false);
                                            long offset1 = Long.decode(value2); // 将字符串解析为 long 类型的数值
                                            long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset1;
                                            AlguiMemTool.setMemoryAddrValue("-698416192", ttre, AlguiMemTool.TYPE_DWORD, false); // 修改目标值 【如果需要冻结将false改为true】
                                            long offset2 = Long.decode(value3); // 将字符串解析为 long 类型的数值
                                            long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset2;
                                            AlguiMemTool.setMemoryAddrValue("1384120352", bttty, AlguiMemTool.TYPE_DWORD, false); // 修改目标值 【如果需要冻结将false改为true】
                                            long offset3 = Long.decode(value4); // 将字符串解析为 long 类型的数值
                                            long adddf = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset3;
                                            AlguiMemTool.setMemoryAddrValue("-698416192", adddf, AlguiMemTool.TYPE_DWORD, false); // 修改目标值 【如果需要冻结将false改为true】

                                            return "开启成功";
                                        } else {
                                            // 功能关闭
                                            AlguiMemTool.setPackageName("com.nerversoft.ark.recode"); // 设置包名

                                            // 计算目标地址
                                            // 假设 value1 是一个十六进制字符串，例如 "0x1C3C878"
                                            long offset = Long.decode(value1); // 将字符串解析为 long 类型的数值
                                            long yatte = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset;

                                            // 修改内存值

                                            AlguiMemTool.setMemoryAddrValue("-1447143426", yatte, AlguiMemTool.TYPE_DWORD, false);
                                            long offset1 = Long.decode(value2); // 将字符串解析为 long 类型的数值
                                            long ttre = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset1;
                                            AlguiMemTool.setMemoryAddrValue("-1459531788", ttre, AlguiMemTool.TYPE_DWORD, false); // 修改目标值 【如果需要冻结将false改为true】
                                            long offset2 = Long.decode(value3); // 将字符串解析为 long 类型的数值
                                            long bttty = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset2;
                                            AlguiMemTool.setMemoryAddrValue("-132247554", bttty, AlguiMemTool.TYPE_DWORD, false); // 修改目标值 【如果需要冻结将false改为true】
                                            long offset3 = Long.decode(value4); // 将字符串解析为 long 类型的数值
                                            long adddf = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset3;
                                            AlguiMemTool.setMemoryAddrValue("-1459531788", adddf, AlguiMemTool.TYPE_DWORD, false); // 修改目标值 【如果需要冻结将false改为true】

                                            return "关闭成功";
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "跳关", result, 3);
                                    }
                                }.execute();
                            }
                        });
        createSpecialFeatureSwitch(a, rightColumn, "关卡全部满星", value7, value8, "1384120352", "-698416192", "-1447143426", "-1459531788");
        createSpecialFeatureSwitch(a, rightColumn, "一键解锁深渊", value9, value10, "1384120352", "-698416192", "-132378626", "-1459527688");
        createSpecialFeatureSwitch(a, rightColumn, "解锁所有关卡", value11, value12, "1384120352", "-698416192", "-1447079938", "-1442839565");

        // 将左右栏添加到内部容器
        innerContainer.addView(leftColumn);
        innerContainer.addView(rightColumn);

        // 将内部容器添加到主卡片
        mainCard.addView(innerContainer);

        // 更新父布局
        parent.remAllView();
        parent.addView(mainCard);
    }

    /** 创建通用功能开关 */
    private static void createFeatureSwitch(AlguiV a, AlguiLinearLayout parent, final String featureName, final String searchValue, final String replaceValue) {
        a.CheckBox(parent, featureName)
                .setCatSize(0, ViewGroup.LayoutParams.WRAP_CONTENT)
                .setCatWeight(1)
                .setCatTextSize(9)
                .setCatTextColor(0xFF000000)
                .setCatPadding(2, 2, 2, 2)
                .setCatCallback(
                        new AlguiCallback.Click() {
                            public void click(final boolean isSwitch) {
                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        try {
                                            AlguiMemTool.clearResultList();
                                            AlguiMemTool.setPackageName("com.nerversoft.ark.recode");
                                            AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);

                                            if (isSwitch) {
                                                AlguiMemTool.MemorySearch(searchValue, AlguiMemTool.TYPE_DOUBLE);
                                                AlguiMemTool.MemoryOffsetWrite(replaceValue, AlguiMemTool.TYPE_DOUBLE, 0, false);
                                                return "开启成功";
                                            } else {
                                                AlguiMemTool.MemorySearch(replaceValue, AlguiMemTool.TYPE_DOUBLE);
                                                AlguiMemTool.MemoryOffsetWrite(searchValue, AlguiMemTool.TYPE_DOUBLE, 0, false);
                                                return "关闭成功";
                                            }
                                        } catch (Exception e) {
                                            return "操作失败: " + e.getMessage();
                                        } finally {
                                            AlguiMemTool.clearResultList();
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, featureName, result, 3);
                                    }
                                }.execute();
                            }
                        });
    }

    /** 创建特殊功能开关 */
    private static void createSpecialFeatureSwitch(AlguiV a, AlguiLinearLayout parent, final String featureName, final String offset1Str, final String offset2Str, final String onValue1, final String onValue2, final String offValue1, final String offValue2) {
        a.CheckBox(parent, featureName)
                .setCatSize(0, ViewGroup.LayoutParams.WRAP_CONTENT)
                .setCatWeight(1)
                .setCatTextSize(featureName.length() > 10 ? 8 : 9)
                .setCatTextColor(0xFF000000)
                .setCatPadding(2, 2, 2, 2)
                .setCatCallback(
                        new AlguiCallback.Click() {
                            public void click(final boolean isSwitch) {
                                new AsyncTask<Void, Void, String>() {
                                    @Override
                                    protected String doInBackground(Void... voids) {
                                        try {
                                            AlguiMemTool.setPackageName("com.nerversoft.ark.recode");

                                            long offset1 = Long.decode(offset1Str);
                                            long addr1 = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset1;

                                            long offset2 = Long.decode(offset2Str);
                                            long addr2 = AlguiMemTool.getModuleBaseAddr("libil2cpp.so", AlguiMemTool.HEAD_XA) + offset2;

                                            if (isSwitch) {
                                                AlguiMemTool.setMemoryAddrValue(onValue1, addr1, AlguiMemTool.TYPE_DWORD, false);
                                                AlguiMemTool.setMemoryAddrValue(onValue2, addr2, AlguiMemTool.TYPE_DWORD, false);
                                                return "开启成功";
                                            } else {
                                                AlguiMemTool.setMemoryAddrValue(offValue1, addr1, AlguiMemTool.TYPE_DWORD, false);
                                                AlguiMemTool.setMemoryAddrValue(offValue2, addr2, AlguiMemTool.TYPE_DWORD, false);
                                                return "关闭成功";
                                            }
                                        } catch (Exception e) {
                                            return "操作失败: " + e.getMessage();
                                        }
                                    }

                                    @Override
                                    protected void onPostExecute(String result) {
                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, featureName, result, 3);
                                    }
                                }.execute();
                            }
                        });
    }

    /** 创建角色修改界面 */
    private static void createGodMode(AlguiLinearLayout parent) {
        final AlguiV a = AlguiV.Get(aContext);



        a.TextColorsMove(parent, "           没写完，下次加上")
                .setCatTextSize(28)
                .setCatTextColor(0xFFBB86FC, 0xFF6200EE, 0xFFFFFFFF, 0xFFFE3666) // 设置文本颜色为多个颜色进行渐变
                .setCatTextMoveGrad(true) // 启用动态渐变
        ;

        a.Image(parent, "No66.gif")
            .setCatSize((float)ViewGroup.LayoutParams.MATCH_PARENT, 205f)
            .setCatMargins(10f, 5f, 5f, 10f);
    }


    /** 创建关于页面 */
    private static void createAboutPage(AlguiLinearLayout parent) {

        final AlguiV a = AlguiV.Get(aContext);

        // 1. 创建网页视图并加载百度
        a.Web(parent, "https://note.youdao.com/s/XyT45GaW")
                .setCatSize(ViewGroup.LayoutParams.MATCH_PARENT, 4400) // 设置高度（避免全屏）
                .setCatRadiu(2); // 可选：圆角
    }

    /**
     * 测试网络验证和远程变量获取
     */
    private static void testNetworkVerification() {
        AlguiLog.d(TAG, "开始测试网络验证...");
        
        // 检查网络状态 - 使用传入的context
        if (!AlguiToolNetwork.isNetworkAvailable(aContext)) {
            AlguiLog.e(TAG, "网络连接不可用");
            AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info, "网络错误", "网络连接不可用，请检查网络设置", 5);
            return;
        }

        String networkType = AlguiToolNetwork.getNetworkType(aContext);
        AlguiLog.d(TAG, "网络类型: " + networkType);

        // 测试网络图片加载
        AlguiLog.d(TAG, "测试网络图片加载...");
        AlguiToolImage.getImageURL("https://tc.z.wiki/autoupload/jkCJwRluZ1VocLiermaMRUIrV12CZwHbnTb4IyNW3aWyl5f0KlZfm6UsKj-HyTuv/20250702/Quc9/1280X1964/3420132-8a840613de-00000002.webp", new AlguiCallback.Web() {
            @Override
            public void web(Message msg) {
                switch (msg.what) {
                    case 200:
                        AlguiLog.d(TAG, "网络图片加载成功");
                        break;
                    case 404:
                        AlguiLog.e(TAG, "网络图片加载失败：服务器错误");
                        break;
                    case 651:
                        AlguiLog.e(TAG, "网络图片加载失败：网络异常");
                        break;
                }
            }
        });
    }


    private Main() {
        throw new UnsupportedOperationException("cannot be instantiated");
    }
}
