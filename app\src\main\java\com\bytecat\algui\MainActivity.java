package com.bytecat.algui;

import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.ImageView;
import android.widget.Toast;
import android.widget.RadioGroup;
import android.widget.RadioButton;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.os.Build;
import android.provider.Settings;
import android.net.Uri;
import android.app.AlertDialog;

/**
 * 主界面Activity，使用底部导航栏在同一个Activity中切换不同页面 包含首页、发卡网、攻略、悬浮窗四个页面，通过显示/隐藏布局实现页面切换
 */
public class MainActivity extends Activity {

    // 页面布局
    private ScrollView homeContent, floatContent, guideContent;
    private LinearLayout cardContent;
    private WebView webViewCard;

    // 导航按钮
    private Button navHome, navCard, navGuide, navFloat;

    // 首页元素
    private ImageView banner;
    private ImageView topCardImage;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置全屏布局，内容延伸到状态栏和导航栏下方
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
        );
        getWindow().setStatusBarColor(0x00000000); // 透明状态栏
        getWindow().setNavigationBarColor(0x00000000); // 透明导航栏

        setContentView(R.layout.activity_main);

        // 初始化页面布局
        homeContent = findViewById(R.id.homeContent);
        cardContent = findViewById(R.id.cardContent);
        guideContent = findViewById(R.id.guideContent);
        floatContent = findViewById(R.id.floatContent);
        webViewCard = findViewById(R.id.webViewCard);

        // 初始化导航按钮
        navHome = findViewById(R.id.navHome);
        navCard = findViewById(R.id.navCard);
        navGuide = findViewById(R.id.navGuide);
        navFloat = findViewById(R.id.navFloat);

        // 初始化首页元素
        banner = findViewById(R.id.banner);
        topCardImage = findViewById(R.id.topCardImage);

        // 确保横幅图片正确显示
        if (banner != null) {
            try {
                banner.setImageResource(R.drawable.beijing1);
                banner.setScaleType(ImageView.ScaleType.CENTER_CROP);
                banner.setVisibility(View.VISIBLE);
                // 添加调试日志
                System.out.println("横幅图片已设置");
            } catch (Exception e) {
                System.out.println("设置横幅图片失败: " + e.getMessage());
                // 如果beijing1加载失败，尝试使用备用图片
                try {
                    banner.setImageResource(R.drawable.ic_launcher);
                } catch (Exception ex) {
                    System.out.println("备用图片也加载失败: " + ex.getMessage());
                }
            }
        } else {
            System.out.println("找不到banner ImageView");
        }

        // 设置顶部卡片图片
        if (topCardImage != null) {
            try {
                topCardImage.setImageResource(R.drawable.beijing1);
                topCardImage.setScaleType(ImageView.ScaleType.CENTER_CROP);
                topCardImage.setVisibility(View.VISIBLE);
                System.out.println("顶部卡片图片已设置");
            } catch (Exception e) {
                System.out.println("设置顶部卡片图片失败: " + e.getMessage());
            }
        }

        // 设置WebView
        setupWebView();

        // 设置导航栏点击事件
        setupNavigation();

        // 设置游戏下载按钮点击事件
        setupGameButtons();

        // 设置作者主页
        setupAuthorPage();

        // 设置悬浮窗按钮点击事件
        setupFloatButtons();

        // 设置横幅图片
        setupBanner();

        // 默认显示首页
        showHome();

        // 检查并显示远程公告
        checkRemoteAnnouncement();

        // 启动时自动检测并引导用户授权悬浮窗权限
        checkAndRequestFloatPermission();
    }

    /**
     * 检查并请求悬浮窗权限（Android 6.0+） 若无权限则自动跳转到系统设置页面引导用户授权
     */
    private void checkAndRequestFloatPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                Toast.makeText(this, "请授权悬浮窗权限，否则部分功能无法使用", Toast.LENGTH_LONG).show();
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + getPackageName()));
                startActivity(intent);
            }
        }
    }

    /**
     * 检查并显示远程公告
     */
    private void checkRemoteAnnouncement() {
        try {
            AnnouncementHelper announcementHelper = new AnnouncementHelper(this);
            // 延迟1秒显示公告，确保界面完全加载
            new android.os.Handler().postDelayed(() -> {
                announcementHelper.checkAndShowAnnouncement(this);
            }, 1000);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("公告检查失败: " + e.getMessage());
        }
    }

    /**
     * 设置横幅图片
     */
    private void setupBanner() {
        if (banner != null) {
            // 强制设置图片资源
            banner.post(new Runnable() {
                @Override
                public void run() {
                    try {
                        banner.setImageResource(R.drawable.beijing1);
                        banner.setScaleType(ImageView.ScaleType.CENTER_CROP);
                        banner.setVisibility(View.VISIBLE);
                        banner.invalidate(); // 强制重绘
                        System.out.println("横幅图片设置完成");
                    } catch (Exception e) {
                        System.out.println("横幅图片设置失败: " + e.getMessage());
                    }
                }
            });
        }
    }

    /**
     * 设置WebView配置
     */
    private void setupWebView() {
        webViewCard.setWebViewClient(new WebViewClient());
        webViewCard.getSettings().setJavaScriptEnabled(true);
        webViewCard.loadUrl("https://shop.xiaoman.top/links/50F4486F"); // 替换为实际的发卡网地址
    }

    /**
     * 设置底部导航栏点击事件
     */
    private void setupNavigation() {
        navHome.setOnClickListener(v -> {
            selectNav(navHome);
            showHome();
        });

        navCard.setOnClickListener(v -> {
            selectNav(navCard);
            showCard();
        });

        navGuide.setOnClickListener(v -> {
            selectNav(navGuide);
            showGuide();
        });

        navFloat.setOnClickListener(v -> {
            selectNav(navFloat);
            showFloat();
        });
    }

    /**
     * 设置游戏下载按钮点击事件
     */
    private void setupGameButtons() {
        findViewById(R.id.btnDownloadXYJH).setOnClickListener(v -> openGameDetail("星陨计划"));
        findViewById(R.id.btnDownloadTXBM).setOnClickListener(v -> openGameDetail("天下布魔"));
        findViewById(R.id.btnDownloadPJYYWL).setOnClickListener(v -> openGameDetail("潘吉亚异闻录"));
        findViewById(R.id.btnDownloadXZTM).setOnClickListener(v -> openGameDetail("贤者同盟"));
        findViewById(R.id.btnDownloadYJWT).setOnClickListener(v -> openGameDetail("樱井物语"));
    }

    /**
     * 设置作者主页
     */
    private void setupAuthorPage() {
        // 设置联系方式点击事件
        setupContactButtons();
    }

    /**
     * 设置联系方式按钮点击事件
     */
    private void setupContactButtons() {
        try {
            // QQ联系方式
            findViewById(R.id.contactQQ).setOnClickListener(v -> {
                copyToClipboard("QQ", "123456789");
                Toast.makeText(this, "QQ号已复制到剪贴板", Toast.LENGTH_SHORT).show();
            });

            // 微信联系方式
            findViewById(R.id.contactWeChat).setOnClickListener(v -> {
                copyToClipboard("微信", "ByteCat_Dev");
                Toast.makeText(this, "微信号已复制到剪贴板", Toast.LENGTH_SHORT).show();
            });

            // 邮箱联系方式
            findViewById(R.id.contactEmail).setOnClickListener(v -> {
                copyToClipboard("邮箱", "<EMAIL>");
                Toast.makeText(this, "邮箱地址已复制到剪贴板", Toast.LENGTH_SHORT).show();
            });

            // B站联系方式
            findViewById(R.id.contactBilibili).setOnClickListener(v -> {
                copyToClipboard("B站", "MY-血-233");
                Toast.makeText(this, "B站ID已复制到剪贴板", Toast.LENGTH_SHORT).show();
            });
        } catch (Exception e) {
            System.out.println("设置联系方式按钮失败: " + e.getMessage());
        }
    }

    /**
     * 复制文本到剪贴板
     */
    private void copyToClipboard(String label, String text) {
        try {
            android.content.ClipboardManager clipboard = (android.content.ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
            android.content.ClipData clip = android.content.ClipData.newPlainText(label, text);
            clipboard.setPrimaryClip(clip);
        } catch (Exception e) {
            System.out.println("复制到剪贴板失败: " + e.getMessage());
        }
    }

    // 当前选中的游戏
    private String selectedFloatGame = "";

    /**
     * 设置悬浮窗选择事件
     */
    private void setupFloatButtons() {
        // 手动设置每个RadioButton的点击事件
        findViewById(R.id.btnFloatXYJH).setOnClickListener(v -> selectFloatGame("星陨计划", R.id.btnFloatXYJH));
        findViewById(R.id.btnFloatTXBM).setOnClickListener(v -> selectFloatGame("天下布魔", R.id.btnFloatTXBM));
        findViewById(R.id.btnFloatPJYYWL).setOnClickListener(v -> selectFloatGame("潘吉亚异闻录", R.id.btnFloatPJYYWL));
        findViewById(R.id.btnFloatXZTM).setOnClickListener(v -> selectFloatGame("贤者同盟", R.id.btnFloatXZTM));
        findViewById(R.id.btnFloatYJWT).setOnClickListener(v -> selectFloatGame("樱井物语", R.id.btnFloatYJWT));

        // 设置开启悬浮窗按钮
        Button btnStartFloat = findViewById(R.id.btnStartFloat);
        if (btnStartFloat != null) {
            btnStartFloat.setOnClickListener(v -> {
                if (!selectedFloatGame.isEmpty()) {
                    openFloatWindow(selectedFloatGame);
                } else {
                    Toast.makeText(this, "请先选择一个游戏", Toast.LENGTH_SHORT).show();
                }
            });
        }
    }

    /**
     * 选择悬浮窗游戏
     */
    private void selectFloatGame(String gameName, int selectedId) {
        selectedFloatGame = gameName;
        System.out.println("选中游戏: " + gameName);

        // 取消所有RadioButton的选中状态
        ((RadioButton) findViewById(R.id.btnFloatXYJH)).setChecked(false);
        ((RadioButton) findViewById(R.id.btnFloatTXBM)).setChecked(false);
        ((RadioButton) findViewById(R.id.btnFloatPJYYWL)).setChecked(false);
        ((RadioButton) findViewById(R.id.btnFloatXZTM)).setChecked(false);
        ((RadioButton) findViewById(R.id.btnFloatYJWT)).setChecked(false);

        // 设置当前选中的RadioButton
        ((RadioButton) findViewById(selectedId)).setChecked(true);
    }

    /**
     * 设置悬浮窗卡片点击事件
     */
    private void setupFloatCardClicks() {
        // 暂时移除卡片点击事件，让用户直接点击RadioButton
        // 这样可以确保RadioGroup正常工作
    }

    /**
     * 显示首页
     */
    private void showHome() {
        showPageWithAnimation(homeContent);
    }

    /**
     * 显示页面并添加动画效果
     */
    private void showPageWithAnimation(View targetView) {
        // 先隐藏所有页面（无动画）
        View[] allPages = {homeContent, cardContent, floatContent, guideContent};

        for (View page : allPages) {
            if (page != targetView) {
                page.setVisibility(View.GONE);
            }
        }

        // 显示目标页面并添加淡入动画
        if (targetView.getVisibility() != View.VISIBLE) {
            targetView.setVisibility(View.VISIBLE);
            targetView.setAlpha(0f);
            targetView.animate()
                    .alpha(1f)
                    .setDuration(250)
                    .start();
        }
    }

    /**
     * 显示发卡网页面
     */
    private void showCard() {
        showPageWithAnimation(cardContent);
    }

    /**
     * 显示攻略页面
     */
    private void showGuide() {
        showPageWithAnimation(guideContent);
    }

    /**
     * 显示悬浮窗页面
     */
    private void showFloat() {
        showPageWithAnimation(floatContent);
    }

    /**
     * 设置导航按钮选中状态
     */
    private void selectNav(Button selected) {

        // 重置所有按钮为未选中状态
        resetButtonToUnselected(navHome);
        resetButtonToUnselected(navCard);
        resetButtonToUnselected(navFloat);
        resetButtonToUnselected(navGuide);

        // 设置选中按钮状态
        setButtonToSelected(selected);

        // 添加按钮点击动画
        addButtonClickAnimation(selected);
    }

    /**
     * 重置按钮为未选中状态，保持圆角效果
     */
    private void resetButtonToUnselected(Button button) {
        button.setTextColor(0xFF636E72);
        // 使用原始的drawable资源，保持圆角效果
        button.setBackgroundResource(0); // 清除背景
        button.setPadding(30, 20, 30, 20); // 保持内边距
    }

    /**
     * 设置按钮为选中状态，保持圆角效果
     */
    private void setButtonToSelected(Button button) {
        button.setTextColor(0xFFFFFFFF);
        // 创建淡紫色的圆角背景
        GradientDrawable drawable = new GradientDrawable();
        drawable.setCornerRadius(50); // 圆角半径
        drawable.setColor(0xFFCCBDFF); // 淡紫色
        button.setBackground(drawable);
        button.setPadding(30, 20, 30, 20); // 保持内边距
    }

    /**
     * 添加按钮点击动画
     */
    private void addButtonClickAnimation(View button) {
        // 简单的缩放动画
        button.animate()
                .scaleX(0.9f)
                .scaleY(0.9f)
                .setDuration(80)
                .withEndAction(() -> {
                    button.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(80);
                });
    }

    /**
     * 打开游戏详情页面
     */
    private void openGameDetail(String gameName) {
        Intent intent = new Intent(this, GameDetailActivity.class);
        intent.putExtra("gameName", gameName);
        startActivity(intent);
    }

    /**
     * 打开悬浮窗
     */
    private void openFloatWindow(String gameName) {
        // 使用悬浮窗管理器来控制悬浮窗的启动
        AlguiFloatWindowManager manager = AlguiFloatWindowManager.getInstance();

        // 检查是否已有悬浮窗在运行
        if (manager.hasAnyFloatWindowRunning()) {
            // 关闭当前悬浮窗，传入当前活跃游戏名
            String currentGame = manager.getCurrentActiveGame();
            if (currentGame != null) {
                manager.closeFloatWindow(currentGame);
            }
        }

        // 启动新悬浮窗
        boolean success = manager.startFloatWindowViaService(this, gameName);
        if (success) {
            Toast.makeText(this, "已请求打开" + gameName + "悬浮窗", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(this, "启动" + gameName + "悬浮窗失败", Toast.LENGTH_SHORT).show();
        }
    }
}
