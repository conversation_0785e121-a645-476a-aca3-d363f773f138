
个人应用开发文档

一共有四个游戏分别是星陨计划，天下布魔，潘吉亚异闻录，贤者同盟，游戏图标你可以先设置名称然后告诉我应该分别把哪张图片添加到哪里，拥有下载的按钮点击之后展示应用详情页然后选择下载的游戏版本E服和工口服然后跳转到下载链接，一个界面是发卡网点击之后在软件内打开网页https://shop.xm1.xyz/Gfc30，一个界面是拥有四个按钮点击之后在软件内打开网页  
"https://www.gamekee.com/xy/",  
"https://www.gamekee.com/txbm/",  
"https://www.gamekee.com/xz/",  
"https://www.gamekee.com/pj/"，一个界面拥有打开悬浮窗的功能，这个原有启动逻辑在mainactivity.java和AlguiActivity.java里，但是他只能打开一个悬浮窗，请你做个单选框，可以选择对应的游戏悬浮窗，分别是星陨计划对应main.java，天下布魔对应Game2FloatWindow.java，潘吉亚异闻录对应Game3FloatWindow.java，贤者同盟对应Game4FloatWindow.java，主题白色美观，写在xml里和mainactivity里

---
注意！！部分界面不用新建类，你可以读取algui库，例如web就在algui库里有

# 关键界面信息补充

## 1. 首页（主界面）
- **主要元素：**
  - 顶部应用标题栏（白色背景，居中标题，简洁风格）
  - 四个游戏的图标与名称（可用 CardView 或圆角矩形背景，排列整齐，图标上方/左侧，名称下方/右侧）
  - 每个游戏下方有“下载”按钮
  - 底部导航栏包含：首页、发卡网、攻略、悬浮窗
- **整体风格：**
  - 主题色：白色为主，搭配淡蓝色/淡紫色点缀
  - 圆角、阴影，现代简洁风
  - 图标风格统一，建议扁平化

## 2. 游戏详情页
- **主要元素：**
  - 游戏大图/Logo
  - 游戏名称、简介
  - 版本选择（单选框：E服/工口服）
  - “下载”按钮（高亮，圆角）
  - 返回按钮
- **整体风格：**
  - 白色背景，内容卡片式分区
  - 主要按钮使用主题色（如蓝色/紫色）
  - 文字清晰，按钮大而易点

## 3. 发卡网界面
- **主要元素：**
  - 内嵌 WebView，加载 https://shop.xm1.xyz/Gfc30
  - 顶部返回按钮
  - 加载进度条（可选）
- **整体风格：**
  - 白色背景，WebView 占满主体
  - 顶部栏与主界面风格一致

## 4. 攻略界面
- **主要元素：**
  - 四个按钮，分别跳转到四个游戏的攻略网页
    - 星陨计划：https://www.gamekee.com/xy/
    - 天下布魔：https://www.gamekee.com/txbm/
    - 贤者同盟：https://www.gamekee.com/xz/
    - 潘吉亚异闻录：https://www.gamekee.com/pj/
  - 按钮可用图标+文字，排列整齐
- **整体风格：**
  - 白色背景，按钮用主题色填充，圆角
  - 按钮点击有水波纹效果

## 5. 悬浮窗选择界面
- **主要元素：**
  - 单选框列表，四个游戏名称
    - 星陨计划（main.java）
    - 天下布魔（Game2FloatWindow.java）
    - 潘吉亚异闻录（Game3FloatWindow.java）
    - 贤者同盟（Game4FloatWindow.java）
  - “打开悬浮窗”按钮
  - 权限申请提示（如未授权）
- **整体风格：**
  - 白色背景，列表卡片式
  - 按钮用主题色，圆角
  - 选中项高亮

  悬浮窗调用逻辑：
  - 选择“星陨计划”时，调用 Main.start(this)
  - 选择“天下布魔”时，调用 Game2FloatWindow.start(this)
  - 选择“潘吉亚异闻录”时，调用 Game3FloatWindow.start(this)
  - 选择“贤者同盟”时，调用 Game4FloatWindow.start(this)
  - 按钮点击后会弹出Toast提示已请求打开对应悬浮窗

## 6. 主题与配色建议
- **主色调：** #FFFFFF（白色）
- **点缀色：** #4F8EF7（蓝色）、#A084E8（紫色，可选）
- **文字色：** #222222（主文字）、#888888（辅助文字）
- **按钮色：** 主题色填充，白色文字
- **圆角：** 8dp-16dp
- **阴影：** 轻微卡片阴影，提升层次感

## 7. 其他建议
- 所有界面适配深色模式（可选）
- 保持界面简洁，避免信息堆叠
- 主要操作按钮突出，易于点击
- 图标、图片资源统一风格

---
