﻿#pragma once
#include<jni.h>
#include "AlguiMemTool.h"
//AlguiMemTool.h - 对接到Java的 JNI 接口
//作者：ByteCat  作者QQ：3353484607   游戏逆向交流QQ群：931212209
extern "C" {

	// 设置目标包名 【注意：这是初始化函数，不调用此函数的话其它内存操作均失效】
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_setPackageName(JNIEnv* env, jobject obj, jstring packageName) {
		const char* nativePackageName = env->GetStringUTFChars(packageName, 0);
		int result = setPackageName(nativePackageName);
		env->ReleaseStringUTFChars(packageName, nativePackageName);
		return result;
	}
	// 获取进程ID
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getPID(JNIEnv* env, jobject obj, jstring packageName) {
		const char* nativePackageName = env->GetStringUTFChars(packageName, 0);
		int result = getPID(nativePackageName);
		env->ReleaseStringUTFChars(packageName, nativePackageName);
		return result;
	}
	// 设置安全写入启用状态
	JNIEXPORT void JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_setIsSecureWrites(JNIEnv* env, jobject obj, jboolean sw) {
		setIsSecureWrites(sw);
	}


	// 获取模块起始地址(基址)
	JNIEXPORT jlong JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getModuleBaseAddr(JNIEnv* env, jobject obj, jstring moduleName, jint headType) {
		const char* nativeModuleName = env->GetStringUTFChars(moduleName, 0);
		unsigned long result = getModuleBaseAddr(nativeModuleName, headType);
		env->ReleaseStringUTFChars(moduleName, nativeModuleName);
		return result;
	}
    // 跳转指针
	JNIEXPORT jlong JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_jump(JNIEnv* env, jobject obj, jlong addr,jint count) {
		return jump(addr,count);
	}
	// 跳转指针 [32位]
	JNIEXPORT jlong JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_jump32(JNIEnv* env, jobject obj, jlong addr) {
		return jump32(addr);
	}
	// 跳转指针 [64位]
	JNIEXPORT jlong JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_jump64(JNIEnv* env, jobject obj, jlong addr) {
		return jump64(addr);
	}
	// 设置指定内存地址指向的值 🛡️该方法已对当前进程进行保护 防止GG模糊🛡️
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_setMemoryAddrValue(JNIEnv* env, jobject obj, jstring value, jlong addr, jint type, jboolean isFree) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		int result = setMemoryAddrValue(nativeValue, addr, type, isFree);
		env->ReleaseStringUTFChars(value, nativeValue);
		return result;
	}
	// 获取指定内存地址的数据
	JNIEXPORT jstring JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getMemoryAddrData(JNIEnv* env, jobject obj, jlong addr, jint type) {
		char* result = getMemoryAddrData(addr, type);
		jstring javaString = env->NewStringUTF(result);
		return javaString;
	}

    JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getDword(JNIEnv *env, jobject obj, jlong addr) {
        return getDword(static_cast<unsigned long>(addr));
    }
    JNIEXPORT jfloat JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getFloat(JNIEnv *env, jobject obj, jlong addr) {
        return getFloat(static_cast<unsigned long>(addr));
    }
    JNIEXPORT jdouble JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getDouble(JNIEnv *env, jobject obj, jlong addr) {
        return getDouble(static_cast<unsigned long>(addr));
    }
    JNIEXPORT jlong JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getQword(JNIEnv *env, jobject obj, jlong addr) {
        return getQword(static_cast<unsigned long>(addr));
    }
    JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getWord(JNIEnv *env, jobject obj, jlong addr) {
        return getWord(static_cast<unsigned long>(addr));
    }
    JNIEXPORT jbyte JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getByte(JNIEnv *env, jobject obj, jlong addr) {
        return getByte(static_cast<unsigned long>(addr));
    }
    JNIEXPORT jstring JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getString(JNIEnv *env, jobject obj, jlong addr) {
        char* str = getString(static_cast<unsigned long>(addr));
        return env->NewStringUTF(str);
    }
    
    JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getDword_1t(JNIEnv *env, jobject obj, jlong addr) {
        return getDword_t(static_cast<unsigned long>(addr));  
    }
    JNIEXPORT jfloat JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getFloat_1t(JNIEnv *env, jobject obj, jlong addr) {
        return getFloat_t(static_cast<unsigned long>(addr));  
    }
    JNIEXPORT jdouble JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getDouble_1t(JNIEnv *env, jobject obj, jlong addr) {
        return getDouble_t(static_cast<unsigned long>(addr));  
    }
    JNIEXPORT jlong JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getQword_1t(JNIEnv *env, jobject obj, jlong addr) {
        return getQword_t(static_cast<unsigned long>(addr)); 
    }
    JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getWord_1t(JNIEnv *env, jobject obj, jlong addr) {
        return getWord_t(static_cast<unsigned long>(addr));  
    }
    JNIEXPORT jbyte JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getByte_1t(JNIEnv *env, jobject obj, jlong addr) {
        return getByte_t(static_cast<unsigned long>(addr)); 
    }
    JNIEXPORT jstring JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getString_1t(JNIEnv *env, jobject obj, jlong startAddr) {
        char* str = getString_t(static_cast<unsigned long>(startAddr));  
        return env->NewStringUTF(str);  
    }




	// 设置要搜索的内存区域
	JNIEXPORT void JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_setMemoryArea
	(JNIEnv* env, jobject obj, jint memoryArea) {
		setMemoryArea(memoryArea);
	}
	// 内存搜索
	JNIEXPORT jlongArray JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_MemorySearch
	(JNIEnv* env, jobject obj, jstring value, jint type) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		std::vector<unsigned long> results = MemorySearch(nativeValue, type);
		env->ReleaseStringUTFChars(value, nativeValue);

		jlongArray resultArray = env->NewLongArray(results.size());
		jlong* arrayElements = new jlong[results.size()];
		for (size_t i = 0; i < results.size(); ++i) {
			arrayElements[i] = static_cast<jlong>(results[i]);
		}
		env->SetLongArrayRegion(resultArray, 0, results.size(), arrayElements);
		delete[] arrayElements;
		return resultArray;
	}
	// 内存搜索范围值
	JNIEXPORT jlongArray JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_MemorySearchRange
	(JNIEnv* env, jobject obj, jstring value, jint type) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		std::vector<unsigned long> results = MemorySearchRange(nativeValue, type);
		env->ReleaseStringUTFChars(value, nativeValue);

		jlongArray resultArray = env->NewLongArray(results.size());
		jlong* arrayElements = new jlong[results.size()];
		for (size_t i = 0; i < results.size(); ++i) {
			arrayElements[i] = static_cast<jlong>(results[i]);
		}
		env->SetLongArrayRegion(resultArray, 0, results.size(), arrayElements);
		delete[] arrayElements;
		return resultArray;
	}
	// 联合内存搜索
	JNIEXPORT jlongArray JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_MemorySearchUnited
	(JNIEnv* env, jobject obj, jstring value, jint type) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		std::vector<unsigned long> results = MemorySearchUnited(nativeValue, type);
		env->ReleaseStringUTFChars(value, nativeValue);

		jlongArray resultArray = env->NewLongArray(results.size());
		jlong* arrayElements = new jlong[results.size()];
		for (size_t i = 0; i < results.size(); ++i) {
			arrayElements[i] = static_cast<jlong>(results[i]);
		}
		env->SetLongArrayRegion(resultArray, 0, results.size(), arrayElements);
		delete[] arrayElements;
		return resultArray;
	}
	// 偏移改善结果
	JNIEXPORT jlongArray JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_ImproveOffset
	(JNIEnv* env, jobject obj, jstring value, jint type, jlong offset) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		std::vector<unsigned long> results = ImproveOffset(nativeValue, type, static_cast<unsigned long>(offset));
		env->ReleaseStringUTFChars(value, nativeValue);

		jlongArray resultArray = env->NewLongArray(results.size());
		jlong* arrayElements = new jlong[results.size()];
		for (size_t i = 0; i < results.size(); ++i) {
			arrayElements[i] = static_cast<jlong>(results[i]);
		}
		env->SetLongArrayRegion(resultArray, 0, results.size(), arrayElements);
		delete[] arrayElements;
		return resultArray;
	}
	// 偏移范围改善结果
	JNIEXPORT jlongArray JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_ImproveOffsetRange
	(JNIEnv* env, jobject obj, jstring value, jint type, jlong offset) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		std::vector<unsigned long> results = ImproveOffsetRange(nativeValue, type, static_cast<unsigned long>(offset));
		env->ReleaseStringUTFChars(value, nativeValue);

		jlongArray resultArray = env->NewLongArray(results.size());
		jlong* arrayElements = new jlong[results.size()];
		for (size_t i = 0; i < results.size(); ++i) {
			arrayElements[i] = static_cast<jlong>(results[i]);
		}
		env->SetLongArrayRegion(resultArray, 0, results.size(), arrayElements);
		delete[] arrayElements;
		return resultArray;
	}
	// 偏移联合改善结果
	JNIEXPORT jlongArray JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_ImproveOffsetUnited
	(JNIEnv* env, jobject obj, jstring value, jint type, jlong offset) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		std::vector<unsigned long> results = ImproveOffsetUnited(nativeValue, type, static_cast<unsigned long>(offset));
		env->ReleaseStringUTFChars(value, nativeValue);

		jlongArray resultArray = env->NewLongArray(results.size());
		jlong* arrayElements = new jlong[results.size()];
		for (size_t i = 0; i < results.size(); ++i) {
			arrayElements[i] = static_cast<jlong>(results[i]);
		}
		env->SetLongArrayRegion(resultArray, 0, results.size(), arrayElements);
		delete[] arrayElements;
		return resultArray;
	}
	// 直接改善结果
	JNIEXPORT jlongArray JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_ImproveValue
	(JNIEnv* env, jobject obj, jstring value, jint type) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		std::vector<unsigned long> results = ImproveValue(nativeValue, type);
		env->ReleaseStringUTFChars(value, nativeValue);

		jlongArray resultArray = env->NewLongArray(results.size());
		jlong* arrayElements = new jlong[results.size()];
		for (size_t i = 0; i < results.size(); ++i) {
			arrayElements[i] = static_cast<jlong>(results[i]);
		}
		env->SetLongArrayRegion(resultArray, 0, results.size(), arrayElements);
		delete[] arrayElements;
		return resultArray;
	}
	// 结果偏移写入数据
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_MemoryOffsetWrite
	(JNIEnv* env, jobject obj, jstring value, jint type, jlong offset, jboolean isFree) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		int result = MemoryOffsetWrite(nativeValue, type, static_cast<unsigned long>(offset), isFree);
		env->ReleaseStringUTFChars(value, nativeValue);
		return result;
	}
	// 获取最终搜索结果数量
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getResultCount
	(JNIEnv* env, jobject obj) {
		return getResultCount();
	}
	// 获取最终搜索结果列表
	JNIEXPORT jlongArray JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getResultList
	(JNIEnv* env, jobject obj) {
		std::vector<unsigned long> results = getResultList();
		jlongArray resultArray = env->NewLongArray(results.size());
		jlong* arrayElements = new jlong[results.size()];
		for (size_t i = 0; i < results.size(); ++i) {
			arrayElements[i] = static_cast<jlong>(results[i]);
		}
		env->SetLongArrayRegion(resultArray, 0, results.size(), arrayElements);
		delete[] arrayElements;
		return resultArray;
	}
	// 打印最终搜索结果列表到指定文件
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_printResultListToFile
	(JNIEnv* env, jobject obj, jstring filePath) {
		const char* nativeFilePath = env->GetStringUTFChars(filePath, 0);
		int result = printResultListToFile(nativeFilePath);
		env->ReleaseStringUTFChars(filePath, nativeFilePath);
		return result;
	}
	// 清空最终搜索结果列表
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_clearResultList
	(JNIEnv* env, jobject obj) {
		return clearResultList();
	}




	// 获取冻结修改项目列表 【Java需要自行创建Item类再创建列表接收】
	JNIEXPORT jobjectArray  JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getFreezeList(JNIEnv* env, jobject obj) {
		vector<Item> freezeList = getFreezeList();
		jclass freezeItemClass = env->FindClass("irene/window/algui/Item");
		jmethodID constructor = env->GetMethodID(freezeItemClass, "<init>", "(Ljava/lang/String;J.I)V");
		jobjectArray result = env->NewObjectArray(freezeList.size(), freezeItemClass, NULL);

		for (size_t i = 0; i < freezeList.size(); ++i) {
			Item& item = freezeList[i];
			jstring value = env->NewStringUTF(item.value);
			jobject freezeItemObj = env->NewObject(freezeItemClass, constructor, value, item.addr, item.type);
			env->SetObjectArrayElement(result, i, freezeItemObj);
		}

		return result;
	}
	// 设置冻结修改延迟
	JNIEXPORT void JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_setFreezeDelayMs(JNIEnv* env, jobject obj, jint delay) {
		setFreezeDelayMs(static_cast<uint32_t>(delay));
	}
	// 获取冻结修改项目数量
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getFreezeNum(JNIEnv* env, jobject obj) {
		return static_cast<jint>(getFreezeNum());
	}
	// 添加一个冻结修改项目
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_addFreezeItem(JNIEnv* env, jobject obj, jstring value, jlong addr, jint type) {
		const char* nativeValue = env->GetStringUTFChars(value, 0);
		int result = addFreezeItem(nativeValue, static_cast<unsigned long>(addr), static_cast<int>(type));
		env->ReleaseStringUTFChars(value, nativeValue);
		return static_cast<jint>(result);
	}
	// 移除一个冻结修改项目
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_removeFreezeItem(JNIEnv* env, jobject obj, jlong addr) {
		return static_cast<jint>(removeFreezeItem(static_cast<unsigned long>(addr)));
	}
	// 移除所有冻结修改项目
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_removeAllFreezeItem(JNIEnv* env, jobject obj) {
		return static_cast<jint>(removeAllFreezeItem());
	}
	// 冻结循环修改线程忽略$
	// 
	// 开始冻结所有修改项目
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_startAllFreeze(JNIEnv* env, jobject obj) {
		return static_cast<jint>(startAllFreeze());
	}
	// 停止冻结所有修改项目
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_stopAllFreeze(JNIEnv* env, jobject obj) {
		return static_cast<jint>(stopAllFreeze());
	}
	// 打印冻结列表到指定文件
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_printFreezeListToFile(JNIEnv* env, jobject obj, jstring filePath) {
		const char* nativeFilePath = env->GetStringUTFChars(filePath, 0);
		int result = printFreezeListToFile(nativeFilePath);
		env->ReleaseStringUTFChars(filePath, nativeFilePath);
		return static_cast<jint>(result);
	}



	// 获取指定内存地址的Maps映射行
	JNIEXPORT jstring JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getMemoryAddrMapLine
	(JNIEnv* env, jobject obj, jlong address) {
		char* result = getMemoryAddrMapLine(static_cast<unsigned long>(address));
		jstring jResult = env->NewStringUTF(result);
		return jResult;
	}
	// 获取Maps映射行所在内存区域名称
	JNIEXPORT jstring JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getMapLineMemoryAreaName
	(JNIEnv* env, jobject obj, jstring mapLine) {
		const char* nativeMapLine = env->GetStringUTFChars(mapLine, 0);
		char* result = getMapLineMemoryAreaName(nativeMapLine);
		jstring jResult = env->NewStringUTF(result);
		env->ReleaseStringUTFChars(mapLine, nativeMapLine);
		return jResult;
	}
	// 获取指定内存id的内存名称
	JNIEXPORT jstring JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getMemoryAreaIdName
	(JNIEnv* env, jobject obj, jint memid) {
		char* result = getMemoryAreaIdName(static_cast<int>(memid));
		jstring jResult = env->NewStringUTF(result);
		return jResult;
	}
	// 获取当前内存名称
	JNIEXPORT jstring JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getMemoryAreaName
	(JNIEnv* env, jobject obj) {
		char* result = getMemoryAreaName();
		jstring jResult = env->NewStringUTF(result);
		return jResult;
	}
	// 获取指定数据类型id的数据类型名称
	JNIEXPORT jstring JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_getDataTypeName
	(JNIEnv* env, jobject obj, jint typeId) {
		char* result = getDataTypeName(static_cast<int>(typeId));
		jstring jResult = env->NewStringUTF(result);
		return jResult;
	}




	// 杀掉指定包名的进程
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_killProcess_1Root
	(JNIEnv* env, jobject obj, jstring packageName) {
		const char* nativePackageName = env->GetStringUTFChars(packageName, 0);
		int result = killProcess_Root(nativePackageName);
		env->ReleaseStringUTFChars(packageName, nativePackageName);
		return result;
	}
	// 暂停指定包名的进程
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_stopProcess_1Root(JNIEnv* env, jobject obj, jstring packageName) {
		const char* nativePackageName = env->GetStringUTFChars(packageName, 0);
		int result = stopProcess_Root(nativePackageName);
		env->ReleaseStringUTFChars(packageName, nativePackageName);
		return result;
	}
	// 恢复被暂停的指定包名的进程
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_resumeProcess_1Root(JNIEnv* env, jobject obj, jstring packageName) {
		const char* nativePackageName = env->GetStringUTFChars(packageName, 0);
		int result = resumeProcess_Root(nativePackageName);
		env->ReleaseStringUTFChars(packageName, nativePackageName);
		return result;
	}
	// 杀掉所有inotify监视器，防止游戏监视文件变化
	JNIEXPORT void JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_killAllInotify_1Root
	(JNIEnv* env, jobject obj) {
		killAllInotify_Root();
	}
	// 杀掉GG修改器
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_killGG_1Root
	(JNIEnv* env, jobject obj) {
		return killGG_Root();
	}
	// 杀掉XS脚本
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_killXscript_1Root
	(JNIEnv* env, jobject obj) {
		return killXscript_Root();
	}
	// 重启手机
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_rebootsystem_1Root
	(JNIEnv* env, jobject obj) {
		return rebootsystem_Root();
	}
	// 静默安装指定路径的APK安装包
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_installapk_1Root
	(JNIEnv* env, jobject obj, jstring apkPackagePath) {
		const char* nativePath = env->GetStringUTFChars(apkPackagePath, 0);
		int result = installapk_Root(nativePath);
		env->ReleaseStringUTFChars(apkPackagePath, nativePath);
		return result;
	}
	// 静默卸载指定包名的APK软件
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_uninstallapk_1Root
	(JNIEnv* env, jobject obj, jstring packageName) {
		const char* nativePackageName = env->GetStringUTFChars(packageName, 0);
		int result = uninstallapk_Root(nativePackageName);
		env->ReleaseStringUTFChars(packageName, nativePackageName);
		return result;
	}
	// 执行命令
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_Cmd
	(JNIEnv* env, jobject obj, jstring packageName) {
		const char* nativePackageName = env->GetStringUTFChars(packageName, 0);
		int result = Cmd(nativePackageName);
		env->ReleaseStringUTFChars(packageName, nativePackageName);
		return result;
	}
	// 执行超级命令
	JNIEXPORT jint JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_Cmd_1Root
	(JNIEnv* env, jobject obj, jstring packageName) {
		const char* nativePackageName = env->GetStringUTFChars(packageName, 0);
		int result = Cmd_Root(nativePackageName);
		env->ReleaseStringUTFChars(packageName, nativePackageName);
		return result;
	}
}
