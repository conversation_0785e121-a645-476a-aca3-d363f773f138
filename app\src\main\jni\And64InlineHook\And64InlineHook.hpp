/*
 *  @date   : 2018/04/18
 *  <AUTHOR> Rprop (<EMAIL>)
 *  https://github.com/Rprop/And64InlineHook
 */
/*
//2023/6/26 by艾琳汉化(团队:无尽的四月) 游戏MOD交流群368969530

 麻省理工学院执照

 版权所有 （c） 2018 Rprop （<EMAIL>）

 特此免费授予任何获得副本的人
 本软件和相关文档文件（“软件”），以处理
 在软件中不受限制，包括但不限于权利
 使用、复制、修改、合并、发布、分发、再许可和/或出售
 软件的副本，并允许软件所针对的人员
 提供，但须符合以下条件：

 上述版权声明和本许可声明应包含在所有
 本软件的副本或大部分内容。

 本软件按“原样”提供，不提供任何形式的明示或保证
 暗示，包括但不限于适销性保证，
 适用于特定目的且不侵权。在任何情况下，都不得
 作者或版权所有者对任何索赔、损害赔偿或其他索赔、损害赔偿或其他责任
 责任，无论是在合同、侵权或其他诉讼中，由以下原因引起：
 出于或与本软件有关，或在
 软件。
 */
#pragma once
#define A64_MAX_BACKUPS 256

#ifdef __cplusplus
extern "C" {
#endif

void A64HookFunction(void *const symbol, void *const replace, void **result);
void *A64HookFunctionV(void *const symbol, void *const replace, void *const rwx, const uintptr_t rwx_size);

#ifdef __cplusplus
}
#endif