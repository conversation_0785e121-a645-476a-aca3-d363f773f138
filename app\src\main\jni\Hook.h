#include "AlguiLog.h"
#include "AlguiMemTool.h"
#include <unistd.h>
#include <cstdio>
#include <cstring>
#include <string>
#include <cstdlib>
#if defined(__aarch64__) 
#include <And64InlineHook/And64InlineHook.hpp>
#else
#include <Substrate/SubstrateHook.h>
#include <Substrate/CydiaSubstrate.h>
#endif

//检查动态库是否已加载
bool isLibraryLoaded(const char *libraryName) {
    char line[512] = {0};
    FILE *fp = fopen("/proc/self/maps", "rt");
    if (fp != NULL) {
        while (fgets(line, sizeof(line), fp)) {
            std::string a = line;
            if (strstr(line, libraryName)) {
                return true;
            }
        }
        fclose(fp);
    }
    return false;
}

//动态库基址静态偏移到地址
unsigned long getLibOffsetToAddr(const char *library,const char* offset) {
    char filename[0xFF] = {0},
         buffer[1024] = {0};
    FILE *fp = NULL;     
    unsigned long address = 0;

    sprintf(filename, "/proc/self/maps");
    fp = fopen(filename, "rt");
    if (fp == NULL) { 
        perror("fopen");
        LOGI("hook", "maps打开失败");
        goto done;
    }

    //按行读取当前进程的maps中的内容
    while (fgets(buffer, sizeof(buffer), fp)) {
        //检查当前行是否包含目标库名
        if (strstr(buffer, library)) {
            //获取该库的基址
            address = (unsigned long) strtoul(buffer, NULL, 16); // 从当前行提取出基址（16进制）
            goto done;
        }
    }

done:
    if (fp) {
        fclose(fp);
    }

    if(address==0)
        return 0;
    //字符串偏移转换
    int base = 16;
    uintptr_t of=0;
    static_assert(sizeof(uintptr_t) == sizeof(unsigned long)
                  || sizeof(uintptr_t) == sizeof(unsigned long long),
                  "请使用字符串形式offset以处理转换");
                  
    if (sizeof(uintptr_t) == sizeof(unsigned long)) {
        of = strtoul(offset, nullptr, base);
    }else{
        of = strtoull(offset, nullptr, base);
    }
    
    return (reinterpret_cast<unsigned long>(address + of));
}






void hook(void *offset, void* ptr, void **orig)
{
#if defined(__aarch64__)
    A64HookFunction(offset, ptr, orig);
#else
    MSHookFunction(offset, ptr, orig);
#endif
}

//通过函数相对库的偏移量进行HOOK
#define HOOK(lib, offset, newFun, oldFun) hook((void *)getLibOffsetToAddr(lib, offset), (void *)newFun, (void **)&oldFun)
//通过函数符号进行HOOK
#define HOOKSYM(lib, sym, newFun, oldFun) hook(dlsym(dlopen(lib, 4), sym), (void *)newFun, (void **)&oldFun)
