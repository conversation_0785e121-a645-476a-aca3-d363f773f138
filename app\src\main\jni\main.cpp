﻿#pragma once
#include<jni.h>
#include "AlguiLog.h"
#include "AlguiMemTool.h"
#include "AlguiMemTool_jni.h"
#include "Hook.h"
#include "stringpt.h"//对于重要字符串数据，请使用STRING("");修饰来防止暴露字符串常量

//作者：ByteCat 作者QQ：3353484607   游戏逆向交流QQ群：931212209
#if defined(__arm__) && !defined(__aarch64__) //针对 ARM 32 位架构 
#define ABI "ARM32"
#elif defined(__aarch64__) //针对 ARM 64 位架构 
#define ABI "ARM64"
#elif defined(__x86_64__) || defined(_M_X64) //针对 x64 架构 
#define ABI "x64"
#elif defined(__i386__) || defined(_M_IX86) //针对 x86 架构 
#define ABI "x86"
#else //其他架构或未知架构
#define ABI "null"
#endif	


extern "C" {

    //HOOK示例 修改血量
    //确保方法签名与游戏原生方法签名保持一致
    bool isModHP = false;//控制开关
    void (*nativeMethod)(void* t);//存储游戏原生方法
    void newMethod(void* t) {//我们替换后的新方法
        //通过开关控制修改 只有开启时我们才改变变量数据
       if(isModHP)
           //如果需要修改方法所在类的变量
           //可以通过方法隐式传入的this指针进行偏移 间接获取到该变量
           *(int *) ((uint64_t) t + 0xC) = 99999;
       //记得执行游戏原生方法以免干扰游戏逻辑
       nativeMethod(t);
       //如果方法是有返回值的 那么我们拦截返回自己需要的数据
       //return 999;
    }
    //启动时在此线程进行HOOK
    void *hook_thread(void *) {
         do {
             sleep(1);//等待需要HOOK的库加载完成再进行HOOK
         } while (!isLibraryLoaded(STRING("libil2cpp.so")));
         //通过偏移量对函数HOOK
         HOOK(STRING("libil2cpp.so"), STRING("0x4ACD04"),newMethod, nativeMethod);
         //通过符号对函数HOOK
         //HOOKSYM("libil2cpp.so", "_ZN3men11FixedUpdateEv", newMethod, nativeMethod);
         return NULL;
    }
    
    //从Java控制jni HOOK的方法(不支持root) 参数：功能ID，该功能开关状态，修改值(可选 对于在拖动条或输入框自定义修改值)
    //Java层方法签名：public static native void JniHook(int id,boolean isSwitch,double value);
    JNIEXPORT void JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_JniHook(JNIEnv* env, jclass cls,jint id,jboolean isSwitch,jdouble value)
    {
          //id=Java传入的功能ID
          //isSwitch=Java传入的开关 
          //value=Java传入的值(对于需要使用拖动条或输入框自定义修改)
          
          //自己定义对应Hook功能的ID 比如下面0代表修改HP
          switch(id){
              case 0:
                  isModHP=isSwitch;//设置开关
                  break;
              case 1:
                  break;
              //...
          }
	}

     //从Java控制jni内存外挂功能的方法 参数：功能ID，该功能开关状态，修改值(可选 对于在拖动条或输入框自定义修改值)
     //Java层方法签名：public static native void JniSwitch(int id,boolean isSwitch,String value);
    JNIEXPORT void JNICALL Java_com_bytecat_algui_AlguiHacker_AlguiNativeMemTool_JniSwitch(JNIEnv* env, jclass cls,jint id,jboolean isSwitch,jstring jvalue)
    {
          const char* value = env->GetStringUTFChars(jvalue, 0);
       
          //id=Java传入的功能ID
          //isSwitch=Java传入的开关 
          //value=Java传入的值(对于需要使用拖动条或输入框自定义修改)
          
          //自己定义对应功能的ID 比如下面0代表飞天功能
          switch(id){
              case 0:
                  if(isSwitch){
                        //开启飞天
                        clearResultList();//清空之前的搜索结果
                        setPackageName(STRING("com.fingersoft.hillclimb.noncmcc"));//设置包名
                        setMemoryArea(RANGE_ANONYMOUS);//设置内存
                        MemorySearch(STRING("120"), TYPE_DWORD);//内存搜索 【主特征码 支持范围，联合】
                        ImproveOffset(STRING("1"), TYPE_DWORD, -8);//偏移筛选特征码 【副特征码1 支持范围，联合】
                        ImproveOffset(STRING("12"), TYPE_DWORD, -4);//偏移筛选特征码 【副特征码2】
                        //n个副特征码...
                        MemoryOffsetWrite(STRING("999999"), TYPE_DWORD, 1, false);//筛选结果偏移修改 【如果需要冻结将false改为true】
                        //对于自定义修改
                        //MemoryOffsetWrite(value, TYPE_DWORD, 1, false);//筛选结果偏移修改 【如果需要冻结将false改为true】
                        clearResultList();//修改完成 则清空这次的搜索结果
                  }else{
                         //关闭飞天
                        clearResultList();//清空之前的搜索结果
                        setPackageName(STRING("com.fingersoft.hillclimb.noncmcc"));//设置包名
                        setMemoryArea(RANGE_ANONYMOUS);//设置内存
                        MemorySearch(STRING("120"), TYPE_DWORD);//内存搜索 【主特征码 支持范围，联合】
                        ImproveOffset(STRING("1"), TYPE_DWORD, -8);//偏移筛选特征码 【副特征码1 支持范围，联合】
                        ImproveOffset(STRING("12"), TYPE_DWORD, -4);//偏移筛选特征码 【副特征码2】
                        //n个副特征码...
                        MemoryOffsetWrite(STRING("999999"), TYPE_DWORD, 1, false);//筛选结果偏移修改 【如果需要冻结将false改为true】
                        clearResultList();//修改完成 则清空这次的搜索结果
                   
                  }
                  break;
              case 1:
                  if(isSwitch){
                      //开启
                  }else{
                      //关闭
                  }
                  break;
              //...
          }
          env->ReleaseStringUTFChars(jvalue, value);
	}
    
    //内存修改示例
    void __example__() {
        //动态基址内存修改示例
        setPackageName("com.yodo1.SkiSafari.yodo1");//设置包名
        unsigned long sAddr = getModuleBaseAddr("libil2cpp.so:bss", HEAD_CB);//获取模块基址 【模块名内使用[1]来代表获取第几个模块的基址 xa模块传HEAD_XA  cb模块传HEAD_CB  cd模块传HEAD_CD】
        unsigned long daddr = jump64(jump64(jump64(sAddr + 0x88B8) + 0xA0) + 0x118) + 0x18;//跳转指针 跳到目标地址 【32位使用 jump32  64位使用jump64】
        //跳转指针如果不直观请使用下面这种方式
        /*unsigned long p1 = jumpPointer64(sAddr + 0x88B8);
        unsigned long p2 = jumpPointer64(p1 + 0xA0);
        unsigned long p3 = jumpPointer64(p2 + 0x118);
        unsigned long addr = p3 + 0x18;*/
        setMemoryAddrValue("999999", daddr, TYPE_DWORD, false);//修改目标值 【如果需要冻结将false改为true】

        //静态基址内存修改示例
        setPackageName("com.fingersoft.hillclimb.noncmcc");//设置包名
        unsigned long jaddr = getModuleBaseAddr("libgame.so", HEAD_XA) + 0x376E10; //从模块基址偏移获取目标地址 【模块名内使用[1]来代表获取第几个模块的基址 xa模块传HEAD_XA  cb模块传HEAD_CB  cd模块传HEAD_CD】
        setMemoryAddrValue("999999", jaddr, TYPE_DWORD, false);//修改目标值 【如果需要冻结将false改为true】


        //特征码内存修改示例
        clearResultList();//清空之前的搜索结果
        setPackageName("com.fingersoft.hillclimb.noncmcc");//设置包名
        setMemoryArea(RANGE_ANONYMOUS);//设置内存
        MemorySearch("120", TYPE_DWORD);//内存搜索 【主特征码 支持范围，联合】
        ImproveOffset("1", TYPE_DWORD, -8);//偏移筛选特征码 【副特征码1 支持范围，联合】
        ImproveOffset("12", TYPE_DWORD, -4);//偏移筛选特征码 【副特征码2】
        //n个副特征码...
        MemoryOffsetWrite("999999", TYPE_DWORD, 1, false);//筛选结果偏移修改 【如果需要冻结将false改为true】
        clearResultList();//修改完成 则清空这次的搜索结果

        //范围搜索示例
        clearResultList();//清空之前的搜索结果
        setPackageName("com.fingersoft.hillclimb.noncmcc");//设置包名
        setMemoryArea(RANGE_ANONYMOUS);//设置内存
        MemorySearch("1~20", TYPE_DWORD);//内存搜索 【主特征码】将搜索出1~20之间的数据
        ImproveOffset("1", TYPE_DWORD, -8);//偏移筛选特征码 【副特征码1】
        ImproveOffset("12", TYPE_DWORD, -4);//偏移筛选特征码 【副特征码2】
        //n个副特征码...
        MemoryOffsetWrite("999999", TYPE_DWORD, 1, false);//筛选结果偏移修改 【如果需要冻结将false改为true】
        clearResultList();//修改完成 则清空这次的搜索结果

        //联合搜索示例
        clearResultList();//清空之前的搜索结果
        setPackageName("com.fingersoft.hillclimb.noncmcc");//设置包名
        setMemoryArea(RANGE_ANONYMOUS);//设置内存
        MemorySearch("1D;0.5F;27.1E:100", TYPE_DWORD);//联合内存搜索[不按顺序]
        //MemorySearch("1D;0.5F;27.1E::100", TYPE_DWORD);//联合内存搜索[按顺序]
        //MemorySearch("1;5;27:100", TYPE_DWORD);//联合内存搜索[值没有加类型符的情况将使用第二个参数作为默认类型]
        //MemorySearch("1;5;27", TYPE_DWORD);//联合内存搜索[不按顺序 没有范围的情况将使用默认范围500]
        //MemorySearch("1;5;27::", TYPE_DWORD);//联合内存搜索[按顺序 没有范围的情况将使用默认范围500]
        MemorySearch("98;30~35;27~29::", TYPE_DWORD);//联合搜索中也可加范围值
        //改善也和以上一样都支持 反正都和GG修改器相同 就不示例了
        ImproveOffset("0.5", TYPE_FLOAT, 0);//改善
        MemoryOffsetWrite("999999", TYPE_FLOAT, 0, false);//修改 【如果需要冻结将false改为true】
        clearResultList();//修改完成 则清空这次的搜索结果

        //其它拓展函数 [只展示实用性功能，其它功能自行在头文件查看]
        char* value = getMemoryAddrData(0x050A4AA0, TYPE_FLOAT);//读取指定内存地址的指定类型的值
        killProcess_Root("com.fingersoft.hillclimb.noncmcc"); // 杀掉指定包名的进程
        stopProcess_Root("com.fingersoft.hillclimb.noncmcc"); // 暂停游戏
        resumeProcess_Root("com.fingersoft.hillclimb.noncmcc"); // 恢复游戏
        printResultListToFile("/storage/emulated/0/bytecatDebug.log");//打印内存搜索筛选结果列表到文件 【传入文件绝对路径】
        printFreezeListToFile("/storage/emulated/0/bytecatDebug.log");//打印冻结列表到文件【传入文件绝对路径】
        //setFreezeDelayMs(200);//设置冻结修改延迟【毫秒】
        killAllInotify_Root(); //杀掉所有inotify监视器，防止游戏监视文件变化 【需要ROOT】
        killGG_Root(); // 杀掉GG修改器 【需要ROOT】
        killXscript_Root(); // 杀掉XS脚本 【需要ROOT】
        //.....
	}


    //初始化Jni
    JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
        JNIEnv* env;
        if (vm->GetEnv((void**)&env, JNI_VERSION_1_6) != JNI_OK) {
            return -1;
        }

        LOGI(STRING("Algui动态共享库"), STRING("当前在 %s 运行"),ABI);
        //创建HOOK线程，对于无需HOOK请注释避免影响性能
        pthread_t ptid;
        pthread_create(&ptid, NULL, hook_thread, NULL);
        return JNI_VERSION_1_6;
	}


}
