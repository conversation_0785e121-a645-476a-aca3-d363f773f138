<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <layer-list>
            <item android:top="2dp" android:left="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="#40000000" />
                    <corners android:radius="25dp" />
                </shape>
            </item>
            <item android:bottom="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#DDA0DD"
                        android:centerColor="#B19CD9"
                        android:endColor="#9370DB"
                        android:angle="45" />
                    <corners android:radius="25dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <layer-list>
            <item android:top="4dp" android:left="4dp">
                <shape android:shape="rectangle">
                    <solid android:color="#40000000" />
                    <corners android:radius="25dp" />
                </shape>
            </item>
            <item android:bottom="4dp" android:right="4dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#E6E6FA"
                        android:centerColor="#DDA0DD"
                        android:endColor="#B19CD9"
                        android:angle="45" />
                    <corners android:radius="25dp" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>
