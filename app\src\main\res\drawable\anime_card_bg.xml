<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 阴影层 -->
    <item android:top="4dp" android:left="4dp">
        <shape android:shape="rectangle">
            <solid android:color="#40000000" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- 主卡片层 -->
    <item android:bottom="4dp" android:right="4dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FFFFFF"
                android:endColor="#F8F9FA"
                android:angle="45" />
            <corners android:radius="20dp" />
            <stroke 
                android:width="2dp" 
                android:color="#E1E8ED" />
        </shape>
    </item>
</layer-list>
