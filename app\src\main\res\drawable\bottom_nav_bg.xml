<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 阴影层 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#20000000" />
            <corners
                android:topLeftRadius="25dp"
                android:topRightRadius="25dp" />
        </shape>
    </item>

    <!-- 主背景层 -->
    <item android:bottom="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FFFFFF"
                android:endColor="#F8F9FA"
                android:angle="90" />
            <corners
                android:topLeftRadius="25dp"
                android:topRightRadius="25dp" />
            <stroke
                android:width="1dp"
                android:color="#E1E8ED" />
        </shape>
    </item>

    <!-- 顶部装饰线 -->
    <item android:bottom="2dp" android:top="0dp" android:left="20dp" android:right="20dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FF6B9D"
                android:endColor="#A29BFE"
                android:angle="0" />
            <size android:height="3dp" />
            <corners android:radius="2dp" />
        </shape>
    </item>
</layer-list>
