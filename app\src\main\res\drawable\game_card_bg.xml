<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 外层光晕效果 -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#20FF6B9D"
                android:endColor="#20A29BFE"
                android:angle="45" />
            <corners android:radius="24dp" />
        </shape>
    </item>
    
    <!-- 主卡片 -->
    <item android:top="6dp" android:left="6dp" android:right="6dp" android:bottom="6dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FFFFFF"
                android:endColor="#FAFBFC"
                android:angle="90" />
            <corners android:radius="20dp" />
            <stroke 
                android:width="1dp" 
                android:color="#E1E8ED" />
        </shape>
    </item>
    
    <!-- 内层装饰 -->
    <item android:top="8dp" android:left="8dp" android:right="8dp" android:bottom="8dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="1dp" 
                android:color="#10FF6B9D" />
            <corners android:radius="18dp" />
        </shape>
    </item>
</layer-list>
