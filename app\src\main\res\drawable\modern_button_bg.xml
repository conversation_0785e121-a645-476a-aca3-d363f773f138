<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape>
            <gradient
                android:startColor="#5a67d8"
                android:endColor="#667eea"
                android:angle="45" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape>
            <gradient
                android:startColor="#667eea"
                android:endColor="#764ba2"
                android:angle="45" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
