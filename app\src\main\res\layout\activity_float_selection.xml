<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_background"
    android:clickable="true"
    android:focusable="true">

    <!-- 返回按钮 -->
    <Button
        android:id="@+id/btnBackFloat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="← 返回"
        android:textColor="#FFFFFF"
        android:background="@android:color/transparent"
        android:textSize="16sp"
        android:padding="16dp"
        android:layout_margin="16dp" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/modern_card_bg"
        android:padding="24dp"
        android:layout_margin="20dp"
        android:elevation="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="悬浮窗选择"
            android:textColor="#333333"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="请选择要打开的游戏悬浮窗"
            android:textColor="#666666"
            android:textSize="14sp"
            android:gravity="center" />

    </LinearLayout>

    <!-- 游戏选择区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:background="@drawable/modern_card_bg"
        android:layout_margin="20dp"
        android:padding="20dp"
        android:elevation="6dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="选择游戏"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <RadioGroup
            android:id="@+id/radioGroupFloat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RadioButton
                android:id="@+id/rbXYJH"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="星陨计划"
                android:textColor="#333333"
                android:textSize="16sp"
                android:padding="12dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/modern_card_bg" />

            <RadioButton
                android:id="@+id/rbTXBM"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="天下布魔"
                android:textColor="#333333"
                android:textSize="16sp"
                android:padding="12dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/modern_card_bg" />

            <RadioButton
                android:id="@+id/rbPJYYWL"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="潘吉亚异闻录"
                android:textColor="#333333"
                android:textSize="16sp"
                android:padding="12dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/modern_card_bg" />

            <RadioButton
                android:id="@+id/rbXZTM"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="贤者同盟"
                android:textColor="#333333"
                android:textSize="16sp"
                android:padding="12dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/modern_card_bg" />

            <RadioButton
                android:id="@+id/rbYJWT"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="樱井物语"
                android:textColor="#333333"
                android:textSize="16sp"
                android:padding="12dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/modern_card_bg" />

        </RadioGroup>
    </LinearLayout>

    <!-- 打开悬浮窗按钮 -->
    <Button
        android:id="@+id/btnOpenFloat"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="打开悬浮窗"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold"
        android:background="@drawable/modern_button_bg"
        android:layout_margin="20dp"
        android:elevation="4dp" />

</LinearLayout>
