<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#FFFFFF">

    <!-- 顶部导航栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:elevation="2dp"
        android:background="#FFFFFF">

        <Button
            android:id="@+id/btnBack"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:text="←"
            android:textSize="18sp"
            android:textColor="#2D3436"
            android:background="@android:color/transparent" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="游戏详情"
            android:textSize="18sp"
            android:textColor="#2D3436"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="56dp" />

    </LinearLayout>

    <!-- 主要内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp">

        <!-- 游戏图标 -->
        <androidx.cardview.widget.CardView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp">

            <ImageView
                android:id="@+id/imgGameLogo"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/xyjh"
                android:scaleType="centerCrop" />

        </androidx.cardview.widget.CardView>

        <!-- 游戏名称 -->
        <TextView
            android:id="@+id/txtGameName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="游戏名称"
            android:textSize="28sp"
            android:textColor="#2D3436"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <!-- 评分 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="⭐⭐⭐⭐⭐ 4.8"
            android:textSize="16sp"
            android:textColor="#636E72"
            android:layout_marginBottom="48dp" />

        <!-- 版本选择 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="选择版本"
            android:textSize="18sp"
            android:textColor="#2D3436"
            android:textStyle="bold"
            android:layout_marginBottom="20dp" />

        <RadioGroup
            android:id="@+id/radioGroupVersion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="40dp">

            <RadioButton
                android:id="@+id/radioE"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="E服版本"
                android:textSize="16sp"
                android:textColor="@color/radio_button_text_color"
                android:textStyle="bold"
                android:padding="20dp"
                android:checked="true"
                android:buttonTint="#74B9FF"
                android:background="@drawable/radio_button_bg"
                android:elevation="2dp" />

            <RadioButton
                android:id="@+id/radioH"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="工口服版本"
                android:textSize="16sp"
                android:textColor="@color/radio_button_text_color"
                android:textStyle="bold"
                android:padding="20dp"
                android:buttonTint="#74B9FF"
                android:background="@drawable/radio_button_bg"
                android:elevation="2dp" />

        </RadioGroup>

        <!-- 下载按钮 -->
        <Button
            android:id="@+id/btnDownload"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="立即下载"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/modern_button_bg"
            android:elevation="4dp" />

    </LinearLayout>

</LinearLayout>