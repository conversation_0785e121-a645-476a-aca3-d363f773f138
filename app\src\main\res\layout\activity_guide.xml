<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_background"
    android:clickable="true"
    android:focusable="true">

    <!-- 返回按钮 -->
    <Button
        android:id="@+id/btnBackGuideList"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="← 返回"
        android:textColor="#FFFFFF"
        android:background="@android:color/transparent"
        android:textSize="16sp"
        android:padding="16dp"
        android:layout_margin="16dp" />

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/modern_card_bg"
        android:padding="24dp"
        android:layout_margin="20dp"
        android:elevation="8dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="游戏攻略"
            android:textColor="#333333"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="选择游戏查看详细攻略"
            android:textColor="#666666"
            android:textSize="14sp"
            android:gravity="center" />

    </LinearLayout>

    <!-- 攻略按钮列表 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 星陨计划攻略 -->
            <Button
                android:id="@+id/btnGuideXYJH"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="星陨计划攻略"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/modern_button_bg"
                android:layout_marginBottom="12dp"
                android:elevation="4dp" />

            <!-- 天下布魔攻略 -->
            <Button
                android:id="@+id/btnGuideTXBM"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="天下布魔攻略"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/modern_button_bg"
                android:layout_marginBottom="12dp"
                android:elevation="4dp" />

            <!-- 贤者同盟攻略 -->
            <Button
                android:id="@+id/btnGuideXZTM"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="贤者同盟攻略"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/modern_button_bg"
                android:layout_marginBottom="12dp"
                android:elevation="4dp" />

            <!-- 潘吉亚异闻录攻略 -->
            <Button
                android:id="@+id/btnGuidePJYYWL"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="潘吉亚异闻录攻略"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/modern_button_bg"
                android:layout_marginBottom="12dp"
                android:elevation="4dp" />

            <!-- 樱井物语攻略 -->
            <Button
                android:id="@+id/btnGuideYJWT"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="樱井物语攻略"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/modern_button_bg"
                android:layout_marginBottom="12dp"
                android:elevation="4dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
