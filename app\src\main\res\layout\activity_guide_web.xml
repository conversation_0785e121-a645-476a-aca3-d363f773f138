<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_background">

    <!-- 顶部栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/modern_card_bg"
        android:padding="16dp"
        android:elevation="4dp"
        android:gravity="center_vertical">

        <!-- 返回按钮 -->
        <Button
            android:id="@+id/btnBackGuideWeb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="← 返回"
            android:textColor="#667eea"
            android:background="@android:color/transparent"
            android:textSize="16sp"
            android:textStyle="bold"
            android:padding="8dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="游戏攻略"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />

    </LinearLayout>

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progressBarGuide"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:progress="0"
        android:max="100"
        android:visibility="gone" />

    <!-- WebView -->
    <WebView
        android:id="@+id/webViewGuide"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>
