<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/banner_home">

    <!-- 首页内容 -->
    <ScrollView
        android:id="@+id/homeContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none"
        android:paddingBottom="90dp"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 顶部特色卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:elevation="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                xmlns:app="http://schemas.android.com/apk/res-auto">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="200dp">

                    <!-- 背景图片 -->
                    <ImageView
                        android:id="@+id/topCardImage"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/beijing1"
                        android:scaleType="centerCrop"
                        android:adjustViewBounds="true" />

                    <!-- 渐变遮罩 -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/gradient_overlay" />

                    <!-- 卡片内容 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="24dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🎮 Tohka辅助"
                            android:textSize="28sp"
                            android:textColor="#FFFFFF"
                            android:textStyle="bold"
                            android:shadowColor="#000000"
                            android:shadowDx="2"
                            android:shadowDy="2"
                            android:shadowRadius="6"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="专业游戏助手 · 极致体验"
                            android:textSize="16sp"
                            android:textColor="#FFFFFF"
                            android:shadowColor="#000000"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:layout_marginBottom="16dp" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⭐⭐⭐⭐⭐"
                                android:textSize="18sp"
                                android:layout_marginEnd="8dp"
                                android:layout_marginRight="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="5.0 完美评分"
                                android:textSize="14sp"
                                android:textColor="#FFFFFF"
                                android:shadowColor="#000000"
                                android:shadowDx="1"
                                android:shadowDy="1"
                                android:shadowRadius="2" />

                        </LinearLayout>

                    </LinearLayout>

                </FrameLayout>

            </androidx.cardview.widget.CardView>

            <!-- 欢迎区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="#FFFFFF"
                android:padding="24dp"
                android:layout_marginBottom="24dp"
                android:visibility="gone">
                
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="✨ 欢迎使用 Tohka"
                    android:textSize="24sp"
                    android:textColor="#2D3436"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />
                
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🌟 选择你喜欢的游戏开始体验"
                    android:textSize="16sp"
                    android:textColor="#636E72"
                    android:layout_marginBottom="16dp" />
                    
                <!-- 横幅图片容器 -->
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/rounded_corner_bg">

                    <ImageView
                        android:id="@+id/banner"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/beijing1"
                        android:scaleType="centerCrop"
                        android:adjustViewBounds="true" />

                    <!-- 渐变遮罩层 -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/gradient_overlay" />

                    <!-- 横幅文字 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🎮 TohkaPro"
                            android:textSize="24sp"
                            android:textColor="#FFFFFF"
                            android:textStyle="bold"
                            android:shadowColor="#000000"
                            android:shadowDx="2"
                            android:shadowDy="2"
                            android:shadowRadius="4" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="游戏助手"
                            android:textSize="14sp"
                            android:textColor="#FFFFFF"
                            android:layout_marginTop="4dp"
                            android:shadowColor="#000000"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="2" />

                    </LinearLayout>

                </FrameLayout>
                    
            </LinearLayout>

            <!-- 游戏列表标题 -->

            <!-- 游戏卡片列表 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 星陨计划 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="#FFFFFF"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical">
                    
                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/xyjh"
                        android:scaleType="centerCrop"
                        android:background="#EEEEEE" />
                        
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginLeft="16dp"
                        android:layout_marginStart="16dp">
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐ 星陨计划"
                            android:textSize="18sp"
                            android:textColor="#2D3436"
                            android:textStyle="bold" />
                            
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1.3.0.101981"
                            android:textSize="14sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />
                            
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐⭐⭐⭐⭐ 4.8"
                            android:textSize="12sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />
                            
                    </LinearLayout>
                    
                    <Button
                        android:id="@+id/btnDownloadXYJH"
                        android:layout_width="80dp"
                        android:layout_height="40dp"
                        android:text="下载"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:background="@drawable/light_purple_button_bg"
                        android:layout_marginLeft="12dp"
                        android:layout_marginStart="12dp" />
                        
                </LinearLayout>

                <!-- 天下布魔 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="#FFFFFF"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical">
                    
                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/txbm"
                        android:scaleType="centerCrop"
                        android:background="#EEEEEE" />
                        
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"

                        android:orientation="vertical"
                        android:layout_marginLeft="16dp"
                        android:layout_marginStart="16dp">
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🔮 天下布魔"
                            android:textSize="18sp"
                            android:textColor="#2D3436"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1.25.0"
                            android:layout_marginTop="4dp"
                            android:textColor="#636E72"
                            android:textSize="14sp" />
                            
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐⭐⭐⭐⭐ 4.7"
                            android:textSize="12sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />
                            
                    </LinearLayout>
                    
                    <Button
                        android:id="@+id/btnDownloadTXBM"
                        android:layout_width="80dp"
                        android:layout_height="40dp"
                        android:text="下载"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:background="@drawable/light_purple_button_bg"
                        android:layout_marginLeft="12dp"
                        android:layout_marginStart="12dp" />
                        
                </LinearLayout>

                <!-- 潘吉亚异闻录 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="#FFFFFF"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical">
                    
                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/pjyywl"
                        android:scaleType="centerCrop"
                        android:background="#EEEEEE" />
                        
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginLeft="16dp"
                        android:layout_marginStart="16dp">
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🌟 潘吉亚异闻录"
                            android:textSize="18sp"
                            android:textColor="#2D3436"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="999"
                            android:layout_marginTop="4dp"
                            android:textColor="#636E72"
                            android:textSize="14sp" />
                            
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐⭐⭐⭐⭐ 4.6"
                            android:textSize="12sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />
                            
                    </LinearLayout>
                    
                    <Button
                        android:id="@+id/btnDownloadPJYYWL"
                        android:layout_width="80dp"
                        android:layout_height="40dp"
                        android:text="下载"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:background="@drawable/light_purple_button_bg"
                        android:layout_marginLeft="12dp"
                        android:layout_marginStart="12dp" />
                        
                </LinearLayout>

                <!-- 贤者同盟 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="#FFFFFF"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical">
                    
                    <ImageView
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:src="@drawable/xztm"
                        android:scaleType="centerCrop"
                        android:background="#EEEEEE" />
                        
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginLeft="16dp"
                        android:layout_marginStart="16dp">
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⚡ 贤者同盟"
                            android:textSize="18sp"
                            android:textColor="#2D3436"
                            android:textStyle="bold" />
                            
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3.6.0"
                            android:textSize="14sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />
                            
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐⭐⭐⭐⭐ 4.9"
                            android:textSize="12sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />
                            
                    </LinearLayout>
                    
                    <Button
                        android:id="@+id/btnDownloadXZTM"
                        android:layout_width="80dp"
                        android:layout_height="40dp"
                        android:text="下载"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:background="@drawable/light_purple_button_bg"
                        android:layout_marginLeft="12dp"
                        android:layout_marginStart="12dp" />
                        
                </LinearLayout>

            </LinearLayout>

            <!-- 樱井物语 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="#FFFFFF"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:background="#EEEEEE"
                    android:scaleType="centerCrop"
                    android:src="@drawable/yjwy" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginLeft="16dp"
                    android:layout_marginStart="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🌸 樱井物语"
                        android:textSize="18sp"
                        android:textColor="#2D3436"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1.7.0"
                        android:textSize="14sp"
                        android:textColor="#636E72"
                        android:layout_marginTop="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⭐⭐⭐⭐⭐ 4.9"
                        android:textSize="12sp"
                        android:textColor="#636E72"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <Button
                    android:id="@+id/btnDownloadYJWT"
                    android:layout_width="80dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginLeft="16dp"
                    android:background="@drawable/light_purple_button_bg"
                    android:text="下载"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <!-- 发卡网页面 -->
    <LinearLayout
        android:id="@+id/cardContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        android:paddingBottom="90dp"
        android:clipToPadding="false">

        <WebView
            android:id="@+id/webViewCard"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

    <!-- 作者主页 -->
    <ScrollView
        android:id="@+id/guideContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="#FFFFFF"
        android:fillViewport="true"
        android:paddingBottom="90dp"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 作者头像和基本信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="32dp">

                <ImageView
                    android:id="@+id/authorAvatar"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_launcher"
                    android:background="@drawable/rounded_corner_bg"
                    android:scaleType="centerCrop"
                    android:layout_marginEnd="20dp"
                    android:layout_marginRight="20dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/authorName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="十香"
                        android:textSize="24sp"
                        android:textColor="#2D3436"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/authorTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Android开发者 | 游戏辅助制作者"
                        android:textSize="14sp"
                        android:textColor="#636E72"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/authorStats"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="⭐ 1.2K 关注者 | 📱 5+ 项目"
                        android:textSize="12sp"
                        android:textColor="#74B9FF" />

                </LinearLayout>

            </LinearLayout>

            <!-- 个人简介 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/float_card_bg"
                android:padding="20dp"
                android:layout_marginBottom="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝 个人简介"
                    android:textSize="18sp"
                    android:textColor="#2D3436"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/authorBio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="专注于Android应用开发和游戏辅助工具制作。致力于为用户提供优质的游戏体验和实用的工具软件。拥有999年的移动端开发经验，熟悉Java、Kotlin、GGLua等编程语言。
                    \n定制游戏辅助请查看下方联系方式。"
                    android:textSize="14sp"
                    android:textColor="#636E72"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

            <!-- 联系方式 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/float_card_bg"
                android:padding="20dp"
                android:layout_marginBottom="24dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📞 联系方式"
                    android:textSize="18sp"
                    android:textColor="#2D3436"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- QQ -->
                <LinearLayout
                    android:id="@+id/contactQQ"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="12dp"
                    android:background="@drawable/light_purple_button_bg"
                    android:layout_marginBottom="12dp"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🐧"
                        android:textSize="20sp"
                        android:layout_marginEnd="12dp"
                        android:layout_marginRight="12dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="QQ: 2647103221"
                        android:textSize="16sp"
                        android:textColor="#FFFFFF" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="复制"
                        android:textSize="14sp"
                        android:textColor="#FFFFFF"
                        android:alpha="0.8" />

                </LinearLayout>

                <!-- 微信 -->
                <LinearLayout
                    android:id="@+id/contactWeChat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="12dp"
                    android:background="@drawable/light_purple_button_bg"
                    android:layout_marginBottom="12dp"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="💬"
                        android:textSize="20sp"
                        android:layout_marginEnd="12dp"
                        android:layout_marginRight="12dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="微信: HeQuanFeiAi0v0"
                        android:textSize="16sp"
                        android:textColor="#FFFFFF" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="复制"
                        android:textSize="14sp"
                        android:textColor="#FFFFFF"
                        android:alpha="0.8" />

                </LinearLayout>

                <!-- 邮箱 -->
                <LinearLayout
                    android:id="@+id/contactEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="12dp"
                    android:background="@drawable/light_purple_button_bg"
                    android:layout_marginBottom="12dp"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📧"
                        android:textSize="20sp"
                        android:layout_marginEnd="12dp"
                        android:layout_marginRight="12dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="邮箱: <EMAIL>"
                        android:textSize="16sp"
                        android:textColor="#FFFFFF" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="复制"
                        android:textSize="14sp"
                        android:textColor="#FFFFFF"
                        android:alpha="0.8" />

                </LinearLayout>


                <!-- B站 -->
                <LinearLayout
                    android:id="@+id/contactBilibili"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="12dp"
                    android:background="@drawable/light_purple_button_bg"
                    android:layout_marginBottom="12dp"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🖥️"
                        android:textSize="20sp"
                        android:layout_marginEnd="12dp"
                        android:layout_marginRight="12dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="B站: MY-血-233"
                        android:textSize="16sp"
                        android:textColor="#FFFFFF" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="复制"
                        android:textSize="14sp"
                        android:textColor="#FFFFFF"
                        android:alpha="0.8" />

                </LinearLayout>

            </LinearLayout>

            <!-- 项目展示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/float_card_bg"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🚀 项目展示——当前权限仅展示部分项目"
                    android:textSize="18sp"
                    android:textColor="#2D3436"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <!-- 项目1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="12dp"
                    android:background="#F8F9FA"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🎮"
                        android:textSize="24sp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Kurumi"
                            android:textSize="16sp"
                            android:textColor="#2D3436"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="多游戏辅助工具"
                            android:textSize="12sp"
                            android:textColor="#636E72" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="v1.0"
                        android:textSize="12sp"
                        android:textColor="#74B9FF"
                        android:background="#E3F2FD"
                        android:padding="4dp" />

                </LinearLayout>

                <!-- 项目2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="12dp"
                    android:background="#F8F9FA">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="🛠️"
                        android:textSize="24sp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="更多项目"
                            android:textSize="16sp"
                            android:textColor="#2D3436"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="已隐藏..."
                            android:textSize="12sp"
                            android:textColor="#636E72" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Soon"
                        android:textSize="12sp"
                        android:textColor="#FD79A8"
                        android:background="#FCE4EC"
                        android:padding="4dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- 悬浮窗选择页面 -->
    <ScrollView
        android:id="@+id/floatContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="#FFFFFF"
        android:paddingBottom="90dp"
        android:clipToPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 标题区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginBottom="32dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎮 选择悬浮窗"
                    android:textSize="28sp"
                    android:textColor="#2D3436"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="选择并打开游戏辅助"
                    android:textSize="14sp"
                    android:textColor="#636E72"
                    android:gravity="center" />

            </LinearLayout>

            <!-- 悬浮窗选择组 -->
            <RadioGroup
                android:id="@+id/radioGroupFloat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 星陨计划悬浮窗 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/float_card_bg"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="20dp"
                    android:elevation="4dp">

                    <RadioButton
                        android:id="@+id/btnFloatXYJH"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="#74B9FF"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/xyjh"
                        android:scaleType="centerCrop"
                        android:background="@drawable/rounded_corner_bg"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐ 星陨计划"
                            android:textSize="18sp"
                            android:textColor="@color/float_card_text_color"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- 天下布魔悬浮窗 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/float_card_bg"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="20dp"
                    android:elevation="4dp">

                    <RadioButton
                        android:id="@+id/btnFloatTXBM"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="#74B9FF"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/txbm"
                        android:scaleType="centerCrop"
                        android:background="@drawable/rounded_corner_bg"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⚔️ 天下布魔"
                            android:textSize="18sp"
                            android:textColor="@color/float_card_text_color"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- 潘吉亚异闻录悬浮窗 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/float_card_bg"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="20dp"
                    android:elevation="4dp">

                    <RadioButton
                        android:id="@+id/btnFloatPJYYWL"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="#74B9FF"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/pjyywl"
                        android:scaleType="centerCrop"
                        android:background="@drawable/rounded_corner_bg"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🗺️ 潘吉亚异闻录"
                            android:textSize="18sp"
                            android:textColor="@color/float_card_text_color"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- 贤者同盟悬浮窗 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/float_card_bg"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="20dp"
                    android:elevation="4dp">

                    <RadioButton
                        android:id="@+id/btnFloatXZTM"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="#74B9FF"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/xztm"
                        android:scaleType="centerCrop"
                        android:background="@drawable/rounded_corner_bg"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🃏 贤者同盟"
                            android:textSize="18sp"
                            android:textColor="@color/float_card_text_color"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- 樱井物语悬浮窗 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="32dp"
                    android:background="@drawable/float_card_bg"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="20dp"
                    android:elevation="4dp">

                    <RadioButton
                        android:id="@+id/btnFloatYJWT"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="#74B9FF"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/yjwy"
                        android:scaleType="centerCrop"
                        android:background="@drawable/rounded_corner_bg"
                        android:layout_marginEnd="16dp"
                        android:layout_marginRight="16dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🌸 樱井物语"
                            android:textSize="18sp"
                            android:textColor="@color/float_card_text_color"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=""
                            android:textSize="14sp"
                            android:textColor="#636E72"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </LinearLayout>

            </RadioGroup>

            <!-- 开启悬浮窗按钮 -->
            <Button
                android:id="@+id/btnStartFloat"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="32dp"
                android:text="开启悬浮窗"
                android:textColor="#FFFFFF"
                android:textSize="18sp"
                android:textStyle="bold"
                android:background="@drawable/light_purple_button_bg"
                android:elevation="4dp" />

        </LinearLayout>

    </ScrollView>

    <!-- 简化的底部导航栏 -->
    <LinearLayout
        android:id="@+id/bottomNav"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:background="@drawable/bottom_nav_rounded_bg"
        android:elevation="8dp"
        android:padding="8dp">
        
        <Button
            android:id="@+id/navHome"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="首页"
            android:textColor="#FFFFFF"
            android:textSize="11sp"
            android:textStyle="bold"
            android:background="@drawable/nav_button_bg"
            android:layout_margin="4dp" />
            
        <Button
            android:id="@+id/navCard"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="发卡网"
            android:textColor="#636E72"
            android:textSize="11sp"
            android:background="@drawable/nav_button_transparent_bg"
            android:layout_margin="4dp" />

        <Button
            android:id="@+id/navFloat"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="悬浮窗"
            android:textColor="#636E72"
            android:textSize="11sp"
            android:background="@drawable/nav_button_transparent_bg"
            android:layout_margin="4dp" />

        <Button
            android:id="@+id/navGuide"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="作者"
            android:textColor="#636E72"
            android:textSize="11sp"
            android:background="@drawable/nav_button_transparent_bg"
            android:layout_margin="4dp" />
            
    </LinearLayout>

</FrameLayout>
