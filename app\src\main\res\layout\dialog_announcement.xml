<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/announcement_bg"
    android:padding="0dp">

    <!-- 顶部装饰图片 -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:src="@drawable/announcement_header"
        android:scaleType="centerCrop"
        android:background="@drawable/announcement_header_bg" />

    <!-- 公告内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:background="#FFFFFF">

        <!-- 公告标题 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_announcement"
                android:layout_marginEnd="8dp"
                android:layout_marginRight="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="📢 系统公告"
                android:textSize="20sp"
                android:textColor="#2D3436"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvAnnouncementTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2024-01-01"
                android:textSize="12sp"
                android:textColor="#636E72"
                android:background="#F8F9FA"
                android:padding="4dp"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp" />

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0"
            android:layout_marginBottom="16dp" />

        <!-- 公告内容滚动区域 -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="300dp"
            android:scrollbars="vertical"
            android:fadeScrollbars="false">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 公告正文 -->
                <TextView
                    android:id="@+id/tvAnnouncementContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="正在加载公告内容..."
                    android:textSize="14sp"
                    android:textColor="#2D3436"
                    android:lineSpacingExtra="4dp"
                    android:layout_marginBottom="16dp" />

                <!-- 重要提示区域 -->
                <LinearLayout
                    android:id="@+id/layoutImportantTip"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="#FFF3E0"
                    android:padding="12dp"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_warning"
                        android:layout_marginEnd="8dp"
                        android:layout_marginRight="8dp" />

                    <TextView
                        android:id="@+id/tvImportantTip"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="重要提示内容"
                        android:textSize="12sp"
                        android:textColor="#F57C00" />

                </LinearLayout>

                <!-- 版本信息 -->
                <LinearLayout
                    android:id="@+id/layoutVersionInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="#E8F5E8"
                    android:padding="12dp"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_update"
                        android:layout_marginEnd="8dp"
                        android:layout_marginRight="8dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="最新版本"
                            android:textSize="12sp"
                            android:textColor="#4CAF50"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvVersionInfo"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="v1.0.0"
                            android:textSize="11sp"
                            android:textColor="#4CAF50" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

        <!-- 底部按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <!-- 不再显示按钮 -->
            <Button
                android:id="@+id/btnDontShowAgain"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="1"
                android:text="不再显示"
                android:textColor="#636E72"
                android:textSize="14sp"
                android:background="@drawable/button_outline_bg"
                android:layout_marginEnd="8dp"
                android:layout_marginRight="8dp" />

            <!-- 我知道了按钮 -->
            <Button
                android:id="@+id/btnGotIt"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="1"
                android:text="我知道了"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:background="@drawable/light_purple_button_bg"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
