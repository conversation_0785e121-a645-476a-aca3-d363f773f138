buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
