API接口文档
请求方式: POST

接口地址: https://wy.llua.cn/v2/{请求令牌}

接口描述: v2接口是一个高度自定义化的api接口，可以自定义接口请求参数加密算法和响应数据加密算法及自定义多check校验规则

调用方式: 使用POST参数提交格式拼接好参数后，使用后台设置的加密算法将加密后的数据使用POST提交，API返回数据使用全局配置（单码登录、远程变量、心跳验证 可使用自定义响应加密算法）加密算法对数据进行解密即可得到返回数据


#单码登录
请求参数

参数	类型	必填	说明
id	String	是	调用ID
kami	String	是	卡密
markcode	String	是	设备码
t	String	是	十位整数时间戳
sign	String	是	《数据签名》
value	String	是	随机数
返回数据(自定义响应[data]变量)

参数	类型	说明
code	Number	《登录状态码》
msg	String/Array	错误信息或登录数据
msg.id	Number	卡密ID
msg.kmtype	String	
《卡密时长类型》

msg.token	String	登录令牌(用于心跳)
msg.note	String	卡密备注
msg.ktype	String	卡密类型(单码:code，次数卡:single)
msg.vip	Number	到期时间戳
time	Number	当前时间戳
check	String	数据校验(后台可自定义校验规则)
伪代码

host="https://wy.llua.cn/v2/"
appkey="程序秘钥"
id="调用ID"
apitoken="请求令牌"

sign = md5("kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&" + appkey)

postdata = 自定义算法加密("id=" + id + "&kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&sign=" + sign + "&value=" + 随机数)

data = POST请求(host + apitoken,postdata)

print(自定义算法解密(data))
//可根据返回数据编写校验规则和登录状态，如使用check校验(后台可自定义校验规则) 

#单码解绑
请求参数

参数	类型	必填	说明
id	String	是	调用ID
kami	String	是	卡密
markcode	String	是	设备码
t	String	是	十位整数时间戳
sign	String	是	《数据签名》
value	String	是	随机数
返回数据

参数	类型	说明
code	Number	状态码
msg	String/Array	错误信息或返回数据
msg.num	Number	剩余可解绑次数
time	Number	当前时间戳
伪代码

host="https://wy.llua.cn/v2/"
appkey="程序秘钥"
id="调用ID"
apitoken="请求令牌"

sign = md5("kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&" + appkey)

postdata = 自定义算法加密("id=" + id + "&kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&sign=" + sign + "&value=" + 随机数)

data = POST请求(host + apitoken,postdata)

print(自定义算法解密(data))
//解密API接口返回数据即可得到JSON数据

#获取公告
请求参数

参数	类型	必填	说明
id	String	是	调用ID
返回数据

参数	类型	说明
code	Number	状态码
msg/Array	String	错误信息或返回数据
msg.app_gg	String	公告内容
time	Number	当前时间戳
伪代码

host="https://wy.llua.cn/v2/"
appkey="程序秘钥"
id="调用ID"
apitoken="请求令牌"

postdata = 自定义算法加密("id=" + id)

data = POST请求(host + apitoken,postdata)

print(自定义算法解密(data))
//解密API接口返回数据即可得到JSON数据

#获取更新
请求参数

参数	类型	必填	说明
id	String	是	调用ID
返回数据

参数	类型	说明
code	Number	状态码
msg/Array	String	错误信息或返回数据
msg.version	String	最新版本版本号
msg.updateshow	String	更新内容
msg.updateurl	String	更新地址
msg.updatemust	String	是否强制更新(是:y，否:n)
time	Number	当前时间戳
伪代码

host="https://wy.llua.cn/v2/"
appkey="程序秘钥"
id="调用ID"
apitoken="请求令牌"

postdata = 自定义算法加密("id=" + id)

data = POST请求(host + apitoken,postdata)

print(自定义算法解密(data))
//解密API接口返回数据即可得到JSON数据

#远程变量
请求参数

参数	类型	必填	说明
id	String	是	调用ID
kami	String	是	卡密
markcode	String	是	设备码
t	String	是	十位整数时间戳
sign	String	是	《数据签名》
value	String	是	随机数
{自定义}	String	是	变量名
返回数据(自定义响应[data]变量)

参数	类型	说明
code	Number	状态码
msg/Array	String	错误信息或返回数据
msg.msg	String	变量值
msg.check	String	数据校验(后台可自定义校验规则)
time	Number	当前时间戳
伪代码

host="https://wy.llua.cn/v2/"
appkey="程序秘钥" 
id="调用ID"
apitoken="请求令牌"
key="变量名称"

sign = md5("kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&{后台自定义}=" + 变量名 + "&" + appkey)

postdata = 自定义算法加密("id=" + id + "&kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&sign=" + sign + "&value=" + 随机数 + "&{后台自定义（请求变量名称）}=" + key)

data = POST请求(host + apitoken,postdata)

print(自定义算法解密(data))
//解密API接口返回数据即可得到JSON数据获取变量值，可使用check校验对数据进行校验(后台可自定义校验规则)

#心跳验证
说明:建议10-60秒请求一次，请勿设置过短的请求间隔，否则可能会被拉黑，Token默认过期时间为120秒(可自定义)
请求参数

参数	类型	必填	说明
id	String	是	调用ID
kami	String	是	卡密
markcode	String	是	设备码
t	String	是	十位整数时间戳
sign	String	是	《数据签名》
kamitoken	String	是	登录Token
value	String	是	随机数
返回数据(自定义响应[data]变量)

参数	类型	说明
code	Number	状态码
msg/Array	String	错误信息或登录数据
msg.endtime	Number	到期时间
msg.type	String	卡密类型(单码:code，次数卡:single)
msg.timetype	String	
《卡密时长类型》

msg.check	String	数据校验(后台可自定义校验规则)
time	Number	当前时间戳
伪代码

host="https://wy.llua.cn/v2/"
appkey="程序秘钥"
id="调用ID"
apitoken="请求令牌"
kamitoken="登录接口返回数据{ msg.token }"

sign = md5("kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&kamitoken=" + kamitoken +  "&" + appkey)

postdata = 自定义算法加密("id=" + id + "&kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&sign=" + sign + "&kamitoken=" + kamitoken + "&value=" + 随机数)

data = POST请求(host + apitoken,postdata)

print(自定义算法解密(data))
//可根据返回数据编写校验规则和登录状态，如使用check校验(后台可自定义校验规则)

#sign签名算法（登录接口）
计算公式

sign = md5("kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&" + APPKEY)

#sign签名算法（心跳接口2.0）
计算公式

sign = md5("kami=" + 卡密 + "&markcode=" + 设备码 + "&t=" + 当前时间戳(十位整数) + "&kamitoken=" + 登录Token + "&" + APPKEY)

#check签名算法
计算公式

如后台未自定义校验规则，系统默认规则如下

check = md5(Time(服务器时间戳) + APPKEY + 提交value变量的随机数)

部分编程语言需要注意数据类型转换，如time变量如是浮点数可能会导致md5不一致，应强制转换为整数或字符串类型

#卡密时长类型
参数	说明
free	免费模式
hour	时卡
day	天卡
week	周卡
month	月卡
season	季卡
year	年卡
longuse	永久卡
single	次数卡

#登录状态码
状态码	说明
200(默认,可自定义)	登录成功
201	[取msg变量信息]
100	未绑定应用ID
-1	应用不存在
102	应用已关闭
104	签名为空
105	数据过期
106	签名有误
107	数据为空
108	未提交时间变量
112	未提交设备码变量
148	卡密为空
149	卡密不存在
150	卡密已使用
152	卡密已到期
153	卡密已被禁用

#使用技巧
一、设置到期自动退出

方法一(推荐):写一个定时器或延迟执行方法，使用 到期时间戳 减 当前时间戳 得到的就是剩余秒数

方法二:使用心跳验证


二、设置部分功能权限

获取登录数据后，可以根据msg.kmtype参数判断卡密类型，例如限制某功能仅限永久卡使用，卡密类型参数详见《卡密时长类型》