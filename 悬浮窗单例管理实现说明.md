# 悬浮窗单例管理实现说明

## 问题描述
项目调用悬浮窗可以调用无限次，导致可以出现无限个悬浮窗，需要实现一个游戏的悬浮窗只能存在一个。

## 解决方案
实现了 `AlguiFloatWindowManager` 悬浮窗管理器，采用单例模式来控制所有游戏的悬浮窗，确保每个游戏的悬浮窗只能存在一个。

## 核心实现

### 1. 悬浮窗管理器 (AlguiFloatWindowManager.java)

#### 单例模式设计
```java
public class AlguiFloatWindowManager {
    // 单例实例
    private static AlguiFloatWindowManager instance;
    
    // 当前活跃的悬浮窗映射表 <游戏名称, 悬浮窗实例>
    private Map<String, AlguiWinMenu> activeFloatWindows;
    
    // 当前活跃的游戏名称
    private String currentActiveGame;
    
    // 私有构造函数，防止外部实例化
    private AlguiFloatWindowManager() {
        activeFloatWindows = new HashMap<>();
        currentActiveGame = null;
    }
    
    // 获取单例实例
    public static synchronized AlguiFloatWindowManager getInstance() {
        if (instance == null) {
            instance = new AlguiFloatWindowManager();
        }
        return instance;
    }
}
```

#### 主要功能方法

**启动悬浮窗**
```java
public boolean startFloatWindow(Context context, String gameName)
```
- 检查是否已有相同游戏的悬浮窗在运行
- 如果当前有其他游戏的悬浮窗在运行，先关闭它
- 根据游戏名称启动对应的悬浮窗

**通过Service启动悬浮窗**
```java
public boolean startFloatWindowViaService(Context context, String gameName)
```
- 通过Service启动悬浮窗，适用于全局悬浮窗
- 自动管理悬浮窗的生命周期

**关闭悬浮窗**
```java
public boolean closeFloatWindow(String gameName)
public boolean closeAllFloatWindows()
```
- 关闭指定游戏的悬浮窗
- 关闭所有悬浮窗

**状态检查**
```java
public boolean isFloatWindowRunning(String gameName)
public boolean hasAnyFloatWindowRunning()
public String getCurrentActiveGame()
```
- 检查指定游戏的悬浮窗是否正在运行
- 检查是否有任何悬浮窗正在运行
- 获取当前活跃的游戏名称

**注册/注销悬浮窗**
```java
public void registerFloatWindow(String gameName, AlguiWinMenu window)
public void unregisterFloatWindow(String gameName)
```
- 注册悬浮窗实例到管理器
- 注销悬浮窗实例

### 2. 修改的文件列表

#### MainActivity.java
- 修改 `openFloatWindow()` 方法，使用悬浮窗管理器
- 添加确认对话框，当有其他悬浮窗运行时询问是否切换
- 检查重复启动，避免同一游戏多次启动悬浮窗

#### AlguiService.java
- 修改 `onStartCommand()` 方法，使用悬浮窗管理器
- 添加重复检查和自动关闭逻辑
- 注册悬浮窗到管理器

#### FloatSelectionActivity.java
- 修改悬浮窗启动逻辑，使用悬浮窗管理器
- 添加确认对话框和状态检查

#### 各个游戏悬浮窗类
- **Game2FloatWindow.java**: 在 `MyMenu()` 方法中注册悬浮窗
- **Game3FloatWindow.java**: 在 `MyMenu()` 方法中注册悬浮窗
- **Game4FloatWindow.java**: 在 `MyMenu()` 方法中注册悬浮窗
- **Game5FloatWindow.java**: 在 `MyMenu()` 方法中注册悬浮窗
- **Main.java**: 在 `MyMenu()` 方法中注册悬浮窗

### 3. 注册机制

每个悬浮窗在创建时都会注册到管理器中：

```java
// 注册悬浮窗到管理器
AlguiFloatWindowManager.getInstance().registerFloatWindow("游戏名称", menu);

// 设置菜单打开关闭回调，用于注销悬浮窗
menu.setCatMenuOpenCallback(new AlguiCallback.Click() {
    @Override
    public void click(boolean isChecked) {
        if (!isChecked) {
            // 菜单关闭时注销悬浮窗
            AlguiFloatWindowManager.getInstance().unregisterFloatWindow("游戏名称");
        }
    }
});
```

### 4. 工作流程

#### 启动悬浮窗流程
1. 用户选择游戏并点击启动悬浮窗
2. 检查是否已有相同游戏的悬浮窗在运行
   - 如果有，提示用户并阻止重复启动
3. 检查是否有其他游戏的悬浮窗在运行
   - 如果有，显示确认对话框询问是否切换
4. 启动新的悬浮窗并注册到管理器
5. 更新当前活跃游戏标记

#### 关闭悬浮窗流程
1. 用户关闭悬浮窗或切换游戏
2. 自动注销悬浮窗实例
3. 清空相关状态标记
4. 释放资源

### 5. 安全机制

#### 重复启动防护
```java
if (manager.isFloatWindowRunning(gameName)) {
    Toast.makeText(this, gameName + "悬浮窗已在运行", Toast.LENGTH_SHORT).show();
    return;
}
```

#### 自动切换确认
```java
if (manager.hasAnyFloatWindowRunning()) {
    String currentGame = manager.getCurrentActiveGame();
    if (currentGame != null && !currentGame.equals(gameName)) {
        // 显示确认对话框
        new AlertDialog.Builder(this)
            .setTitle("确认切换悬浮窗")
            .setMessage("当前已有" + currentGame + "悬浮窗在运行，是否要切换到" + gameName + "？")
            .setPositiveButton("确定", (dialog, which) -> {
                // 执行切换逻辑
            })
            .setNegativeButton("取消", null)
            .show();
        return;
    }
}
```

#### 资源自动释放
```java
menu.setCatMenuOpenCallback(new AlguiCallback.Click() {
    @Override
    public void click(boolean isChecked) {
        if (!isChecked) {
            // 菜单关闭时自动注销悬浮窗
            AlguiFloatWindowManager.getInstance().unregisterFloatWindow("游戏名称");
        }
    }
});
```

### 6. 日志记录

管理器会记录所有关键操作：
- 悬浮窗启动/关闭
- 状态变化
- 错误处理
- 资源管理

### 7. 优势特点

1. **单例保证**: 每个游戏只能有一个悬浮窗实例
2. **自动管理**: 自动处理悬浮窗的生命周期
3. **用户友好**: 提供确认对话框和状态提示
4. **资源安全**: 自动释放资源，避免内存泄漏
5. **扩展性强**: 易于添加新的游戏支持
6. **日志完整**: 详细的操作日志便于调试

### 8. 使用示例

```java
// 获取管理器实例
AlguiFloatWindowManager manager = AlguiFloatWindowManager.getInstance();

// 启动悬浮窗
boolean success = manager.startFloatWindowViaService(context, "天下布魔");

// 检查状态
if (manager.isFloatWindowRunning("天下布魔")) {
    // 悬浮窗正在运行
}

// 关闭悬浮窗
manager.closeFloatWindow("天下布魔");

// 关闭所有悬浮窗
manager.closeAllFloatWindows();
```

## 总结

通过实现 `AlguiFloatWindowManager` 悬浮窗管理器，成功解决了悬浮窗重复启动的问题。现在每个游戏的悬浮窗只能存在一个，系统会自动管理悬浮窗的生命周期，提供良好的用户体验。

主要特性：
- ✅ 单例模式保证唯一性
- ✅ 自动资源管理
- ✅ 用户友好的交互
- ✅ 完整的日志记录
- ✅ 安全的错误处理
- ✅ 易于维护和扩展 